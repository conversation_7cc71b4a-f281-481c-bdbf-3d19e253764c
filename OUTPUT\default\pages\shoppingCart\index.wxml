<view id="bh_shop_cart">
    <jd-address bind:parentEvent="areaReady" jdsku="1" wx:if="{{hasLogin}}"></jd-address>
    <view class="login-tip-container" wx:else>
        <text class="login-tip-text">登录后下单更方便哦</text>
        <view bindtap="goToLogin" class="login-btn">登录</view>
    </view>
    <view class="hd_city" hidden="{{status_goods_is_empty_div!=0}}">
        <view class="city_cur">百货门店：{{store_name}}</view>
        <view catchtap="showChangeStoreModal" class="city_cho" wx:if="{{storeList.length>0}}">
            <image class="city_icon" src="https://obs.springland.com.cn/obs-179f/huadi2/2020/09/15995285723076.png"></image>切换门店</view>
    </view>
    <view class="shop_none" hidden="{{status_goods_is_empty_div!=1||checkoutProductCs.hasData}}">
        <image class="shop_bg" mode="widthFix" src="../../img/empty_cart.png"></image>
        <view class="shop_text">哎呀，您的购物车还没有商品哦～</view>
        <button catchtap="goHome" class="goHome">逛一逛</button>
    </view>
    <view class="container" hidden="{{status_goods_is_empty_div!=0}}">
        <view class="head">
            <view class="hd_num">共{{count_cart_goods}}件商品</view>
            <view catchtap="complete_edit_goods" class="hd_rt hd_rt_ok" hidden="{{status_is_click_manage==0}}">完成</view>
            <view catchtap="manage_goods" class="hd_rt" hidden="{{status_is_click_manage!=0}}">
                <image class="hd_icon" src="/yjh-config/images/shop_icon02.png"></image>管理 </view>
        </view>
        <view class="goods">
            <view wx:for="{{goods_valid}}" wx:for-index="key" wx:for-item="shops" wx:key="key1">
                <view class="invalid_title">
                    <view class="goods_checkbox_all">
                        <label>
                            <checkbox catchtap="selectShopStatus" checked="{{shops.status==1?true:false}}" data-dept_id="{{key}}" data-select_status="{{shops.status}}"></checkbox>
                        </label>
                    </view>
                    <navigator hoverClass="none" style="width: 350rpx;float: left;overflow: hidden;white-space: nowrap;" url="{{filter.isJxCart(key)?'/jx':'/home'}}/pages/counter-detail/index?dept_id={{shops.dept_id}}">
                        <image class="goods_icon" src="/yjh-config/images/good_shop.png"></image> {{shops.name}} </navigator>
                    <view class="post_explain">{{shops.post_explain==null?'':shops.post_explain}}</view>
                    <view catchtap="showCouponModel" class="goods_ticket" data-dept_id="{{key}}" wx:if="{{shops.have_coupn}}">领券</view>
                </view>
                <view class="goods_act" wx:for="{{shops.fullMinus}}" wx:for-item="fullMinu" wx:key="fullminuxids">
                    <view class="goods_act_lt">
                        <text class="goods_act_text">满减</text>{{fullMinu.full_info}}</view>
                    <navigator hoverClass="none" url="{{filter.isJxCart(key)?'/jx':'/home'}}/pages/activeReduce/index?full_id={{fullMinu.full_id}}">
                        <view class="goods_act_rt">去凑单</view>
                    </navigator>
                </view>
                <view class="goods_act" wx:for="{{shops.shipping}}" wx:for-item="shipping" wx:key="shipping">
                    <view class="goods_act_lt">
                        <text class="goods_act_text">包邮</text>{{shipping.shipping_info}}</view>
                    <navigator hoverClass="none" url="{{filter.isJxCart(key)?'/jx':'/home'}}/pages/activeShipping/index?active_id={{shipping.shipping_id}}">
                        <view class="goods_act_rt">去凑单</view>
                    </navigator>
                </view>
                <view class="goods_act" wx:for="{{shops.discountS}}" wx:for-item="discount" wx:key="disocunts">
                    <view class="goods_act_lt">
                        <text class="goods_act_text">折扣</text>{{discount.discount_info}}</view>
                    <navigator hoverClass="none" url="{{filter.isJxCart(key)?'/jx':'/home'}}/pages/activeDiscount/index?discount_id={{discount.discount_id}}">
                        <view class="goods_act_rt">去凑单</view>
                    </navigator>
                </view>
                <checkbox-group bindchange="selectShopGoodsStatus" data-dept_id="{{key}}" data-goods_num="{{shops.goods.length}}">
                    <view class="list" wx:for="{{shops.goods}}" wx:key="index">
                        <view class="goods_list txt" id="goods_div_{{item.detail_id}}" style="{{item.left_value}}">
                            <view class="goods_checkbox">
                                <label>
                                    <checkbox checked="{{item.status==1?true:false}}" value="{{index}}"></checkbox>
                                </label>
                            </view>
                            <navigator bindtouchend="touchE" bindtouchmove="touchM" bindtouchstart="touchS" data-arary_index="{{index}}" data-dept_id="{{key}}" data-index="{{item.detail_id}}" hoverClass="none" url="{{filter.isJxCart(key)?'/jx':'/home'}}/pages/goods-detail/index?goods_detail_id={{item.detail_id}}">
                                <view class="posi-rea goods_img">
                                    <image class="goods_img" src="{{item.cover_img}}"></image>
                                    <image class="goods_img back_img" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                                    <activity_frame addClass="goods_img" storeId="{{item.store_id}}"></activity_frame>
                                    <image class="goods_img no_stock" hidden="{{jdNoStockGoods[item.detail_id]!=1}}" src="../../img/nostock.png"></image>
                                </view>
                                <view class="goods_title">
                                    <text hidden="{{item.is_prime!=1}}">
                                        <text class="goods_title_tip">Prime专享</text>
                                    </text> {{item.name}}</view>
                                <view class="goods_text" hidden="{{item.add_price<=0}}">比加入时降 ¥ {{item.add_price}}</view>
                                <view class="goods_subtitle">¥<text>{{item.price}}</text>
                                </view>
                                <view catchtap="zuzhi" class="goods_choose">
                                    <view catchtap="changeGoodsNum" class="goods_add" data-arary_index="{{index}}" data-cart_id="{{item.cart_id}}" data-dept_id="{{key}}" data-operator="reduce_num">
                                        <image class="" src="/yjh-config/images/icon_reduce.png"></image>
                                    </view>
                                    <input bindblur="changeInputGoodsNum" class="goods_int" data-arary_index="{{index}}" data-cart_id="{{item.cart_id}}" data-dept_id="{{key}}" value="{{item.num}}"></input>
                                    <view catchtap="changeGoodsNum" class="goods_add" data-arary_index="{{index}}" data-cart_id="{{item.cart_id}}" data-dept_id="{{key}}" data-operator="add_num">
                                        <image class="" src="/yjh-config/images/icon_plus.png"></image>
                                    </view>
                                </view>
                            </navigator>
                        </view>
                        <view catchtap="showDelGoodsModel" class="list_del del" data-array_index="{{index}}" data-cart_id="{{item.cart_id}}" data-dept_id="{{key}}" data-index="{{item.detail_id}}">删除</view>
                    </view>
                </checkbox-group>
            </view>
        </view>
    </view>
    <sm-cart-active bind:csCartChangeEvent="csCartChange" cs_checkbox_disable="{{cs_checkbox_disable}}" id="sm-cart-id">
        <view class="goods_invalid" hidden="{{status_goods_is_empty_div!=0||status_have_valid_goods==0}}">
            <view class="invalid_title">百货失效商品{{count_cart_goods_invalid}}件 <text catchtap="clear_invalidgoods">清除失效商品</text>
            </view>
            <view class="goods_list invalid_bg" wx:for="{{goods_invalid}}" wx:key="key">
                <view class="goods_checkbox">
                    <view class="goods_checkbox_no">失效</view>
                </view>
                <image class="goods_img" src="{{item.cover_img}}"></image>
                <image class="goods_img invalid_back_img" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                <view class="goods_title">{{item.name}}</view>
                <view class="goods_text invalid_goods_div" data-cart_id="{{item.cart_id}}">商品已不能购买</view>
                <view class="goods_subtitle"></view>
            </view>
        </view>
    </sm-cart-active>
    <onlyforyou banner_pic="/yjh-config/images/car_good.png" id="onlyforyou"></onlyforyou>
    <view class="goods_fixed goods_fixed_ios" hidden="{{is_show_foot_tools!=1}}">
        <view class="goods_flex">
            <checkbox-group bindchange="selectAllGoods">
                <label class="checkbox">
                    <checkbox bindchange="selectAllGoods" checked="{{status_is_select_all==0||!checkoutProductCs.allCheck?false:true}}"></checkbox>全选 </label>
            </checkbox-group>
        </view>
        <view class="goods_cart" hidden="{{status_foot_tools!=1}}">
            <view wx:if="{{checkoutProduct.fullMinusAmount>0||checkoutProduct.discountAmount>0||checkoutProductCs.allDiscount>0}}">
                <view class="goods_total">
                    <view class="goods_discount">优惠合计：<text>¥{{filterCs.priceSum(checkoutProduct.activityOrderPrice,checkoutProductCs.allPirce)}}</text>
                    </view>
                    <view catchtap="showCouponDetailModel" class="goods_dis_text">店铺减 ¥ {{filterCs.priceSum(checkoutProduct.total_coupon,checkoutProductCs.allDiscount)}} 优惠明细<image class="goods_dis_arrow" src="/yjh-config/images/goods_dis_arrow.png"></image>
                    </view>
                </view>
                <view bindtap="orderConfirm" class="goods_btn"> 去结算(<text>{{count_cart_selec_goods_num+checkoutProductCs.allNum}}</text>) </view>
            </view>
            <view wx:else>
                <view class="goods_total">合计：<text>¥ {{filterCs.priceSum(checkoutProduct.activityOrderPrice,checkoutProductCs.allPirce)}}</text>
                </view>
                <view catchtap="orderConfirm" class="goods_btn"> 去结算(<text>{{count_cart_selec_goods_num+checkoutProductCs.allNum}}</text>) </view>
            </view>
        </view>
        <view class="goods_del" hidden="{{status_foot_tools!=0}}">
            <view catchtap="showDelMoreGoodsModel" class="goods_del_btn">批量删除</view>
            <view catchtap="addCollect" class="goods_del_btn01">加入收藏</view>
        </view>
    </view>
    <view class="mask" hidden="{{status_is_show_del_model==0}}"></view>
    <view class="dialog_shop" hidden="{{status_is_show_del_model==0}}">
        <view class="dialog_title">确认要删除所选商品么？</view>
        <view class="dialog_shop_btn">
            <view catchtap="cancelShowDelModel" class="dialog_shop_lt">取消</view>
            <view catchtap="delGoodsItem" class="dialog_shop_rt">确定</view>
        </view>
    </view>
    <view class="mask" hidden="{{status_is_show_more_del_model==0}}"></view>
    <view class="dialog_shop" hidden="{{status_is_show_more_del_model==0}}">
        <view class="dialog_title">确认要删除所选商品么？</view>
        <view class="dialog_shop_btn">
            <view catchtap="cancelShowDelModel" class="dialog_shop_lt">取消</view>
            <view catchtap="delMoreGoods" class="dialog_shop_rt">确定</view>
        </view>
    </view>
    <view class="mask" hidden="{{!is_show_coupn_model}}"></view>
    <view class="coupon_popup" hidden="{{!is_show_coupn_model}}">
        <view class="coupon_hd"> 领取优惠券 <image catchtap="closeCouponModel" class="shop_close" src="/yjh-config/images/shop_close.png"></image>
        </view>
        <view class="coupon_content">
            <view class="con_list" wx:for="{{result_quanlist}}" wx:key="quan">
                <image class="con_coupon_bg" src="https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591955871606553.png"></image>
                <view class="con_title">
                    <text wx:if="{{item.if_express==1&&item.express_type==1}}">全额包邮</text>
                    <text wx:else>￥{{item.amount}}</text>
                    <text>{{item.if_express==1?'邮费券':'商城券'}}</text>
                </view>
                <view class="list_lt_label" wx:if="{{item.prime==1}}">
                    <text>Prime专享</text>
                </view>
                <view class="con_subtitle" wx:if="{{item.if_express==0&&item.sharp_amount}}">订单满{{item.sharp_amount}}元可用</view>
                <view class="con_subtitle" wx:if="{{item.if_express==1&&item.sharp_amount}}">{{item.sharp_amount}}</view>
                <view class="con_num">使用期限 {{item.begin_time}}-{{item.end_time}}</view>
                <view catchtap="getCoupon" class="con_btn" data-array_index="{{item.id}}" data-coupon_id="{{item.id}}" data-prime="{{item.prime}}">立即领取</view>
            </view>
            <view class="con_list" wx:for="{{public}}" wx:key="quan">
                <image class="con_coupon_bg" src="https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591955871606553.png"></image>
                <view class="con_title">
                    <text>￥{{item.amount}}</text>
                    <text>通用券</text>
                </view>
                <view class="con_subtitle">订单满{{item.sharp_amount}}元可用</view>
                <view class="con_num">使用期限 {{item.begin_time}}-{{item.end_time}}</view>
                <view catchtap="getPublicCoupon" class="con_btn" data-array_index="{{item.activity_id}}" data-coupon_id="{{item.activity_id}}">立即领取</view>
            </view>
        </view>
        <view catchtap="closeCouponModel" class="coupon_btn">确定</view>
    </view>
    <view class="mask" hidden="{{!isShowSelectStoreModal}}"></view>
    <view class="coupon_popup" hidden="{{!isShowSelectStoreModal}}">
        <view class="coupon_hd">请选择门店<image catchtap="storeChangeCancel" class="shop_close" src="/yjh-config/images/shop_close.png"></image>
        </view>
        <view class="city_content">
            <radio-group bindchange="storeChange" class="radio-group">
                <label class="radio" wx:for="{{storeList}}" wx:for-index="key" wx:key="key">
                    <view class="radio_list">
                        <radio checked="{{item.checked}}" value="{{key}}"></radio>
                        <view class="city_list">{{item.name}}</view>
                    </view>
                </label>
            </radio-group>
        </view>
        <view catchtap="storeChangeConfirm" class="coupon_btn">确定</view>
    </view>
    <view class="mask" hidden="{{show_coupon_detail}}"></view>
    <view class="coupon_dis" hidden="{{show_coupon_detail}}">
        <view class="coupon_hd"> 金额优惠明细 <image catchtap="closeCouponDetailModel" class="shop_close" src="/yjh-config/images/shop_close.png"></image>
        </view>
        <view class="coupon_content">
            <view class="dis_title">*实际优惠金额请以下单页为准</view>
            <view class="dis_sub" wx:for="{{checkoutProduct.details}}">
                <view class="dis_subtext text-bold">{{item.storeName}}：</view>
                <view class="dis_subtext">商品金额<text>¥{{item.totalGoodsPrice}}</text>
                </view>
                <view class="dis_subtext" wx:if="{{item.discountAmount}}">折扣优惠<text class="dis_on">- ¥{{item.discountAmount}}</text>
                </view>
                <view class="dis_subtext" wx:if="{{item.fullMinusAmount}}">满减优惠<text class="dis_on">- ¥{{item.fullMinusAmount}}</text>
                </view>
            </view>
            <view class="dis_sub" wx:if="{{checkoutProductCs.confirmIds&&checkoutProductCs.confirmIds.length>0}}">
                <view class="dis_subtext text-bold">{{checkoutProductCs.storeName}}：</view>
                <view class="dis_subtext">商品金额<text>¥{{filterCs.priceToFixed2(checkoutProductCs.allPirce)}}</text>
                </view>
                <view class="dis_subtext" wx:if="{{checkoutProductCs.allDiscount}}">优惠金额<text class="dis_on">- ¥{{filterCs.priceToFixed2(checkoutProductCs.allDiscount)}}</text>
                </view>
            </view>
            <view class="dis_deta">
                <view class="dis_detatext">共优惠<text class="dis_on">- ¥{{filterCs.priceSum(checkoutProduct.total_coupon,checkoutProductCs.allDiscount)}}</text>
                </view>
                <view class="dis_detatext">合计<text>¥{{filterCs.priceSum(checkoutProduct.activityOrderPrice,checkoutProductCs.allPirce)}}</text>
                </view>
            </view>
        </view>
    </view>
    <view catchtap="closeQjsModal" class="mask" wx:if="{{is_show_qjs}}"></view>
    <view class="coupon_dis qjs-wra" wx:if="{{is_show_qjs}}">
        <view class="coupon_hd"> 请分开结算以下商品 <image catchtap="closeQjsModal" class="shop_close" src="/yjh-config/images/shop_close.png"></image>
        </view>
        <view class="coupon_content">
            <view class="dis_title">暂不支持百货、精选及超市商品合并结算哦</view>
            <view class="jiesuan-wra" wx:for="{{checkoutProduct.details}}" wx:key="storeId">
                <view class="jiesuan-store">{{item.storeName}}</view>
                <scroll-view scrollX class="jiesuan-imgs">
                    <image src="{{imgItem}}" wx:for="{{item.imgs}}" wx:for-item="imgItem"></image>
                </scroll-view>
                <view class="jiesuan-bottom">
                    <text class="jiesuan-yhe" wx:if="{{item.total_coupon>0}}">优惠额:￥{{item.total_coupon}}</text>
                    <text class="jiesuan-hj">合计:</text>
                    <text class="jiesuan-je">￥{{item.activityOrderPrice}}</text>
                    <view bindtap="goQjs" class="jiesuan-btn kuoda" data-store-id="{{item.storeId}}" data-type="1">去结算({{item.qty}})</view>
                </view>
            </view>
            <view class="jiesuan-wra" wx:if="{{checkoutProductCs.confirmIds&&checkoutProductCs.confirmIds.length>0}}">
                <view class="jiesuan-store">{{checkoutProductCs.storeName}}</view>
                <scroll-view scrollX class="jiesuan-imgs">
                    <image src="{{filterCs.addImgPath350x350(imgItem)}}" wx:for="{{checkoutProductCs.imgs}}" wx:for-item="imgItem"></image>
                </scroll-view>
                <view class="jiesuan-bottom">
                    <text class="jiesuan-yhe" wx:if="{{checkoutProductCs.allDiscount>0}}">优惠额:￥{{filterCs.priceToFixed2(checkoutProductCs.allDiscount)}}</text>
                    <text class="jiesuan-hj">合计:</text>
                    <text class="jiesuan-je">￥{{filterCs.priceToFixed2(checkoutProductCs.allPirce)}}</text>
                    <view bindtap="goCsQjs" class="jiesuan-btn kuoda">去结算({{checkoutProductCs.allNum}})</view>
                </view>
            </view>
        </view>
    </view>
</view>
<back-home hdsyNotShow="{{hdsyNotShow}}" homeShow="{{homeShow}}"></back-home>

<wxs module="filter" src="tools.wxs"/>
<wxs module="filterCs" src="..\..\supermarket-utils\filter.wxs"/>