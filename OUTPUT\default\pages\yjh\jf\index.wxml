<view class="last_bottom {{!hiddenName?'preventTouchMove':' '}}">
    <view class="index_bg" id="selfTitle">
        <image bindtap="goBack" class="back_arrow" src="/yjh-config/images/shouye.png" style="z-index:1000;position: fixed;top: {{statusBarHeight+11}}px;left:3px;" wx:if="{{index_type==2}}"></image>
        <image bindtap="goBack" class="back_arrow" src="/yjh-config/images/back_arrow.png" style="z-index:1000;position: fixed;top: {{statusBarHeight+11}}px;left:3px;" wx:else></image>
        <view class="index_name" style="height: {{navHeight}}px;line-height: {{navHeight+25}}px"></view>
        <view class="head">
            <input bindconfirm="goSearch" bindinput="key_input" class="head_seach" confirmType="search" placeholder="搜索商品" value="{{key_words}}"></input>
            <image class="head_seach_icon" mode="widthFix" src="/yjh-config/images/search.png"></image>
        </view>
    </view>
    <view class="whole" style="padding-top: {{bannerPaddingTop?bannerPaddingTop:navHeight+70}}px;">
        <view class="index_con_bg"></view>
        <view class="head_cast">
            <swiper autoplay="{{autoplay}}" circular="true" class="swiper-box" duration="{{duration}}" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
                <swiper-item wx:for="{{lunbo}}" wx:key="item">
                    <image bindtap="goRedirect" class="slide-image" data-is_article="{{item.is_article}}" data-out_link="{{item.out_link}}" data-url="{{item.url}}" mode="widthFix" src="{{item.img_url}}"></image>
                </swiper-item>
            </swiper>
        </view>
    </view>
    <view class="continer" style="background: #fff">
        <swiper circular="true" class="swiper_tab" duration="200" hidden="{{isHomeStatus==0}}" indicatorActiveColor="#FE4754" indicatorColor="#EED4BA" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
            <swiper-item>
                <view class="index_cate">
                    <block wx:for="{{navigations1}}" wx:key="key">
                        <view catchtap="goPrimeLink" class="cate_list" data-item="{{item}}" wx:if="{{item.name==='Prime会员'}}">
                            <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                            <view class="cate_text">{{item.name}}</view>
                        </view>
                        <block wx:else>
                            <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url+'?mdid='+mdid}}" wx:if="{{item.name==='更多分类'||item.name==='积分商城'}}">
                                <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                                <view class="cate_text">{{item.name}}</view>
                            </view>
                            <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:else>
                                <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                                <view class="cate_text">{{item.name}}</view>
                            </view>
                        </block>
                    </block>
                </view>
            </swiper-item>
            <swiper-item wx:if="{{navigations2.length!=0}}">
                <view class="index_cate">
                    <block wx:for="{{navigations2}}" wx:key="key">
                        <view catchtap="goPrimeLink" class="cate_list" data-item="{{item}}" wx:if="{{item.name==='Prime会员'}}">
                            <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                            <view class="cate_text">{{item.name}}</view>
                        </view>
                        <block wx:else>
                            <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url+'?mdid='+mdid}}" wx:if="{{item.name==='更多分类'||item.name==='积分商城'}}">
                                <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                                <view class="cate_text">{{item.name}}</view>
                            </view>
                            <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:else>
                                <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                                <view class="cate_text">{{item.name}}</view>
                            </view>
                        </block>
                    </block>
                </view>
            </swiper-item>
        </swiper>
        <view class="swiper-tab" duration="200" hidden="{{isHomeStatus==1}}">
            <view class="index_else clearfix">
                <view bindtap="typeTwoSearchGoods" class="else_list" data-cate_id="{{item.type_two_id}}" data-cate_level="2" wx:for="{{cate_type_two1}}" wx:key="key">
                    <image class="else_icon" lazyLoad="true" mode="widthFix" src="{{item.cover_img}}"></image>
                    <view class="else_text">{{item.name}}</view>
                </view>
            </view>
        </view>
    </view>
    <view>
        <view class="other_con">
            <block wx:for="{{floorIn}}" wx:key="index">
                <view class="mall_hd_title mall_hd_on {{index>0?'title_top':''}}" style="color: #a5a5a5;">{{item.title}}<span style="margin-left: 3px;font-size: 12px;">({{item.subhead}})</span>
                    <view bindtap="goList" class="mall_more" data-floorid="{{item.id}}" data-sort=""> 查看更多<image class="mall_icon" src="/yjh-config/images/index_sale_more.png"></image>
                    </view>
                </view>
                <navigator hoverClass="none" url="{{item.banner_link}}" wx:if="{{item.banner_link}}">
                    <view class="other_bg">
                        <image class="other_img" lazyLoad="true" mode="widthFix" src="{{item.banner}}" style="height: 50rpx"></image>
                    </view>
                </navigator>
                <view class="other_bg" wx:else>
                    <image class="other_img" lazyLoad="true" mode="widthFix" src="{{item.banner}}" style="height: 50rpx"></image>
                </view>
                <view class="scroll">
                    <scroll-view class="scroll_view_item" scrollX="true">
                        <view bindtap="goDetail" class="scroll_mall_list we-prod-card" data-base="{{goods.base_id}}" data-id="{{goods.detail_id}}" data-item="{{goods}}" data-scene="jf_floor_api_item" wx:for="{{item.goods_list}}" wx:for-item="goods" wx:key="key">
                            <image class="scroll_mall_img" src="{{goods.cover_img}}"></image>
                            <block wx:if="{{goods.is_over==1}}">
                                <image lazyLoad="true" src="https://obs.springland.com.cn/mg-mp/image/20221121174119.png" style="height: 270rpx;width: 250rpx;position: absolute;display: block;top: 0;"></image>
                                <text class="over_price">￥{{goods.limit_price}}</text>
                                <text class="over_del">￥{{goods.del_price}}</text>
                                <text class="over_jf">{{goods.super_jf}}</text>
                            </block>
                            <view class="scroll_mall_title">{{goods.base_name}}</view>
                            <view class="scroll_mall_point"> {{goods.rule}} <text>{{goods.market_price}}</text>
                            </view>
                            <view class="scroll_mall_num">已兑{{goods.jf_sales}}件</view>
                        </view>
                    </scroll-view>
                </view>
            </block>
        </view>
    </view>
    <onlyforyou banner_pic="https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591956024348972.png" id="onlyforyou"></onlyforyou>
</view>
<view bindtap="jf_car" class="index_car">
    <image class="index_car_go" mode="widthFix" src="/yjh-config/images/index_bottom_car_g.png"></image>
</view>
<view bindtap="click_mask" class="mask" hidden="{{hiddenName}}"></view>
<view class="index_bg_popup" hidden="{{hiddenName}}">
    <view class="head">
        <input bindconfirm="goSearch" bindinput="key_input" class="head_seach" confirmType="search" placeholder="搜索商品" value="{{key_words}}"></input>
        <image class="head_seach_icon" mode="widthFix" src="/yjh-config/images/search.png"></image>
    </view>
</view>
<view class="mall_fixed">
    <view bindtap="gotoHuiYuan" class="mall_flex">会员商城</view>
    <view bindtap="gotoMyHuan" class="mall_flex">我的兑换</view>
</view>
