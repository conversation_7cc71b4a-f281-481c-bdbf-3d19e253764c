<view class="skeleton" wx:if="{{pageLoad}}">
    <view class="block line"></view>
    <view class="block"></view>
    <view class="menu">
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
        <view class="item"></view>
    </view>
    <view class="block"></view>
    <view class="block"></view>
</view>
<view class="index_bg" id="selfTitle" style="height: {{navHeight}}px;">
    <image bindtap="back" class="back" hidden="{{isHomeStatus==1}}" src="/yjh-config/images/back_arrow.png" style="top:{{navHeight-25}}px;"></image>
    <view class="index_name" style="height: {{navHeight}}px;line-height: {{navHeight+25}}px"></view>
    <view class="new_log">
        <navigator hoverClass="none" url="/jx/pages/search/index">
            <view class="head">
                <input class="head_seach" disabled="disabled" placeholder="搜索商品"></input>
                <image class="head_seach_icon" mode="widthFix" src="/yjh-config/images/search.png"></image>
            </view>
        </navigator>
        <view bindtap="clickOpen" class="index_hd_rt">
            <image class="index_arrow" mode="widthFix" src="/yjh-config/images/index_arrow.png"></image> 分类 </view>
    </view>
</view>
<view style="padding-top: {{bannerPaddingTop?bannerPaddingTop:navHeight+94}}px;">
    <view class="index_con_bg" hidden="{{isHomeStatus==0}}"></view>
    <view class="head_cast" hidden="{{isHomeStatus==0}}">
        <swiper autoplay="{{autoplay}}" circular="true" class="swiper-box" duration="{{duration}}" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
            <navigator bindtap="toHw" data-out_link="{{item.out_link}}" hoverClass="none" url="{{item.url}}" wx:for="{{banners}}" wx:key="key">
                <swiper-item>
                    <image class="slide-image" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                </swiper-item>
            </navigator>
        </swiper>
    </view>
    <view class="continer">
        <swiper circular="true" class="swiper_tab" duration="200" hidden="{{isHomeStatus==0}}" indicatorActiveColor="#E0BE87" indicatorColor="#DADADA" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
            <swiper-item>
                <view class="index_cate">
                    <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:for="{{navigations1}}" wx:key="key">
                        <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                        <view class="cate_text">{{item.pre_name==null?'':item.pre_name}}{{item.name}}</view>
                    </view>
                </view>
            </swiper-item>
            <swiper-item wx:if="{{navigations2.length!=0}}">
                <view class="index_cate">
                    <view catchtap="nav_link_url" class="cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:for="{{navigations2}}" wx:key="key">
                        <image class="cate_icon" lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                        <view class="cate_text">{{item.pre_name==null?'':item.pre_name}}{{item.name}}</view>
                    </view>
                </view>
            </swiper-item>
        </swiper>
        <view class="swiper-tab" duration="200" hidden="{{isHomeStatus==1}}">
            <view class="index_else clearfix">
                <view bindtap="typeTwoSearchGoods" class="else_list" data-cate_id="{{item.type_two_id}}" data-cate_level="2" wx:for="{{cate_type_two1}}" wx:key="key">
                    <image class="else_icon" lazyLoad="true" mode="widthFix" src="{{item.cover_img}}"></image>
                    <image class="else_icon back_img" lazyLoad="true" mode="widthFix" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                    <view class="else_text">{{item.pre_name==null?'':item.pre_name}}{{item.name}}</view>
                </view>
            </view>
        </view>
    </view>
    <coupon_merge class="jx_coupon" id="coupon_merge"></coupon_merge>
    <subsidy class="jx_coupon" id="actives"></subsidy>
    <view class="other_content" hidden="{{isHomeStatus==0}}">
        <view class="other_con">
            <view class="scroll" wx:for="{{mallFloors}}" wx:key="key">
                <view class="specialwrap" wx:if="{{item.style_type==1}}">
                    <navigator hoverClass="none" url="{{item.banner_link}}">
                        <view class="specialtitle">
                            <view class="titlewrap">
                                <view class="specialtit">{{item.title}}</view>
                                <view class="specialtip">{{item.subhead}}</view>
                            </view>
                            <view class="specialloadmore">更多></view>
                        </view>
                        <view class="specialimgnav">
                            <image mode="widthFix" src="{{item.banner}}"></image>
                        </view>
                    </navigator>
                    <scroll-view scrollX bindscroll="bindScroll" class="spikewrap" data-index="{{index}}" data-slideratio="{{filter.slideRatio(item.goods_list.length,windowWidth)}}">
                        <view class="spikeitem" wx:if="{{item.tops.length!=0}}">
                            <navigator hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{item.tops.detail_id}}">
                                <view class="spikeimg">
                                    <image mode="widthFix" src="{{item.tops.cover_img}}?x-image-process=style/style-350x350"></image>
                                </view>
                                <view class="newwrap">
                                    <view class="goodsflag">热卖产品</view>
                                    <view class="spiketext">
                                        <view class="scroll_zhekou" wx:if="{{item.tops.zhekou}}">{{item.tops.zhekou}}折</view> {{item.tops.name}}</view>
                                    <view class="spikeprice">
                                        <view class="spikenewprice">¥ {{item.tops.limit_price}}</view>
                                        <view class="spikeoldprice">¥ {{item.tops.del_price}}</view>
                                    </view>
                                </view>
                            </navigator>
                        </view>
                        <view class="spikeitem" wx:for="{{item.goods_list}}" wx:for-item="itemtwo" wx:key="index2">
                            <navigator hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{itemtwo.detail_id}}">
                                <view class="spikeimg">
                                    <image mode="widthFix" src="{{itemtwo.cover_img}}?x-image-process=style/style-350x350"></image>
                                </view>
                                <view class="newwrap">
                                    <view class="goodsflag">热卖产品</view>
                                    <view class="spiketext">
                                        <view class="scroll_zhekou" wx:if="{{itemtwo.zhekou}}">{{itemtwo.zhekou}}折</view> {{itemtwo.name}}</view>
                                    <view class="spikeprice">
                                        <view class="spikenewprice">¥ {{itemtwo.limit_price}}</view>
                                        <view class="spikeoldprice">¥ {{itemtwo.del_price}}</view>
                                    </view>
                                </view>
                            </navigator>
                        </view>
                        <navigator class="newloadmore spikeitem" hoverClass="none" url="/home/<USER>/floor-goods-list/index?floor_id={{item.id}}" wx:if="{{item.goods_list.length>4}}">
                            <view class="loadwrap">
                                <view>查看更多</view>
                                <image mode="widthFix" src="../../img/smicon/loadmore.png"></image>
                            </view>
                        </navigator>
                    </scroll-view>
                    <view class="slide">
                        <view class="slide-bar">
                            <view class="slide-action" style="width:{{filter.sliderWidth(item.goods_list.length)}}rpx; margin-left:{{newbtnbox[index]<=1?0:newbtnbox[index]+'rpx'}};"></view>
                        </view>
                    </view>
                </view>
                <block wx:else>
                    <navigator hoverClass="none" url="{{item.banner_link}}" wx:if="{{item.banner_link}}">
                        <view class="other_bg">
                            <image class="other_img" lazyLoad="true" mode="widthFix" src="{{item.banner}}"></image>
                        </view>
                    </navigator>
                    <view class="other_bg" wx:else>
                        <image class="other_img" lazyLoad="true" mode="widthFix" src="{{item.banner}}"></image>
                    </view>
                    <scroll-view class="scroll_view_item" hidden="{{item.is_hidden_tops}}" scrollX="true">
                        <view class="scroll_lt">
                            <view class="scroll_title">{{item.title}}</view>
                            <view class="scroll_subtitle">{{item.subhead}}</view>
                            <view class="scroll_line"></view>
                            <navigator bindtap="we_click" class="we-prod-card" data-id="{{item.tops.detail_id}}" data-item="{{item.tops}}" data-scene="index_mall_api_item" hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{item.tops.detail_id}}&jx=1" wx:if="{{item.tops}}">
                                <view class="posi-rea">
                                    <image class="scroll_lt_img" lazyLoad="true" mode="widthFix" src="{{item.tops.cover_img}}"></image>
                                    <image class="scroll_lt_img back_img" lazyLoad="true" mode="widthFix" src="{{item.tops.back_img}}" wx:if="{{item.tops.back_img!=''}}"></image>
                                    <activity_frame addClass="scroll_lt_img" storeId="{{item.tops.store_id}}"></activity_frame>
                                </view>
                                <view class="scroll_name">{{item.tops.pre_name==null?'':item.tops.pre_name}}{{item.tops.name}}</view>
                                <view class="scroll_price">
                                    <text>¥ {{item.tops.limit_price}}</text>
                                    <text>¥ {{item.tops.del_price}}</text>
                                </view>
                            </navigator>
                        </view>
                        <view class="scroll_rt" style="width:{{item.width}};">
                            <view class="scroll_rt_list" wx:for="{{item.goods_list}}" wx:for-item="goods" wx:key="key">
                                <navigator bindtap="we_click" class="we-prod-card" data-id="{{goods.detail_id}}" data-item="{{goods}}" data-scene="index_mall_api_item" hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{goods.detail_id}}&jx=1">
                                    <view class="posi-rea">
                                        <image class="scroll_rt_img" lazyLoad="true" mode="widthFix" src="{{goods.cover_img}}?x-image-process=style/style-350x350"></image>
                                        <image class="scroll_rt_img back_img" lazyLoad="true" mode="widthFix" src="{{goods.back_img}}?x-image-process=style/style-350x350" wx:if="{{goods.back_img!=''}}"></image>
                                        <activity_frame addClass="scroll_rt_img" storeId="{{goods.store_id}}"></activity_frame>
                                    </view>
                                    <view class="scroll_name">{{goods.pre_name==null?'':goods.pre_name}}{{goods.name}}</view>
                                    <view class="scroll_price">
                                        <text>¥ {{goods.limit_price}}</text>
                                        <text>¥ {{goods.del_price}}</text>
                                    </view>
                                </navigator>
                            </view>
                            <navigator url="/jx/pages/floor-goods-list/index?floor_id={{item.id}}" wx:if="{{item.show_more}}">
                                <view class="scroll_rt_more">
                                    <image src="https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591955088592674.png"></image>
                                </view>
                            </navigator>
                        </view>
                    </scroll-view>
                </block>
            </view>
        </view>
    </view>
</view>
<view class="morethingall" hidden="{{isHomeStatus==0}}">
    <view class="morethingbox">
        <view class="specialtit">为你推荐</view>
        <view class="specialtip">超值好物任你选</view>
    </view>
    <view class="morethingnewwrap">
        <view class="tabs {{tabFixed?'fixtab':''}}" style="top:{{navHeight}}px;">
            <view bindtap="changeTab" class="tabs-item {{currentNum==index?'active':''}}" data-cate_id="{{item.id}}" data-current="{{index}}" wx:for="{{tuijianlist}}" wx:key="index">{{item.name}}</view>
        </view>
    </view>
    <view class="morethingwrap" style="min-height:calc(100vh - {{navHeight+45+60}}px)">
        <view class="moregoodsthingbox">
            <view class="moregoodsthingitem" wx:for="{{allcurrentdata}}" wx:key="index">
                <navigator hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{item.detail_id}}">
                    <view class="moregoodsthingimg">
                        <image src="{{item.cover_img}}?x-image-process=style/style-350x350"></image>
                    </view>
                    <view class="goodswrap">
                        <view class="moregoodsthingtext">
                            <view class="scroll_zhekou" wx:if="{{item.zhekou}}">{{item.zhekou}}折</view> {{item.name}}</view>
                        <view class="moregoodsthingprice">
                            <view class="moregoodsthingnewprice">¥ {{item.price}}</view>
                            <view class="moregoodsthingoldprice">¥ {{item.market_price}}</view>
                        </view>
                    </view>
                </navigator>
            </view>
        </view>
    </view>
</view>
<view class="content_tab" hidden="{{isHomeStatus==1}}">
    <view class="swiper-tab">
        <view bindtap="swichNav" class="swiper-tab-list {{subTab==0?'on':''}}" data-current="0" data-order_by="sales_num_down">销量</view>
        <view bindtap="swichNav" class="swiper-tab-list {{subTab==1?'on':''}}" data-current="1" data-order_by="create_time_down">最新</view>
        <view bindtap="swichNav" class="swiper-tab-list {{subTab>=2?'on':''}}" data-current="2" data-order_by="price_down" hidden="{{cate_goods_price_icon==0}}"> 价格<image class="content_tab_icon" mode="widthFix" src="/yjh-config/images/search_down.png"></image>
        </view>
        <view bindtap="swichNav" class="swiper-tab-list {{subTab>=2?'on':''}}" data-current="2" data-order_by="price_up" hidden="{{cate_goods_price_icon==1}}"> 价格<image class="content_tab_icon" mode="widthFix" src="/yjh-config/images/search_up.png"></image>
        </view>
    </view>
    <view class="content_other clearfix">
        <view class="goods" wx:if="{{cate_type_goods.length!=0}}" wx:for="{{cate_type_goods}}" wx:key="key">
            <navigator bindtap="we_click" class="we-prod-card" data-id="{{item.id}}" data-item="{{item}}" data-scene="nav_class_api_item" hoverClass="none" url="/jx/pages/goods-detail/index?goods_detail_id={{item.id}}&jx=1">
                <view class="goods_img">
                    <image class="goods_image" lazyLoad="true" mode="widthFix" src="{{item.cover_img}}"></image>
                    <image class="goods_image back_img" lazyLoad="true" mode="widthFix" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                </view>
                <view class="goods_title">{{item.pre_name==null?'':item.pre_name}}{{item.name}}</view>
                <view class="goods_price">
                    <text>¥ {{item.price}}</text>
                    <text>¥ {{item.market_price}}</text>
                </view>
            </navigator>
        </view>
        <view class="none" wx:if="{{cate_type_goods.length==0}}">
            <image class="search_none" mode="widthFix" src="/yjh-config/images/search_none.png"></image>
            <view class="none_title">抱歉，未找到相关商品~</view>
        </view>
    </view>
</view>
<fenxiao></fenxiao>
<gift id="gift-id" navHeight="{{navHeight}}"></gift>
<screen id="screen-id"></screen>

<wxs module="filter" src="tool.wxs"/>