<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{index==TabCur?'text-blue cur cust-color':''}}" data-id="{{index}}" wx:for="{{3}}"> {{titleList[index]}} </item>
    </view>
</scroll-view>
<view class="margin-cust">
    <view bindtap="fireEvent" class="coupon-item" data-index="{{index}}" wx:for="{{couponList}}">
        <view class="xian">
            <view class="left-wrapper">￥<text>{{item.ye}}</text>
            </view>
            <view class="right-wrapper">
                <view class="coupon-title">{{item.qmc}}</view>
                <view class="coupon-desc">{{item.sysm}}</view>
                <view class="coupon-range">{{item.ksrq}}-{{item.jsrq}}</view>
                <view class="tap-kuoda">
                    <view class="shiyong {{TabCur==0?'':'unuse'}}">{{btnTextArray[TabCur]}}</view>
                </view>
            </view>
        </view>
    </view>
</view>
<back-home homeShow="{{homeShow}}"></back-home>
