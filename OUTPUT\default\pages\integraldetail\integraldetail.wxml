<view>
    <view>
        <image class="top-img" src="http:///picture.springland.com.cn:8101/static/images/integral/{{filename2}}" style="750rpx" wx:if="{{filename2}}"></image>
    </view>
    <view class="info">
        <view class="salename">{{name}}</view>
        <view class="saleprice">
            <text>{{jf}}积分</text>
            <text wx:if="{{je>0}}">+{{je}}元</text>
        </view>
        <view class="salenum">
            <text wx:if="{{isshowstock==1}}">库存：{{stocknum}}	</text>
            <text wx:if="{{isshowsales==1}}">销量：{{salenum}}</text>
        </view>
    </view>
</view>
<list class="menu">
    <item class="" wx:if="{{hyktypename.length>0}}">
        <view class="content"> “{{hyktypename}}”用户可兑换 </view>
    </item>
    <item bindtap="toPrime" class="arrow" wx:if="{{isprime==1}}">
        <view class="content">
            <view class="access">Prime会员专享</view>
            <view class="primetext">开通Prime会员立享更多优惠</view>
        </view>
    </item>
    <item style="height:120rpx">
        <view class="content">
            <text class="icon-time text-grey"></text>
            <text class="text-grey timetext">兑换时间：{{starttime}} 至 {{endtime}}
</text>
        </view>
    </item>
    <item style="height:120rpx">
        <view class="content">
            <text class="icon-time text-grey"></text>
            <text class="text-grey timetext">核销时间：{{checkstarttime}} 至 {{checkendtime}}
</text>
        </view>
    </item>
    <item style="height:120rpx">
        <view class="content">
            <text class="icon-location text-grey"></text>
            <text class="text-grey timetext">核销地点：{{address}}
</text>
        </view>
    </item>
</list>
<view class="saledetail">
    <view class="title"> 商品详情 </view>
    <view class="main">
        <view class="container">
            <view class="uinn">
                <rich-text nodes="{{content}}"></rich-text>
            </view>
        </view>
    </view>
</view>
<view class="blank"></view>
<view class="paybar">
    <view class="bar-left">
        <text decode="{{true}}" space="{{true}}">&nbsp;&nbsp;</text>
        <text>{{jf}}积分</text>
    </view>
    <user-info-btn hasPhone="{{hasPhone}}">
        <view bindtap="confirm" class="bar-right">兑换</view>
    </user-info-btn>
</view>
<block wx:if="{{isShow==1}}">
    <view class="shadow"></view>
    <view class="modal">
        <image bindtap="closeModal" src="/img/icon/<EMAIL>"></image>
        <view>
            <view class="title"> 兑换提醒 </view>
            <view class="price"> {{jf}}积分{{je>0?'+'+je+'元':''}} </view>
            <view class="salename">
                <text>商品</text>
                <text>{{name}}</text>
            </view>
            <button bindtap="onTap" class="submit" disabled="disabled" wx:if="{{disabled}}">立即{{je>0?'支付':'兑换'}}</button>
            <user-info-btn hasPhone="{{hasPhone}}" wx:else>
                <button bindtap="exchange" class="submit">立即{{je>0?'支付':'兑换'}}</button>
            </user-info-btn>
        </view>
    </view>
</block>
<back-home homeShow="{{homeShow}}"></back-home>
