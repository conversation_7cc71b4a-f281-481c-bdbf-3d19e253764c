<view class="none-show" wx:if="{{noneFlag}}">{{noneMsg}}</view>
<block wx:else>
    <view class="bg-white aa">
        <view class="tc-title">
            <image src="/img/icon/tingche.png"></image>
            <text>{{mallname}}</text>
        </view>
        <view class="jine">
            <text class="da">{{parkInfo.payable/100}}</text>
            <text>元</text>
        </view>
        <view class="jine-title">待付金额</view>
    </view>
    <view class="title-wr">优惠方式</view>
    <list class="menu">
        <item bindtap="showJifen" class="arrow" wx:if="{{mdid!=6023}}">
            <view class="content">
                <text class="text-black">商场积分</text>
            </view>
            <view class="action">
                <text class="text-blue text-sm" wx:if="{{jifenJian>0}}">-{{jifenJian/100}}</text>
                <block wx:else>
                    <tag class="bg-blue round"></tag>
                    <text class="text-grey text-sm">{{jf}}积分可用</text>
                </block>
            </view>
        </item>
        <item>
            <view class="content">
                <text class="text-black">会员特权</text>
            </view>
            <view class="action">
                <block wx:if="{{parkInfo.freeAmount>0}}">
                    <tag class="bg-blue round"></tag>
                    <text class="text-grey text-sm">{{parkInfo.klxmc}}会员已优惠{{parkInfo.freeAmount}}元</text>
                </block>
                <block wx:elif="{{parkInfo.freeMinutes>0}}">
                    <tag class="bg-blue round"></tag>
                    <text class="text-grey text-sm">{{parkInfo.klxmc}}会员已免费{{parkInfo.freeMinutes}}分钟</text>
                </block>
                <text class="text-grey text-sm" wx:else>暂无优惠</text>
            </view>
        </item>
    </list>
    <view class="title-wr">停车信息</view>
    <list class="menu">
        <item>
            <view class="content">
                <text class="text-black">入场时间</text>
            </view>
            <view class="action">
                <text class="text-grey text-sm">{{parkInfo.entryTime}}</text>
            </view>
        </item>
        <item>
            <view class="content">
                <text class="text-black">停车时长</text>
            </view>
            <view class="action">
                <text class="text-grey text-sm">{{parkInfo.parkingTime}}</text>
            </view>
        </item>
    </list>
    <view class="bottom-ab bg-white">
        <view class="tishi">
            <image src="/img/icon/tishi.png"></image>
            <text>请于付款后{{parkingSet.lcsj}}分钟内离场，超时将加收停车费</text>
        </view>
        <view class="btn-wrapper">
            <view class="yingfu">应付金额： <text>{{shifuAmount/100}}</text> 元 </view>
            <button bindtap="lijifu" class="liji" disabled="{{btnDsiabled}}">立即付款</button>
        </view>
    </view>
</block>
<modal-box class="bottom-modal {{tcqShow?'show':''}}">
    <dialog>
        <view class="motai">
            <bar class="">
                <view class="action bt-title">商场停车券</view>
                <view bindtap="hideTcq" class="action text-blue">取消</view>
            </bar>
            <scroll-view scrollY class="www">
                <view class="bg-white ss">
                    <view class="xz-item solid-top" wx:for="{{equityList}}">
                        <view class="mingcheng">{{item.jian}}元（{{item.name}}）</view>
                        <view bindtap="chooseQuan" class="xuanze" data-quanidex="{{index}}">选择</view>
                    </view>
                    <view style="height:30rpx;"></view>
                </view>
            </scroll-view>
        </view>
    </dialog>
</modal-box>
<modal-box class="{{jfShow?'show':''}}">
    <dialog>
        <bar class="justify-end">
            <view class="content">请输入抵扣的金额</view>
            <view bindtap="hideJifen" class="action">
                <text class="icon-close text-blue"></text>
            </view>
        </bar>
        <view class="padding-xl">
            <input bindinput="jineInput" class="input-shuzi" placeholder="金额" type="number"></input>
            <view class="yuan">元</view>
        </view>
        <bar class="justify-end">
            <view class="action">
                <button bindtap="hideJifen" class="line-blue text-blue">取消</button>
                <button bindtap="queding" class="bg-blue margin-left">确定</button>
            </view>
        </bar>
    </dialog>
</modal-box>
