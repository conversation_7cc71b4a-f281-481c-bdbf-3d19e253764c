<form bindsubmit="search">
    <bar class="search">
        <view class="search-form round">
            <text class="icon-search"></text>
            <input confirmType="search" name="key" placeholder="搜索商品名称" type="text" value="{{key}}"></input>
        </view>
        <button formType="submit">
            <view class="action">
                <image src="/img/icon/<EMAIL>"></image>
            </view>
        </button>
    </bar>
</form>
<view class="blank"></view>
<view class="container">
    <view bindtap="goDetail" class="item" data-jlbh="{{it.jlbh}}" data-lpid="{{it.lpid}}" wx:for="{{list}}" wx:for-index="idx" wx:for-item="it">
        <image src="http://picture.springland.com.cn:8101/static/images/integral/{{it.filename1}}"></image>
        <view class="access" wx:if="{{it.isprime==1}}">Prime会员专享</view>
        <view class="text">
            <view class="salename">{{it.name}}</view>
            <view class="saleprice">
                <text>{{it.jf}}积分{{it.je>0?'+'+it.je+'元':''}}</text>
            </view>
            <view class="salenum">
                <text wx:if="{{it.isshowstock==1}}">库存	{{it.stocknum}}
</text>
                <text wx:if="{{it.isshowsales==1}}">销量	{{it.salenum}}</text>
            </view>
        </view>
    </view>
</view>
<back-home homeShow="{{homeShow}}"></back-home>
<view class="nodata" wx:if="{{list.length==0}}">—————— 暂无数据 ——————</view>
