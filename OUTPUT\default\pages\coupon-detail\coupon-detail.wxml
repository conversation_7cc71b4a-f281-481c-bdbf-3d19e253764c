<block wx:if="{{hdlx==3}}">
    <view class="qmq-cou-wra">
        <view bindtap="seeDetail" class="coupon-item" data-index="{{index}}" wx:for="{{couponList[0].qxxs}}">
            <view class="xian">
                <view class="left-wrapper">￥ <text>{{item.je}}</text>
                </view>
                <view class="right-wrapper">
                    <view class="coupon-title">{{item.qmc}}</view>
                    <view class="{{item.showDesc?'coupon-desc-all':'coupon-desc'}}">{{item.sysm}}</view>
                    <view class="coupon-range">{{item.ksyxq}} - {{item.jsyxq}}</view>
                </view>
            </view>
        </view>
        <view class="des-wrapper margin-top" style="padding-top:20rpx;">
            <view class="img-wrapper">
                <image class="img-i" src="../../img/icon/<EMAIL>"></image>
            </view>
            <view class="content1">
                <view class="title">活动说明</view>
                <view class="descr">
                    <text>{{couponList[0].hdsm}}</text>
                </view>
            </view>
        </view>
        <view class="gmxz-wrapper" wx:if="{{couponList[0].qlb==2}}">
            <view class="gmxz-title">购买须知</view>
            <view class="gmxz-desc">
                <text>1.优惠券有效期及使用规则以每张优惠券内说明为准
      2.优惠券不找零，仅限单次使用
      3.券包中任意一张券一经使用，则该券包不可退换</text>
            </view>
        </view>
    </view>
    <view class="content">
        <text class="icon-time text-grey shijian"></text>
        <text class="text-grey timetext">购买时间：{{couponList[0].gmkssj}} 至 {{couponList[0].gmjssj}}</text>
    </view>
    <view class="paybar">
        <view class="bar-left">
            <view class="money">￥{{couponList[0].gmje}}<text class="txtaa">￥{{couponList[0].gmje+couponList[0].zqje}}</text>
            </view>
            <view class="shengyu" wx:if="{{item.xskcs==1}}">剩余{{couponList[0].kcs}}份</view>
        </view>
        <user-info-btn hasPhone="{{hasPhone}}">
            <view bindtap="couponSubSure" class="bar-right">支付</view>
        </user-info-btn>
    </view>
</block>
<block wx:else>
    <coupon-list bind:couponSjEvent="couponSjEvent" bind:fireCouponEvent="couponSubSure" btnText="{{couponList[0].hdlx==1||couponList[0].hdlx==4?'领取':'兑换'}}" couponList="{{couponList}}" hasPhone="{{hasPhone}}" isUse="{{isUse}}"></coupon-list>
    <view class="margin-top"></view>
    <view class="des-wrapper">
        <view class="img-wrapper">
            <image class="img-i" src="../../img/icon/<EMAIL>"></image>
        </view>
        <view class="content1">
            <view class="title">{{couponList[0].hdlx==1||couponList[0].hdlx==4?'领取时间':'兑换时间'}}</view>
            <view class="descr" wx:if="{{couponList[0].hdlx==1||couponList[0].hdlx==4}}">{{couponList[0].lqkssj}} - {{couponList[0].lqjssj}}</view>
            <view class="descr" wx:else>{{couponList[0].dqkssj}} - {{couponList[0].dqjssj}}</view>
        </view>
    </view>
    <view class="des-wrapper">
        <view class="img-wrapper">
            <image class="img-i" src="../../img/icon/<EMAIL>"></image>
        </view>
        <view class="content1">
            <view class="title">有效期限</view>
            <view class="descr">{{couponList[0].sykssj}} - {{couponList[0].syjssj}}</view>
        </view>
    </view>
    <view class="des-wrapper">
        <view class="img-wrapper">
            <image class="img-i" src="../../img/icon/<EMAIL>"></image>
        </view>
        <view class="content1">
            <view class="title">使用说明</view>
            <view class="descr">
                <text>{{couponList[0].sysm}}</text>
            </view>
        </view>
    </view>
</block>
<modal-pay-coupon bindnumMinus="numMinus" bindnumPlus="numPlus" bindpayevent="pay" canBuy="{{canBuy}}" disabled="{{disabled}}" isShow="{{isModalShow}}" num="{{couponList[selectIndex].qty}}" price="{{couponList[selectIndex].gmje}}" saleName="{{couponList[selectIndex].bt}}"></modal-pay-coupon>
<back-home homeShow="{{homeShow}}"></back-home>
<modal-box class="{{couponSjShow?'show':''}}"> -->
 <dialog class="dialogaa">
        <bar class="justify-end">
            <view class="contentFu">领取成功，将为您同步加入到微信卡包</view>
        </bar>
        <view class="padding-xl bg-white">
            <send-coupon bindcustomevent="getcouponEvent" send_coupon_merchant="{{couponSjInfo.send_coupon_merchant}}" send_coupon_params="{{couponSjInfo.send_coupon_params}}" sign="{{couponSjInfo.sign}}">
                <button class="bg-green btn-sjqlq">确认</button>
            </send-coupon>
        </view>
    </dialog>
</modal-box>
<button bindtap="getPoster" class="swiper_fixed" data-type="{{hdlx}}" wx:if="{{isLogin&&hdlx<4}}">
    <image class="goods_share" mode="widthFix" src="/yjh-config/images/goods_share.png"></image>
    <text class="goods_share_text">分享</text>
</button>
<view bindtap="tcodePop" class="mask" hidden="{{codePop}}"></view>
<view class="dialog" hidden="{{codePop}}">
    <view class="posi-rea">
        <image class="img" mode="widthFix" src="{{shareImg}}" style="max-width:100%;height:auto"></image>
    </view>
    <view class="dialog_bottom" hidden="{{codePop}}">
        <button class="share_btn" openType="share" style="display: inline">
            <image mode="widthFix" src="https://obs.springland.com.cn/obs-179f/huadi2/2020/10/1602490089777047.png"></image> 分享好友 </button>
        <button bindtap="save" class="share_btn" style="display: inline">
            <image mode="widthFix" src="/yjh-config/images/pyq.png"></image> 保存图片 </button>
    </view>
</view>
