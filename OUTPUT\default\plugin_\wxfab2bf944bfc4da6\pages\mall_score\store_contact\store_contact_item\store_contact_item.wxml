<view class="component-store-contact-item exposure-report-dom" data-exposure-data="{{({event_id:100008,store_id:storeInfo.store_id,store_name:storeInfo.store_name})}}" id="store-guider-item-{{storeInfo.store_id}}">
    <view class="store-info">
        <image class="store-logo" mode="aspectFill" src="{{storeInfo.store_logo}}"></image>
        <text class="store-name">{{storeInfo.store_name}}</text>
    </view>
    <view class="store-contact">
        <view bind:tap="openStoreGuideList" class="contact-guider" data-item="{{storeInfo}}" data-report-data="{{({event_id:5001,store_id:storeInfo.store_id,store_name:storeInfo.store_name})}}">
            <image class="contact-guider-ico" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-wechat-green.png"></image>
            <text>店员</text>
        </view>
        <view class="store-contact-split"></view>
        <view bind:tap="callGuiderPhone" class="contact-phone" data-item="{{storeInfo}}" data-report-data="{{({event_id:5002,store_id:storeInfo.store_id,store_name:storeInfo.store_name})}}">
            <image class="contact-guider-ico" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-phone-yellow.png"></image>
            <text>电话</text>
        </view>
    </view>
</view>
