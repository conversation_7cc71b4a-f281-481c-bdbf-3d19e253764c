/**
 * 全面的Authorization加密方式测试
 * 尝试所有可能的组合，包括时间戳、随机数等
 */

const crypto = require('crypto');

// 从抓包数据中提取的信息
const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
const hyid = "820536796";
const mdid = "6021";

// 常量
const REQUEST_KEY = "SPRINGLAND";
const REQUEST_SECRET = "springland*&^0627@";

function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

function sha1(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

/**
 * 尝试所有可能的时间戳组合
 */
function testWithTimestamps() {
    console.log("=== 测试时间戳相关组合 ===");
    
    // 基于抓包时间推测的可能时间戳
    const possibleTimestamps = [];
    
    // 2024年7月29日的各种时间戳格式
    const baseDate = new Date('2024-07-29');
    const baseDateChina = new Date('2024-07-29T08:52:22+08:00'); // 中国时间
    
    // 添加各种可能的时间戳
    possibleTimestamps.push(
        Math.floor(baseDate.getTime() / 1000),           // 秒级时间戳
        Math.floor(baseDateChina.getTime() / 1000),      // 中国时间秒级
        baseDate.getTime(),                              // 毫秒级时间戳
        baseDateChina.getTime(),                         // 中国时间毫秒级
        1722211942,  // 2024-07-29 00:52:22 UTC
        1722248542,  // 2024-07-29 10:52:22 UTC (可能的中国时间)
        20240729,    // 日期格式
        20240729085222, // 完整时间格式
    );
    
    // 也尝试一些固定的常见时间戳
    possibleTimestamps.push(
        1640995200,  // 2022-01-01
        1672531200,  // 2023-01-01
        1704067200,  // 2024-01-01
        1735689600,  // 2025-01-01
    );
    
    const prefix = md5(hyid).substring(0, 8); // 1fb508f5
    const targetSuffix = targetAuth.substring(8); // a4e6557df6cb447aa314544f308a7a5d
    
    console.log("前缀 (MD5(hyid)前8位):", prefix);
    console.log("目标后缀:", targetSuffix);
    console.log();
    
    let found = false;
    
    possibleTimestamps.forEach((timestamp, i) => {
        const timestampStr = timestamp.toString();
        
        // 测试各种时间戳组合
        const combinations = [
            timestampStr,
            hyid + timestampStr,
            mdid + timestampStr,
            timestampStr + hyid,
            timestampStr + mdid,
            hyid + mdid + timestampStr,
            timestampStr + hyid + mdid,
            hyid + timestampStr + mdid,
            mdid + timestampStr + hyid,
            // 加上常量的组合
            REQUEST_KEY + timestampStr,
            timestampStr + REQUEST_SECRET,
            REQUEST_KEY + timestampStr + REQUEST_SECRET,
            REQUEST_KEY + hyid + mdid + timestampStr,
            timestampStr + REQUEST_KEY + hyid + mdid,
            REQUEST_KEY + hyid + mdid + timestampStr + REQUEST_SECRET,
            // 与原始token的组合
            originalToken.substring(8) + timestampStr,
            timestampStr + originalToken.substring(8),
            originalToken.substring(8) + hyid + mdid + timestampStr,
        ];
        
        combinations.forEach((combo, j) => {
            const md5Result = md5(combo);
            const sha1Result = sha1(combo);
            
            // 检查MD5的前32位
            if (md5Result.substring(0, 32) === targetSuffix) {
                const fullAuth = prefix + md5Result.substring(0, 32);
                console.log(`✓ MD5匹配! 时间戳${i+1}: ${timestamp}, 组合${j+1}: "${combo}"`);
                console.log(`  完整Authorization: ${fullAuth}`);
                console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
                found = true;
            }
            
            // 检查SHA1的前32位
            if (sha1Result.substring(0, 32) === targetSuffix) {
                const fullAuth = prefix + sha1Result.substring(0, 32);
                console.log(`✓ SHA1匹配! 时间戳${i+1}: ${timestamp}, 组合${j+1}: "${combo}"`);
                console.log(`  完整Authorization: ${fullAuth}`);
                console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
                found = true;
            }
            
            // 检查完整匹配
            if (md5Result === targetSuffix) {
                console.log(`✓ MD5完整匹配! 时间戳${i+1}: ${timestamp}, 组合${j+1}: "${combo}"`);
                found = true;
            }
            
            if (sha1Result === targetSuffix) {
                console.log(`✓ SHA1完整匹配! 时间戳${i+1}: ${timestamp}, 组合${j+1}: "${combo}"`);
                found = true;
            }
        });
    });
    
    if (!found) {
        console.log("未找到时间戳相关的匹配");
    }
    
    return found;
}

/**
 * 测试随机数和其他可能的参数
 */
function testWithRandomNumbers() {
    console.log("\n=== 测试随机数和其他参数 ===");
    
    const prefix = md5(hyid).substring(0, 8);
    const targetSuffix = targetAuth.substring(8);
    
    // 可能的随机数或固定参数
    const possibleParams = [
        "123456",
        "000000",
        "999999",
        "888888",
        "666666",
        hyid.substring(0, 6), // hyid的前6位
        hyid.substring(2, 8), // hyid的中间6位
        mdid + "000",
        mdid + "123",
        "sign",
        "auth",
        "token",
        originalToken.substring(0, 6),
        originalToken.substring(34, 40), // token的最后6位
    ];
    
    let found = false;
    
    possibleParams.forEach((param, i) => {
        const combinations = [
            param,
            hyid + param,
            mdid + param,
            param + hyid,
            param + mdid,
            hyid + mdid + param,
            param + hyid + mdid,
            hyid + param + mdid,
            mdid + param + hyid,
            REQUEST_KEY + param,
            param + REQUEST_SECRET,
            REQUEST_KEY + param + REQUEST_SECRET,
            originalToken.substring(8) + param,
            param + originalToken.substring(8),
        ];
        
        combinations.forEach((combo, j) => {
            const md5Result = md5(combo);
            const sha1Result = sha1(combo);
            
            if (md5Result.substring(0, 32) === targetSuffix) {
                const fullAuth = prefix + md5Result.substring(0, 32);
                console.log(`✓ MD5匹配! 参数${i+1}: "${param}", 组合${j+1}: "${combo}"`);
                console.log(`  完整Authorization: ${fullAuth}`);
                console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
                found = true;
            }
            
            if (sha1Result.substring(0, 32) === targetSuffix) {
                const fullAuth = prefix + sha1Result.substring(0, 32);
                console.log(`✓ SHA1匹配! 参数${i+1}: "${param}", 组合${j+1}: "${combo}"`);
                console.log(`  完整Authorization: ${fullAuth}`);
                console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
                found = true;
            }
            
            if (md5Result === targetSuffix) {
                console.log(`✓ MD5完整匹配! 参数${i+1}: "${param}", 组合${j+1}: "${combo}"`);
                found = true;
            }
            
            if (sha1Result === targetSuffix) {
                console.log(`✓ SHA1完整匹配! 参数${i+1}: "${param}", 组合${j+1}: "${combo}"`);
                found = true;
            }
        });
    });
    
    if (!found) {
        console.log("未找到随机数相关的匹配");
    }
    
    return found;
}

/**
 * 测试是否为服务器响应的固定值
 */
function testServerResponse() {
    console.log("\n=== 检查服务器响应证据 ===");
    
    console.log("分析Authorization与原始Token的关系:");
    console.log("原始Token:    ", originalToken);
    console.log("Authorization:", targetAuth);
    console.log();
    
    console.log("相同部分分析:");
    console.log("前8位相同:", originalToken.substring(0, 8) === targetAuth.substring(0, 8));
    console.log("前8位值:", originalToken.substring(0, 8));
    console.log();
    
    console.log("不同部分分析:");
    console.log("Token后32位:  ", originalToken.substring(8));
    console.log("Auth后32位:   ", targetAuth.substring(8));
    console.log();
    
    // 检查是否有规律
    const tokenSuffix = originalToken.substring(8);
    const authSuffix = targetAuth.substring(8);
    
    console.log("后32位差异分析:");
    for (let i = 0; i < 32; i++) {
        if (tokenSuffix[i] !== authSuffix[i]) {
            console.log(`位置${i}: Token='${tokenSuffix[i]}', Auth='${authSuffix[i]}'`);
        }
    }
    
    console.log("\n结论:");
    console.log("1. 前8位完全相同，说明有共同的生成基础");
    console.log("2. 后32位完全不同，说明使用了不同的算法或参数");
    console.log("3. 这很可能是服务器端基于会话状态生成的不同token");
}

/**
 * 主测试函数
 */
function runComprehensiveTest() {
    console.log("=== 全面Authorization加密方式测试 ===");
    console.log("目标Authorization:", targetAuth);
    console.log("原始Token:", originalToken);
    console.log("HYID:", hyid);
    console.log("MDID:", mdid);
    console.log();
    
    // 确认前8位的生成方式
    console.log("--- 确认前8位生成方式 ---");
    const hyidMd5 = md5(hyid);
    console.log("MD5(hyid):", hyidMd5);
    console.log("前8位匹配:", hyidMd5.substring(0, 8) === targetAuth.substring(0, 8));
    console.log();
    
    // 测试时间戳相关
    const foundTimestamp = testWithTimestamps();
    
    // 测试随机数相关
    const foundRandom = testWithRandomNumbers();
    
    // 分析服务器响应
    testServerResponse();
    
    console.log("\n=== 最终结论 ===");
    if (foundTimestamp || foundRandom) {
        console.log("✓ 找到了完整的Authorization生成算法!");
    } else {
        console.log("❌ 未找到完整的客户端生成算法");
        console.log("这很可能是服务器端生成的会话token");
        console.log("建议通过登录接口获取，而不是客户端计算");
    }
}

// 运行测试
runComprehensiveTest();
