@import "..\..\colorui.wxss";

.top-info {
    display: flex;
    justify-content: space-between;
    line-height: 50rpx;
    padding: 30rpx 30rpx 0
}

.right-status {
    color: #09bb07;
    font-size: 28rpx
}

.item {
    background-color: #fff;
    margin: 20rpx auto auto;
    padding-bottom: 10rpx;
    padding-top: 10rpx;
    width: 700rpx
}

.item01,.item02,.item03,.item04 {
    background-color: #fff;
    display: flex;
    height: 40rpx;
    justify-content: space-between;
    line-height: 40rpx;
    margin: auto;
    width: 660rpx
}

.add {
    background-color: #fff;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center
}

.add,.total {
    margin: 20rpx auto auto;
    width: 700rpx
}

.total {
    border-top: 1px solid #cdcdcd;
    padding-bottom: 10rpx;
    padding-top: 10rpx
}

.ckb {
    float: left;
    margin-left: 10rpx;
    width: 50rpx
}

.ckbtext {
    border-bottom: 1px solid #adff2f;
    float: left;
    margin-bottom: 12rpx;
    padding-left: 10rpx;
    padding-right: 10rpx;
    width: 680rpx
}

.coupon {
    height: 80rpx;
    margin-top: 20rpx;
    padding-top: 20rpx
}

.sb,wx-button {
    margin: auto;
    width: 710rpx
}

.sysm {
    margin-bottom: 8rpx
}

.item05 {
    display: flex;
    height: 40rpx;
    justify-content: space-between;
    line-height: 40rpx;
    margin: auto;
    width: 660rpx
}

.btn-wrapper {
    height: 98rpx;
    margin-top: 50rpx;
    padding: 26rpx;
    text-align: right
}

.btn-wrapper wx-button {
    border-radius: 68rpx;
    font-size: 24rpx;
    height: 68rpx;
    line-height: 68rpx;
    width: 170rpx
}

.btn-active {
    color: #d59f6a
}
