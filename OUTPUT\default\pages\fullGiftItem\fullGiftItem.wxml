<view class="top">
    <image class="background" src="https://obs.springland.com.cn/mg-mp/image/20250515_1.png"></image>
    <image class="background2" src="https://obs.springland.com.cn/mg-mp/image/20250515_2.png"></image>
    <view class="rule">
        <image class="img-i" src="../../img/icon/icon_fullgift_1.png"></image>
        <text class="description">活动时间：{{fullGift.startTime}} - {{fullGift.endTime}}</text>
    </view>
    <view class="rule" wx:if="{{fullGift.description}}">
        <image class="img-i" src="../../img/icon/icon_fullgift_2.png"></image>
        <text class="description">活动规则：{{fullGift.description}}</text>
    </view>
</view>
<view class="text-xxl padding-top-xl text-center" wx:if="{{list.length==0}}">礼品已被领取完</view>
<view class="container">
    <view bindtap="goDetail" class="item" data-id="{{it.id}}" wx:for="{{list}}" wx:for-index="idx" wx:for-item="it">
        <image src="{{it.image}}"></image>
        <view class="text">
            <view class="salenum" wx:if="{{fullGift.showStock}}"> 剩余库存: {{it.num-it.receiveNum}} </view>
            <view class="salename">{{it.giftName}}</view>
            <view class="salelimit">
                <block wx:if="{{fullGift.type==1}}">
                    <text wx:if="{{it.moneyQuota>0}}">单笔消费满{{it.moneyQuota}}元
</text>
                    <text wx:if="{{it.cashQuota>0}}">单笔现金消费满{{it.cashQuota}}元
</text>
                    <text wx:if="{{it.integralQuota>0}}">单笔消费产生积分大于{{it.integralQuota}}分</text>
                </block>
                <block wx:else>
                    <text wx:if="{{it.moneyQuota>0}}">单日累计消费满{{it.moneyQuota}}元
</text>
                    <text wx:if="{{it.cashQuota>0}}">单日累计现金消费满{{it.cashQuota}}元
</text>
                    <text wx:if="{{it.integralQuota>0}}">单日累计消费产生积分大于{{it.integralQuota}}分</text>
                </block>
            </view>
        </view>
    </view>
</view>
<back-home homeShow="{{homeShow}}"></back-home>
