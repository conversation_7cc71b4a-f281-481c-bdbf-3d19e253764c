@import "..\..\colorui.wxss";

.select-mall {
    display: flex;
    justify-content: space-between
}

.select-mall .mallname {
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
    line-height: 76rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 314rpx
}

.xiao-tishi {
    border: 2rpx solid hsla(0,0%,100%,.6);
    border-radius: 32rpx;
    color: hsla(0,0%,100%,.8);
    font-size: 18rpx;
    font-weight: 300;
    height: 32rpx;
    line-height: 32rpx;
    margin-top: 10rpx;
    padding: 0 10rpx
}

.xiao-tb {
    flex: 0 0 56rpx;
    margin-top: 10rpx
}

.xiao-tb wx-image {
    height: 56rpx;
    width: 56rpx
}

.top-wrapper {
    background: #2196ff;
    height: 310rpx;
    padding: 0 0 28rpx
}

.yuan-padding {
    padding: 20rpx 22rpx 0
}

.yuan-wrapper {
    background: #66b1f5;
    border-radius: 130rpx;
    display: flex;
    height: 130rpx;
    padding: 0 38rpx 0 20rpx
}

.wenzi-left {
    color: #fff;
    flex: 0 0 64rpx;
    font-size: 80rpx;
    font-weight: 550;
    height: 130rpx;
    line-height: 130rpx;
    width: 52rpx
}

.juti-rigth {
    flex: 1
}

.cc-desc {
    display: flex;
    padding: 10rpx 0
}

.ccms {
    color: #fff;
    flex: 0 0 550rpx;
    font-size: 24rpx;
    font-weight: 300;
    height: 34rpx;
    line-height: 26rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 550rpx
}

.ms-icon {
    flex: 0 0 34rpx
}

.ms-icon wx-image {
    height: 33rpx;
    width: 34rpx
}

.lc-thishi {
    margin-top: 26rpx;
    padding: 0 48rpx
}

.lc-thishi wx-image {
    height: 32rpx;
    vertical-align: top;
    width: 32rpx
}

.lc-thishi wx-text {
    color: hsla(0,0%,100%,.8);
    font-size: 24rpx;
    font-weight: 300;
    height: 32rpx;
    line-height: 32rpx;
    margin-left: 18rpx
}

.xiangxia {
    height: 12rpx;
    margin-left: 8rpx;
    width: 20rpx
}

.chepai-wrapper {
    margin-top: -84rpx;
    padding: 0 24rpx
}

.chepai-con {
    background: #fff;
    border-radius: 34rpx;
    box-shadow: 4px 4px 5px hsla(0,0%,41%,.3);
    padding: 10rpx 0 40rpx
}

.qiehuan-wra {
    margin: 20rpx 0;
    text-align: center
}

.qiehuan {
    border: 1rpx solid #eee;
    border-radius: 60rpx
}

.qiehuan,.qiehuan-item {
    display: inline-block;
    vertical-align: top
}

.qiehuan-item {
    color: #666;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 56rpx
}

.qiehuan-active {
    background: #2196ff;
    border-radius: 60rpx;
    color: #fff
}

.chepai-bd {
    margin-top: 40rpx;
    padding-left: 112rpx;
    text-align: center
}

.chepai-bd-no {
    background: #eee;
    color: #666;
    display: inline-block;
    font-size: 40rpx;
    line-height: 70rpx;
    padding: 0 54rpx;
    vertical-align: top
}

.chepai-xiala {
    height: 18rpx;
    margin: 26rpx;
    vertical-align: top;
    width: 30rpx
}

.chepai-add-tj {
    height: 70rpx;
    margin-left: 12rpx;
    padding: 4rpx 14rpx;
    vertical-align: top;
    width: 90rpx
}

.chepai-bd-ts {
    color: #999;
    margin-bottom: 30rpx
}

.chepai-bd-xz {
    background: #f4b266;
    border-radius: 8rpx;
    display: inline-block;
    padding: 20rpx 36rpx;
    vertical-align: top
}

.chepai-bd-xz wx-image {
    height: 60rpx;
    width: 60rpx
}

.chepai-bd-bdcp {
    font-size: 26rpx;
    margin-top: 8rpx
}

.chepai-input {
    border-bottom: 2rpx solid rgba(48,48,48,.4);
    display: flex;
    margin: 0 54rpx;
    padding: 12rpx 0
}

.chepai-img {
    flex: 0 0 65rpx
}

.chepai-img wx-image {
    height: 32rpx;
    margin-top: 20rpx;
    width: 45rpx
}

.chapai-item {
    border: 2rpx solid rgba(48,48,48,.4);
    border-radius: 8rpx;
    font-size: 40rpx;
    height: 72rpx;
    line-height: 72rpx;
    margin-right: 6rpx;
    text-align: center;
    width: 62rpx
}

.chapai-item:last-child {
    margin-right: 0
}

.xinnengyuan {
    color: rgba(48,48,48,.7);
    font-size: 18rpx;
    line-height: 72rpx
}

.chepai-active {
    border: 2rpx solid #79c0ff;
    color: #79c0ff
}

.chepai {
    background: #fff;
    border: 2rpx solid #79c0ff;
    border-radius: 92rpx;
    font-size: 26rpx;
    height: 92rpx;
    line-height: 92rpx;
    margin: 0 30rpx;
    padding: 0 26rpx;
    position: relative
}

.xuanjc {
    margin-left: 30rpx;
    vertical-align: top
}

.chepaihao,.xuanjc {
    display: inline-block
}

.chepaihao {
    line-height: 92rpx;
    margin: 20rpx 14rpx;
    width: 396rpx
}

.btn-wrapper {
    background: #d8d8d8;
    border-radius: 56rpx;
    color: #fff;
    display: inline-block;
    height: 56rpx;
    line-height: 56rpx;
    padding: 0 22rpx;
    position: absolute;
    right: 36rpx;
    top: 20rpx;
    vertical-align: top
}

.zuijin-chepai {
    display: flex;
    margin-top: 20rpx;
    padding: 8rpx 54rpx
}

.chepai-item:last-child {
    border-right: none
}

.chepai-item {
    border-right: 1px solid hsla(0,0%,44%,.4);
    flex: 0 0 33.33%;
    text-align: center
}

.chepai-item .che-text {
    color: rgba(0,0,0,.6);
    display: inline-block;
    font-size: 28rpx;
    font-weight: 300;
    height: 28rpx;
    line-height: 28rpx;
    position: relative
}

.chepai-item wx-image {
    height: 20rpx;
    margin-left: 14rpx;
    margin-top: 4rpx;
    position: relative;
    vertical-align: top;
    width: 20rpx
}

.query {
    background: #2196ff;
    border-radius: 10rpx;
    color: #fff;
    font-size: 40rpx;
    font-weight: 300;
    height: 80rpx;
    line-height: 80rpx;
    margin: 42rpx auto 0;
    text-align: center;
    width: 480rpx
}

.caozuo-wrapper {
    display: flex;
    flex-flow: row wrap;
    padding: 40rpx 30rpx 0
}

.caozuo {
    flex: 0 0 25%;
    margin-bottom: 30rpx;
    text-align: center
}

.caozuo wx-image {
    display: block;
    height: 64rpx;
    margin: 0 auto 4rpx;
    width: 64rpx
}

.wenzi {
    color: rgba(48,48,48,.4);
    font-size: 28rpx;
    font-weight: 500;
    line-height: 40rpx
}

.pad {
    margin-top: 50rpx;
    padding: 0 30rpx 0 20rpx
}

.xuzhi {
    font-size: 30rpx;
    font-weight: 600;
    padding-bottom: 32rpx;
    text-align: center
}

.title-wenzi {
    font-size: 26rpx;
    line-height: 40rpx
}

.top-margin {
    border-bottom: 2rpx solid rgba(48,48,48,.4);
    margin: 0 auto;
    width: 592rpx
}

.screen-swiper {
    border-radius: 8rpx;
    height: 250rpx;
    margin: 34rpx auto 0;
    min-height: 250rpx;
    overflow: hidden;
    width: 710rpx
}

.screen-swiper wx-image {
    height: 100%;
    width: 100%
}

.equity-wrapper {
    padding: 16rpx 50rpx
}

.equity-info {
    background-color: #fff;
    border-radius: 5rpx;
    display: flex;
    height: 167rpx;
    padding: 8rpx 10rpx
}

.huise .equity-left {
    border-top: 2rpx solid #d8d8d8
}

.huise .equity-left,.huise .radius-top-right {
    border-bottom: 2rpx solid #d8d8d8;
    border-left: 2rpx solid #d8d8d8
}

.huise .radius-bottom-right {
    border-left: 2rpx solid #d8d8d8;
    border-top: 2rpx solid #d8d8d8
}

.huise .jine {
    border-right: 2px dotted #d8d8d8
}

.huise .equity-right {
    background: #d8d8d8!important
}

.huise .lijian,.huise .money {
    color: #d8d8d8
}

.equity-left {
    border-bottom: 2rpx solid #c8aa82;
    border-left: 2rpx solid #c8aa82;
    border-radius: 5rpx;
    border-top: 2rpx solid #c8aa82;
    display: flex;
    flex: 1;
    height: 100%;
    padding: 20rpx 0;
    position: relative
}

.eeee {
    display: block
}

.equity-right {
    align-items: center;
    background: #c8aa82!important;
    border-radius: 5rpx;
    display: flex;
    flex: 0 0 92rpx;
    height: 100%;
    justify-content: center;
    position: relative;
    width: 92rpx
}

.dotted-left {
    border-left: 6rpx dotted #fff;
    bottom: 10rpx;
    left: 0;
    position: absolute;
    top: 10rpx
}

.radius-bottom-right,.radius-top-right {
    background: #fff;
    border: 1px solid #fff;
    height: 10rpx;
    position: absolute;
    width: 10rpx;
    z-index: 1
}

.radius-bottom-left,.radius-top-left {
    background: #fff;
    border: 1px solid #fff;
    height: 8rpx;
    position: absolute;
    width: 8rpx;
    z-index: 1
}

.radius-top-left {
    border-radius: 0 0 8rpx 0;
    left: 0;
    top: 0
}

.radius-top-right {
    border-bottom: 2rpx solid #c8aa82;
    border-left: 2rpx solid #c8aa82;
    border-radius: 0 0 0 10rpx;
    right: -2rpx;
    top: -2rpx
}

.radius-bottom-left {
    border-radius: 0 8rpx 0 0;
    bottom: 0;
    left: 0
}

.radius-bottom-right {
    border-left: 2rpx solid #c8aa82;
    border-radius: 10rpx 0 0 0;
    border-top: 2rpx solid #c8aa82;
    bottom: -2rpx;
    right: -2rpx
}

.receive {
    color: #fff;
    font-size: 28rpx;
    height: auto;
    line-height: 30rpx;
    margin: auto;
    text-align: center;
    vertical-align: middle;
    width: 28rpx
}

.jine {
    border-right: 2px dotted #c8aa82;
    display: flex;
    flex: 0 0 176rpx;
    flex-direction: column;
    justify-content: center;
    width: 176rpx
}

.lijian {
    color: #cfad83;
    font-size: 28rpx;
    height: 60rpx;
    line-height: 30rpx;
    margin-left: 18rpx;
    width: 28rpx
}

.money {
    color: #cfad83;
    font-size: 78rpx;
    height: 78rpx;
    line-height: 78rpx;
    margin-left: 8rpx
}

.rmb {
    font-size: 24rpx
}

.dagai {
    margin: auto 0 auto 32rpx
}

.name-title {
    color: #353535;
    font-size: 28rpx;
    height: 28rpx;
    line-height: 28rpx;
    margin-bottom: 14rpx
}

.yxq {
    color: #9b9b9b;
    font-size: 24rpx;
    height: 24rpx;
    line-height: 24rpx
}

.row-d {
    align-items: center;
    display: flex
}

.common-hy {
    border-bottom: 2rpx solid #c8aa82;
    border-bottom-right-radius: 36rpx;
    border-right: 2rpx solid #c8aa82;
    border-top: 2rpx solid #c8aa82;
    border-top-right-radius: 36rpx;
    color: #c8aa82;
    font-size: 24rpx;
    height: 36rpx;
    line-height: 36rpx;
    margin-bottom: 6rpx;
    text-align: center;
    width: 132rpx
}

.huise .common-hy {
    border-bottom: 2rpx solid #d8d8d8;
    border-right: 2rpx solid #d8d8d8;
    border-top: 2rpx solid #d8d8d8;
    color: #d8d8d8
}

.prime-hy {
    background-color: #c8aa82;
    border-bottom-right-radius: 36rpx;
    border-top-right-radius: 36rpx;
    color: #fff;
    font-size: 24rpx;
    height: 36rpx;
    line-height: 36rpx;
    margin-bottom: 6rpx;
    text-align: center;
    width: 140rpx
}

.huise .prime-hy {
    background-color: #d8d8d8
}

.beijing {
    bottom: 0;
    left: 0;
    opacity: 0;
    position: absolute;
    right: 0;
    top: 0
}

.ccxinxi {
    border-radius: 40rpx;
    width: 672rpx
}

.xinxi-wraee {
    padding: 42rpx 46rpx 12rpx
}

.chechang-title {
    color: #1769b2;
    font-size: 40rpx;
    height: 56rpx;
    line-height: 56rpx;
    margin-bottom: 50rpx
}

.chechang-desc {
    color: #676767;
    font-size: 28rpx;
    font-weight: 300;
    line-height: 48rpx;
    margin-bottom: 30rpx;
    text-align: left
}

.i-known {
    border-top: 2rpx solid hsla(0,0%,44%,.4);
    color: #2196ff;
    font-size: 32rpx;
    font-weight: 300;
    line-height: 80rpx;
    padding-top: 12rpx
}

.btn-aaaa {
    display: flex
}

.xunche {
    padding: 26rpx 50rpx 0 0rpx;
    text-align: center
}

.xunche wx-image {
    display: block;
    height: 64rpx;
    margin: 0 auto 4rpx;
    width: 64rpx
}

.mallcircle-shadow {
    background: rgba(0,0,0,.4);
    height: 2000rpx;
    left: 0;
    position: fixed;
    top: 0;
    width: 750rpx;
    z-index: 10000
}

.mallcircle-box {
    left: 70rpx;
    position: fixed;
    top: 450rpx;
    z-index: 10001
}

.mallcircle-box,.mallcircle-img {
    height: 500rpx;
    width: 610rpx
}

.mallcircle-qx {
    background-color: #fff;
    color: #a3a3a3;
    left: 100rpx
}

.mallcircle-kt,.mallcircle-qx {
    border-radius: 50rpx;
    height: 80rpx;
    line-height: 80rpx;
    position: absolute;
    text-align: center;
    top: 360rpx;
    width: 160rpx
}

.mallcircle-kt {
    background-color: #ff9300;
    color: #fff;
    left: 326rpx
}
