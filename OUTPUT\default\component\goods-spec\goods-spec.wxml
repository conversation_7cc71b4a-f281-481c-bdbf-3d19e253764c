<modal-box class="bottom-modal {{specShow?'show':''}}">
    <view bindtap="hideSpec" class="beijing"></view>
    <dialog class="bg-white">
        <view class="xinxi-wraee">
            <view bindtap="hideSpec" class="close-wra kuoda">
                <image src="/yjh-config/images/shop_close.png"></image>
            </view>
            <view class="top-wra">
                <view class="top-img" wx:if="{{goodsInfo}}">
                    <image src="{{filter.addImgPath350x350(goodsInfo.specPicture)}}" wx:if="{{goodsInfo.specPicture}}"></image>
                    <image src="{{filter.addImgPath350x350(goodsInfo.picture)}}" wx:else></image>
                </view>
                <view class="top-spxx">
                    <view class="sp-title">{{goodsInfo.goodsName}}</view>
                    <view class="sp-yixuan">已选： <block wx:for="{{goodsInfo.goodsSpecList}}" wx:key="type">
                            <text decode="true" wx:if="{{it.choosed}}" wx:for="{{item.values}}" wx:for-item="it" wx:key="value">{{it.value}}&nbsp;</text>
                        </block>
                    </view>
                    <view class="sp-jiage">￥{{filter.priceToFixed2(goodsInfo.price)}}<text wx:if="{{goodsInfo.markingPrice>goodsInfo.price}}">￥{{filter.priceToFixed2(goodsInfo.markingPrice)}}</text>
                    </view>
                </view>
            </view>
            <view class="guige-list">
                <view class="guige-item" data-index="{{index}}" wx:for="{{goodsInfo.goodsSpecList}}" wx:key="type">
                    <view class="guige-title">{{item.type}}</view>
                    <view class="guige-chooses">
                        <view bindtap="tapChooseGuige" class="guige-choose {{it.choosed?'choosed':''}}" data-ind="{{ind}}" data-index="{{index}}" wx:for="{{item.values}}" wx:for-index="ind" wx:for-item="it" wx:key="value">{{it.value}}</view>
                    </view>
                </view>
            </view>
            <view class="xuanhaole">
                <view bindtap="xuanhaole" class="xuanhaole-btn">选好了</view>
            </view>
        </view>
    </dialog>
</modal-box>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>