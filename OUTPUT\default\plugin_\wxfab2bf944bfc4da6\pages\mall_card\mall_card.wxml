<view class="plugin-mall-card">
    <navigate-bar></navigate-bar>
    <view class="mall-card-body exposure-report-dom" data-exposure-data="{{({event_id:100000})}}" hidden="{{!loadStatus}}" id="mall-card-body">
        <image class="score-background" mode="widthFix" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/card_header_bg_v3.png"></image>
        <view class="mall-header-img">
            <image class="score-bg-text" mode="widthFix" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/{{cardOpenStatus?mallServiceUpgradeStatus?'mall_upgrade_text_v3.png':'mall_score_text_v3.png':'card_score_text_v3.png'}}"></image>
        </view>
        <view class="score-card-info">
            <view catchtap="switchCardServiceAuth" class="card-benefit" wx:if="{{!cardOpenStatus}}">
                <view class="card-benefit-list" wx:if="{{cardServiceAuth}}">
                    <view class="benefit-name">
                        <image class="card-benefit-ico" mode="aspectFit" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-wechat-pay.png"></image>
                        <text>快速积分</text>
                    </view>
                    <view class="benefit-name">
                        <image class="card-benefit-ico" mode="aspectFit" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-wechat-park.png"></image>
                        <text>停车服务</text>
                    </view>
                    <view class="benefit-name">
                        <image class="card-benefit-ico" mode="aspectFit" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-wechat-green.png"></image>
                        <text>门店服务</text>
                    </view>
                </view>
                <view class="no-card-benefit" wx:else>会员专属服务未授权</view>
                <view class="more-benefit"></view>
            </view>
            <view class="mall-card-info">
                <image class="mall-card-background" mode="aspectFill" src="{{memberCardInfo.card_background}}"></image>
                <view class="mall-card-mask"></view>
                <view class="mall-card-name">
                    <image class="mall-logo" src="{{memberCardInfo.card_logo}}"></image>
                    <text class="mall-name">{{memberCardInfo.card_name}}</text>
                </view>
            </view>
            <view class="card-operate {{cardOpenStatus?'':'card-open-operate'}}">
                <mall-member bind:openMallMember="openPayAuthDialog" mallServiceUpgradeStatus="{{mallServiceUpgradeStatus}}" mchCode="{{mchCode}}" memberCardInfo="{{memberCardInfo}}" openId="{{openId}}" wx:if="{{cardOpenStatus}}"></mall-member>
                <card-member bind:openMallCard="openPayAuthDialog" bind:updateCardFieldList="updateCardFieldList" bind:updatePhoneValidCode="updatePhoneValidCode" cardFieldList="{{memberCardInfo.card_field_list}}" cardServiceAuth="{{cardServiceAuth}}" mchCode="{{mchCode}}" openId="{{openId}}" wx:else></card-member>
            </view>
        </view>
        <half-dialog show="{{payAuthDialog}}" showClose="{{false}}">
            <view class="pay-record-auth">
                <image class="pay-record-auth-logo" mode="aspectFit" src="{{memberCardInfo.card_logo}}"></image>
                <text>{{memberCardInfo.main_brand_name}}</text>
            </view>
            <text class="pay-record-content">申请获取你在商圈内的微信支付消费记录</text>
            <text class="pay-record-desc">获取后你无需再拍摄小票积分或到服务台积分，系统将帮你快速进行商圈会员积分。</text>
            <view class="pay-record-button">
                <button bindtap="cancelPayAuth" class="cancel-button" type="default">取消</button>
                <button bindtap="confirmPayAuth" class="confirm-button" type="primary">允许</button>
            </view>
        </half-dialog>
        <half-dialog show="{{cardOpenResultDialog}}" showClose="{{false}}">
            <view class="card-open-status">
                <icon class="card-open-status-icon" color="#07C160" size="51" type="success"></icon>
                <text class="card-open-status-text">{{cardOpenStatus&&mallServiceUpgradeStatus?'升级':'开通'}}成功</text>
                <text class="card-open-status-desc">可在「我-微信卡包」里查看会员卡</text>
                <button bindtap="cardOpenResultClose" class="card-open-status-button" type="default">知道了</button>
            </view>
        </half-dialog>
        <half-dialog bind:dialogClose="closeHalfDialog" data-dialog="cardServiceAuthDialog" show="{{cardServiceAuthDialog}}" title="会员专属服务">
            <view class="card-banefit-auth">
                <member-benefit></member-benefit>
            </view>
            <button bindtap="cancelCardServiceAuth" class="upgrade-cancel-button" wx:if="{{cardServiceAuth}}">不授权该服务</button>
            <button bindtap="saveCardServiceAuthStatuus" class="upgrade-confirm-button" wx:else>立即授权</button>
        </half-dialog>
    </view>
</view>
