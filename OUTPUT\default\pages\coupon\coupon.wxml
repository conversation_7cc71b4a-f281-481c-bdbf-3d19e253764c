<view class="top-wrapper">
    <select-mall bind:fireReSearch="fireReSearch" class="dingbu" mallName="{{mallName}}" mdid="{{mdid}}"></select-mall>
</view>
<image class="image-info" src="http://picture.springland.com.cn:8101/static/images/advert/a1.jpg?w=750"></image>
<view class="lq-tab solid-bottom">
    <view class="lq-item solid-right">
        <image class="coupon-icon" src="../../img/icon/<EMAIL>"></image> 领券中心</view>
    <view bindtap="goMyCoupon" class="lq-item moren">
        <image class="coupon-icon" src="../../img/icon/<EMAIL>"></image> 我的券包</view>
    <view bindtap="goRechargeCode" class="lq-item moren">
        <image class="coupon-icon" src="../../img/icon/czqm.png"></image> 充值券码</view>
</view>
<view class="classify-wrapper">
    <view bindtap="tabSelect" class="all-classify solid-right {{0==TabCur?'text-green cur cust-color':''}}" data-id="0">
        <view class="{{0==TabCur?'aaa':''}}">全部</view>
    </view>
    <scroll-view scrollWithAnimation scrollX class="bg-white nav scroll-wrapper" scrollLeft="{{scrollLeft}}">
        <item bindtap="tabSelect" class="{{index==TabCur?'text-green cur cust-color':''}}" data-id="{{index}}" wx:if="{{index!=0}}" wx:for="{{classifyList}}"> {{item.flmc}} </item>
    </scroll-view>
    <view bindtap="modelChange" class="jiantou solid-left">
        <view class="xuanzhuan">
            <text class="icon-right lg text-gray cus"></text>
        </view>
    </view>
    <modal-box bindtap="hideModal" class="{{modelShow?'show':''}}"></modal-box>
    <view class="select-wrapper {{modelShow?'showModel':''}}">
        <view class="qxz-wrapper solid-bottom">
            <view class="qxz">请选择</view>
            <view bindtap="modelChange" class="jiantou solid-left">
                <view class="xuanzhuan1">
                    <text class="icon-right lg text-gray cus"></text>
                </view>
            </view>
        </view>
        <view class="items-wrapper">
            <view bindtap="tabSelect" class="select-item {{index==TabCur?'activea':''}}" data-id="{{index}}" wx:for="{{classifyList}}">{{item.flmc}}</view>
            <view class="select-none" wx:for="{{noneLength}}"></view>
        </view>
    </view>
</view>
<view class="margin-cust">
    <coupon-list bind:fireCouponEvent="fireEvent" bind:fireDetail="goDetail" btnText="领取" couponList="{{couponList}}" hasPhone="{{hasPhone}}" isUse="{{isUse}}"></coupon-list>
</view>
<modal-pay-coupon bindnumMinus="numMinus" bindnumPlus="numPlus" bindpayevent="pay" canBuy="{{canBuy}}" disabled="{{disabled}}" isShow="{{isModalShow}}" num="{{couponList[selectIndex].qty}}" price="{{couponList[selectIndex].gmje}}" saleName="{{couponList[selectIndex].bt}}"></modal-pay-coupon>
