.we-emoji-panel__wx-Awesome {
    width: 64px;
    height: 64px;
    background-position: 0 -668px
}

.we-emoji-panel__wx-Boring {
    width: 64px;
    height: 64px;
    background-position: -74px -668px
}

.we-emoji-panel__wx-Sigh {
    width: 64px;
    height: 64px;
    background-position: -518px -668px
}

.we-emoji-panel__wx-KeepFighting {
    width: 64px;
    height: 64px;
    background-position: -222px -668px
}

.we-emoji-panel__wx-Hurt {
    width: 64px;
    height: 64px;
    background-position: -296px -668px
}

.we-emoji-panel__wx-Broken {
    width: 64px;
    height: 64px;
    background-position: -370px -668px
}

.we-emoji-panel__wx-LetMeSee {
    width: 64px;
    height: 64px;
    background-position: -444px -668px
}

.we-emoji-panel__wx-16 {
    width: 64px;
    height: 64px;
    background-position: -148px -668px
}

.weui-emotion_list {
    text-align: center;
    padding: 0 10px 30px;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    white-space: normal;
    font-size: 0
}

.weui-emotion_item {
    display: inline-block;
    position: relative;
    padding: 0 4px 5px;
    box-sizing: content-box!important;
    line-height: 40px;
    vertical-align: middle;
    width: 40px;
    height: 40px;
    background-position: 50%;
    background-size: 70%;
    background-repeat: no-repeat
}

.weui-emotion_recently .weui-emotion_page {
    height: 45px;
    overflow-y: hidden
}

.weui-icon_emotion {
    position: absolute;
    top: 4px;
    left: 4px;
    display: inline-block;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scale(.5);
    transform: scale(.5)
}

.weui-emotion_head {
    margin: 14px 10px 8px;
    color: #d1d1d1;
    font-size: 14px;
    z-index: 5004;
    text-align: left
}

.weui-emoji_area {
    position: relative;
    width: 100%;
    z-index: 5004;
    pointer-events: auto;
    overflow: hidden
}

.weui-emoji_area:before {
    position: absolute;
    content: " ";
    display: block;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,.7);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    margin: -30px
}

.weui-emotion_page {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.weui-emotion_body-all {
    padding-bottom: 48px
}

.weui-emotion_del_btn {
    display: inline-block;
    width: 24px;
    height: 24px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    background: url(https://res.wx.qq.com/a/fed_upload/4e4fc31b-48d0-4b33-be9c-9aaada4e10da/Icons_Outlined_DelEmoji.png) no-repeat 50%/contain
}

.weui-emoji__operation {
    position: fixed;
    bottom: 20px;
    right: 9px;
    height: 44px;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}

.weui-emoji__operation__delete,.weui-emoji__operation__send {
    display: inline-block;
    width: 58px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    font-size: 14px
}

.weui-emoji__operation__delete {
    position: relative;
    font-size: 0;
    background-color: #2c2c2c
}

.weui-emoji__operation__delete.disabled {
    color: #303030;
    background-color: #2c2c2c
}

.weui-emoji__operation__send.disabled {
    color: hsla(0,0%,100%,.5);
    background-color: #472721
}

.disabled .weui-emotion_del_btn {
    opacity: .1
}

.weui-emoji__operation__send {
    margin-left: 8px;
    background-color: var(--color-theme);
    font-weight: 450;
    color: #fff
}

.weui-emoji_area-safe-placeholder {
    height: constant(safe-area-inset-bottom);
    height: env(safe-area-inset-bottom)
}

.weui-emoji_area__has-safe-bar .weui-emoji__operation {
    position: fixed;
    bottom: 38px
}

.weui-emoji_area__platform-not-ios:before {
    background-color: rgba(0,0,0,.95);
    -webkit-backdrop-filter: none;
    backdrop-filter: none
}
