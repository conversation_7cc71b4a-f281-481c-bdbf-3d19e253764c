@import "..\..\wxss\style.wxss";

wx-bar,wx-button,wx-capsule,wx-chat,wx-custom,wx-form,wx-form-group,wx-image,wx-info,wx-input,wx-item,wx-label,wx-list,wx-navigator,wx-progress-bar,wx-scroll-view,wx-swiper,wx-tag,wx-text,wx-textarea,wx-timeline,wx-view {
    box-sizing: initial
}

#bh_shop_cart wx-checkbox,#bh_shop_cart wx-radio,#bh_shop_cart wx-switch {
    position: relative
}

#bh_shop_cart wx-checkbox::before,#bh_shop_cart wx-radio::before {
    content: ""
}

page {
    background: #f5f5f5;
    padding-bottom: 120rpx
}

.container {
    padding: 0rpx
}

.head {
    border-bottom: 2rpx solid #f7f7f7;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 18rpx
}

.hd_num {
    color: #8c8c8c;
    float: left
}

.hd_num,.hd_rt {
    font-size: 24rpx
}

.hd_rt {
    color: #282828;
    float: right;
    font-weight: 700
}

.hd_icon {
    float: left;
    height: 24rpx;
    margin-right: 10rpx;
    margin-top: 28rpx;
    width: 24rpx
}

.hd_rt_ok {
    color: #b1261e
}

.shop_none {
    margin-bottom: 80rpx;
    margin-top: 200rpx
}

.shop_bg {
    display: block;
    height: 196rpx;
    margin: 0 auto;
    width: 196rpx
}

.shop_text {
    color: #666;
    font-size: 28rpx;
    margin-top: 50rpx;
    text-align: center
}

.post_explain {
    color: #b1261e;
    float: left;
    text-align: center;
    width: 260rpx
}

.goHome {
    background: none;
    border: 2rpx solid #c8aa82;
    border-radius: 42rpx;
    color: #c8aa82;
    display: block;
    font-size: 32rpx;
    height: 76rpx;
    line-height: 76rpx;
    margin: 50rpx auto 0;
    padding: 0;
    width: 232rpx
}

.checkbox {
    color: #666;
    display: inline-block;
    font-size: 28rpx;
    height: 100rpx;
    line-height: 100rpx
}

wx-checkbox .wx-checkbox-input {
    border-radius: 50rpx;
    box-sizing: border-box;
    height: 36rpx;
    width: 36rpx
}

wx-checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    background: #c8aa82;
    border: none;
    box-sizing: border-box
}

wx-checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
    background: transparent;
    color: #fff;
    font-size: 22rpx;
    height: 28rpx;
    line-height: 28rpx;
    text-align: center;
    transform: translate(-50%,-50%) scale(1);
    -webkit-transform: translate(-50%,-50%) scale(1);
    width: 28rpx
}

.goods_flex wx-checkbox .wx-checkbox-input {
    margin-top: -6rpx
}

.goods {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx
}

.goods_list {
    background: #fff;
    height: 200rpx;
    position: relative;
    z-index: 999
}

.goods_list:last-child {
    border-bottom: none
}

.goods_icon {
    float: left;
    height: 32rpx;
    margin-right: 10rpx;
    margin-top: 4rpx;
    width: 32rpx
}

.goods_checkbox,.goods_checkbox_all {
    float: left;
    width: 60rpx
}

.goods_checkbox,.goods_img {
    height: 200rpx
}

.goods_img {
    float: left;
    margin-right: 20rpx;
    width: 200rpx
}

.back_img {
    position: absolute
}

.back_img,.invalid_back_img {
    display: block;
    max-width: 100%;
    top: 0rpx
}

.invalid_back_img {
    left: -220rpx;
    position: relative
}

.goods_title {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #333;
    display: -webkit-box;
    font-size: 22rpx;
    font-weight: 700;
    height: 64rpx;
    line-height: 32rpx;
    overflow: hidden;
    text-align: left;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis
}

.goods_title_tip {
    background: #222;
    border-radius: 2rpx;
    color: #d59f6a;
    display: inline-block;
    font-size: 16rpx;
    height: 32rpx;
    line-height: 32rpx;
    margin-right: 6rpx;
    padding: 0 6rpx;
    vertical-align: middle
}

.goods_text {
    color: #b1261e;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx;
    margin-bottom: 8rpx;
    margin-top: 58rpx
}

.goods_subtitle {
    color: #b1261e;
    font-size: 24rpx;
    font-weight: 700;
    height: 44rpx;
    line-height: 44rpx;
    text-align: left
}

.goods_subtitle wx-text {
    font-size: 32rpx
}

.goods_choose {
    align-items: center;
    bottom: 0;
    display: flex;
    position: absolute;
    right: 0
}

.goods_add {
    display: inline-block;
    height: 32rpx;
    width: 32rpx
}

.goods_add wx-image {
    display: block;
    height: 32rpx;
    width: 32rpx
}

.goods_int {
    border-radius: 4rpx;
    color: #666;
    font-size: 24rpx;
    height: 32rpx;
    line-height: 32rpx;
    margin: 0 10rpx;
    text-align: center;
    width: 60rpx
}

.goods_invalid {
    background: #fff;
    border-radius: 10rpx
}

.invalid_title {
    box-sizing: initial;
    font-size: 24rpx;
    font-weight: 700;
    height: 40rpx;
    line-height: 40rpx;
    padding: 20rpx 18rpx;
    position: relative
}

.goods_ticket {
    background: #d59f6a;
    border-radius: 15rpx;
    color: #fff;
    font-size: 16rpx;
    font-weight: 400;
    height: 30rpx;
    line-height: 30rpx;
    padding: 5rpx;
    position: absolute;
    right: 18rpx;
    text-align: center;
    top: 25rpx;
    width: 60rpx
}

.invalid_title wx-text {
    color: #d59f6a;
    float: right;
    font-weight: 400
}

.invalid_bg {
    margin-bottom: 30rpx;
    padding: 0 20rpx
}

.invalid_bg wx-view {
    color: #8c8c8c
}

.shop_bottom {
    padding-bottom: 120rpx
}

.dialog_shop {
    background: #fff;
    border-radius: 12rpx;
    height: 288rpx;
    left: 12%;
    overflow: hidden;
    position: fixed;
    top: 30%;
    width: 76%;
    z-index: 1001
}

.dialog_title {
    color: #333;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
    margin: 73rpx auto;
    text-align: center
}

.dialog_shop_btn {
    display: flex
}

.dialog_shop_lt {
    box-shadow: 0 -2px 0 0 #f3f3f3
}

.dialog_shop_lt,.dialog_shop_rt {
    flex: 1;
    font-size: 28rpx;
    height: 100rpx;
    line-height: 100rpx;
    text-align: center
}

.dialog_shop_rt {
    background: #b1261e;
    color: #fff
}

.goods_fixed {
    background: #fff;
    border-top: 1rpx solid #ddd;
    bottom: 0;
    box-sizing: border-box;
    display: flex;
    height: 150rpx;
    left: 0;
    line-height: 100rpx;
    position: fixed;
    width: 100%;
    z-index: 10001
}

.goods_flex {
    color: #c8aa82;
    flex: 2;
    font-size: 32rpx;
    line-height: 100rpx;
    margin-left: 20rpx
}

.goods_cart {
    color: #333;
    float: right;
    font-size: 32rpx;
    padding-left: 30rpx;
    text-align: left
}

.goods_total {
    color: #666;
    float: left;
    font-size: 24rpx;
    height: 100rpx;
    line-height: 100rpx;
    margin-right: 20rpx
}

.goods_total wx-text {
    color: #282828;
    font-size: 36rpx;
    font-weight: 700
}

.goods_discount {
    color: #666;
    line-height: 50rpx;
    margin-top: 8rpx
}

.goods_discount wx-text {
    color: #b1261e
}

.goods_dis_text {
    color: #b1261e;
    font-size: 20rpx;
    line-height: 28rpx;
    text-align: right
}

.goods_dis_arrow {
    float: right;
    height: 12rpx;
    margin-left: 8rpx;
    margin-top: 8rpx;
    width: 22rpx
}

.goods_btn {
    background: #c8aa82;
    box-sizing: border-box;
    color: #fff;
    font-size: 28rpx;
    line-height: 100rpx;
    text-align: center;
    width: 240rpx
}

.goods_btn,.goods_del {
    float: right;
    height: 100rpx
}

.goods_del_btn {
    border: 2rpx solid #b1261e;
    border-radius: 33rpx;
    color: #b1261e
}

.goods_del_btn,.goods_del_btn01 {
    box-sizing: border-box;
    float: right;
    font-size: 28rpx;
    height: 64rpx;
    line-height: 64rpx;
    margin-right: 20rpx;
    margin-top: 18rpx;
    padding: 0 42rpx
}

.goods_del_btn01 {
    border: 2rpx solid #d59f6a;
    border-radius: 33rpx;
    color: #d59f6a
}

.list {
    height: 200rpx;
    line-height: 200rpx;
    margin: 0 18rpx;
    padding-bottom: 30rpx
}

.list,.list_del.txt {
    overflow: hidden;
    position: relative
}

.list_del.txt {
    background-color: #fff;
    text-overflow: ellipsis;
    transition: left .2s ease-in-out;
    width: 100%;
    z-index: 5
}

.list_del.del {
    background-color: #eb0d00;
    color: #fff;
    height: 200rpx;
    line-height: 200rpx;
    right: 0;
    text-align: center;
    width: 140rpx;
    z-index: 4
}

.list_del {
    position: absolute;
    right: 20rpx;
    top: 0rpx
}

.goods_act {
    color: #282828;
    font-size: 22rpx;
    margin-bottom: 24rpx;
    margin-left: 80rpx;
    padding-right: 18rpx
}

.goods_act,.goods_act_lt {
    height: 34rpx;
    line-height: 34rpx
}

.goods_act_lt {
    float: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 580rpx
}

.goods_act_text {
    border: 2rpx solid #d59f6a;
    border-radius: 4rpx;
    box-sizing: border-box;
    color: #d59f6a;
    float: left;
    font-size: 18rpx;
    height: 30rpx;
    line-height: 30rpx;
    margin-right: 8rpx;
    padding: 0 8rpx
}

.goods_act_rt {
    color: #d59f6a;
    float: right;
    height: 34rpx;
    line-height: 34rpx
}

.goods_checkbox_no {
    background: #f5f5f8;
    border-radius: 13rpx;
    display: inline-block;
    font-size: 16rpx;
    height: 26rpx;
    line-height: 26rpx;
    margin-top: 87rpx;
    padding: 0 10rpx;
    text-align: center
}

.bar_good_title {
    margin-bottom: 16rpx;
    margin-top: 36rpx
}

.bar_good_bg {
    display: block;
    width: 100%
}

.content_goods {
    padding: 20rpx 18rpx
}

.car_goods {
    background: #fff;
    border-radius: 12rpx;
    float: left;
    margin-bottom: 20rpx;
    overflow: hidden;
    width: 348rpx
}

.car_goods:nth-child(even) {
    float: right
}

.car_goods_img {
    position: relative
}

.car_goods_image {
    display: block;
    margin: 0 auto;
    width: 100%
}

.car_goods_title {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: 20rpx;
    height: 56rpx;
    line-height: 28rpx;
    overflow: hidden;
    padding: 16rpx;
    text-overflow: -o-ellipsis-lastline;
    text-overflow: ellipsis
}

.car_goods_label {
    font-size: 16rpx;
    padding: 0 16rpx
}

.car_goods_label wx-text {
    border: 2rpx solid #b1261e;
    color: #b1261e;
    display: inline-block;
    height: 26rpx;
    line-height: 26rpx;
    padding: 0 6rpx
}

.car_goods_price {
    color: #c6c2be;
    font-size: 24rpx;
    margin: 10rpx 16rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.car_goods_price wx-text:first-child {
    color: #b1261e
}

.car_goods_price wx-text:last-child {
    color: #c6c2be;
    font-size: 20rpx;
    margin-left: 10rpx;
    text-decoration: line-through
}

.coupon_popup {
    background: #fff;
    bottom: 0;
    left: 0;
    position: fixed;
    width: 100%;
    z-index: 1001
}

.coupon_hd {
    line-height: 42rpx;
    padding: 40rpx 0 20rpx;
    position: relative;
    text-align: center
}

.shop_close {
    height: 32rpx;
    position: absolute;
    right: 40rpx;
    top: 45rpx;
    width: 32rpx
}

.coupon_content {
    height: 682rpx;
    overflow: auto;
    padding: 0 18rpx 50rpx
}

.con_list {
    border-radius: 20rpx;
    height: 200rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    position: relative
}

.con_list:last-child {
    margin-bottom: 0
}

.con_coupon_bg {
    display: block;
    height: 200rpx;
    width: 100%
}

.con_title {
    color: #d59f6a;
    font-size: 32rpx;
    height: 80rpx;
    left: 26rpx;
    line-height: 80rpx;
    position: absolute;
    top: 24rpx
}

.con_title wx-text {
    font-size: 56rpx
}

.con_title wx-text:last-child {
    font-size: 24rpx;
    margin-left: 8rpx
}

.con_subtitle {
    top: 108rpx
}

.con_num,.con_subtitle {
    color: #d59f6a;
    font-size: 20rpx;
    height: 28rpx;
    left: 26rpx;
    line-height: 28rpx;
    position: absolute
}

.con_num {
    top: 140rpx
}

.con_btn {
    background: #d59f6a;
    border: 2rpx solid #d59f6a;
    border-radius: 30rpx;
    color: #fff;
    font-size: 20rpx;
    height: 44rpx;
    line-height: 44rpx;
    position: absolute;
    right: 26rpx;
    text-align: center;
    top: 78rpx;
    width: 124rpx
}

.con_btn01 {
    background: transparent;
    border: 2rpx solid transparent;
    color: hsla(30,56%,63%,.6);
    font-size: 28rpx
}

.coupon_btn {
    background: #d59f6a;
    border-radius: 50rpx;
    color: #fff;
    height: 90rpx;
    line-height: 90rpx;
    margin: 28rpx auto;
    text-align: center;
    width: 710rpx
}

.coupon_dis {
    background: #fff;
    bottom: 100rpx;
    left: 0;
    position: fixed;
    width: 100%;
    z-index: 1001
}

.dis_title {
    color: #8c8c8c;
    font-size: 24rpx;
    line-height: 34rpx;
    text-align: center
}

.dis_sub {
    margin-top: 30rpx
}

.dis_subtext {
    font-size: 28rpx;
    line-height: 40rpx;
    margin-bottom: 20rpx
}

.dis_subtext wx-text {
    float: right;
    font-weight: 700
}

.dis_deta {
    margin-top: 40rpx
}

.dis_detatext {
    font-size: 32rpx;
    font-weight: 700;
    line-height: 40rpx;
    margin-bottom: 20rpx
}

.dis_detatext wx-text {
    float: right;
    font-size: 28rpx
}

.dis_on {
    color: #b1261e
}

.list_lt_label {
    background: #000;
    border-radius: 24rpx;
    left: 400rpx;
    line-height: 15rpx;
    position: absolute;
    top: 34rpx
}

.list_lt_label wx-text {
    background: hsla(0,0%,100%,.2);
    border-radius: 14rpx;
    color: #d59f6a;
    display: inline-block;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx;
    padding: 0 8rpx
}

.goods_fixed_ios,.hd_choose {
    background: #fff
}

.hd_choose {
    border-bottom: 2rpx solid #f5f5f5;
    height: 80rpx;
    line-height: 80rpx;
    padding-bottom: 20rpx;
    padding-right: 18rpx
}

.hd_tab {
    display: flex;
    float: left
}

.hd_tab_title {
    flex: 1;
    font-size: 28rpx;
    text-align: center;
    width: 180rpx
}

.active {
    color: #d59f6a;
    position: relative
}

.active::after {
    background: #d59f6a;
    border-radius: 3rpx;
    bottom: 0;
    content: "";
    height: 6rpx;
    left: 30%;
    position: absolute;
    width: 40%
}

.hd_city {
    background: #fff;
    height: 68rpx;
    line-height: 68rpx;
    margin-bottom: 20rpx;
    padding: 0 18rpx
}

.city_cur {
    color: #d59f6a;
    float: left
}

.city_cho,.city_cur {
    font-size: 28rpx;
    height: 68rpx;
    line-height: 68rpx
}

.city_cho {
    color: #666;
    float: right
}

.city_icon {
    float: left;
    height: 32rpx;
    margin-right: 6rpx;
    margin-top: 18rpx;
    width: 32rpx
}

.city_title {
    height: 20rpx;
    line-height: 20rpx;
    padding: 0 58rpx 40rpx;
    position: relative
}

.city_img {
    bottom: 15rpx;
    height: 6rpx;
    left: 78rpx;
    position: absolute;
    width: 46rpx
}

.city_content {
    height: 622rpx;
    overflow: auto;
    padding: 0 58rpx
}

.city_list {
    float: left;
    font-size: 28rpx;
    line-height: 70rpx
}

wx-radio .wx-radio-input {
    border-radius: 50%;
    height: 36rpx;
    width: 36rpx
}

wx-radio .wx-radio-input.wx-radio-input-checked {
    background: #c8aa82;
    border-color: #c8aa82
}

wx-radio .wx-radio-input.wx-radio-input-checked::before {
    background: #c8aa82;
    border-radius: 50%;
    color: #fff;
    font-size: 30rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    transform: translate(-50%,-50%) scale(1);
    -webkit-transform: translate(-50%,-50%) scale(1);
    width: 36rpx
}

.radio wx-radio {
    float: left;
    margin-top: -6rpx
}

.radio_list {
    height: 70rpx;
    line-height: 70rpx
}

.goods_act wx-navigator {
    height: 34rpx!important
}

.posi-rea {
    position: relative
}

.no_stock,.sp_38jie {
    display: block;
    position: absolute;
    top: 0rpx
}

.no_stock {
    opacity: .7
}

.text-bold {
    font-size: 30rpx;
    font-weight: 700
}

.qjs-wra {
    background: #f5f5f8;
    bottom: 0;
    z-index: 20001
}

.qjs-wra .coupon_content {
    height: 850rpx
}

.jiesuan-wra {
    background: #fff;
    border-radius: 16rpx;
    margin-top: 16rpx;
    padding: 20rpx
}

.jiesuan-store {
    font-size: 30rpx;
    font-weight: 700
}

.jiesuan-imgs {
    margin-top: 20rpx;
    overflow: hidden;
    white-space: nowrap;
    width: 674rpx
}

.jiesuan-imgs wx-image {
    border-radius: 8rpx;
    height: 100rpx;
    margin-right: 14rpx;
    width: 100rpx
}

.jiesuan-bottom {
    line-height: 60rpx;
    margin-top: 12rpx;
    padding: 10rpx 0 0;
    text-align: right
}

.jiesuan-yhe {
    color: #999;
    margin-right: 14rpx
}

.jiesuan-hj,.jiesuan-yhe {
    font-size: 24rpx
}

.jiesuan-je {
    color: #b1261e;
    font-size: 34rpx;
    margin-right: 14rpx
}

.jiesuan-btn {
    background: #c8aa82;
    border-radius: 60rpx;
    color: #fff;
    display: inline-block;
    font-size: 28rpx;
    padding: 0 24rpx;
    position: relative
}

.kuoda::after {
    bottom: -20rpx;
    content: "";
    left: -20rpx;
    position: absolute;
    right: -20rpx;
    top: -20rpx
}

.login-tip-container {
    align-items: center;
    background-color: #ffeeba;
    border-radius: 10rpx;
    display: flex;
    justify-content: center;
    margin: 10rpx;
    padding: 15rpx 20rpx
}

.login-tip-text {
    color: #666;
    font-size: 28rpx;
    margin-right: 10rpx
}

.login-btn {
    border: 1rpx solid #f40;
    border-radius: 20rpx;
    color: #f40;
    cursor: pointer;
    font-size: 28rpx;
    padding: 5rpx 15rpx
}
