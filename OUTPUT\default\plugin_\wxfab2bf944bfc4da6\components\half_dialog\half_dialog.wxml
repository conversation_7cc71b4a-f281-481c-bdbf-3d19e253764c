<view class="component-half-dialog" hidden="{{!rootDomShow}}">
    <view catchtouchmove="touchmove" class="component-half-dialog-mask {{show?'weui-animate-fade-in':'weui-animate-fade-out'}}"></view>
    <view bindanimationend="hideRootDom" class="component-half-dialog-container {{show?'weui-animate-slide-up':'weui-animate-slide-down'}}">
        <view catchtouchmove="touchmove" class="component-half-dialog-header" wx:if="{{showClose||title}}">
            <view class="component-half-dialog-title">
                <view class="component-half-dialog-slot-title">
                    <slot name="title"></slot>
                </view>
                <view class="component-half-dialog-default-title">{{title}}</view>
            </view>
            <view bind:tap="closeDialog" class="component-half-dialog-close" wx:if="{{showClose}}">×</view>
        </view>
        <view class="component-half-dialog-body">
            <slot></slot>
        </view>
    </view>
</view>
