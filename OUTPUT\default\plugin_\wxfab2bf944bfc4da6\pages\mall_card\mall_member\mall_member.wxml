<view class="component-mall-member">
    <view class="mall-member-body">
        <view class="mall-member-title">
            <view class="member-title-left"></view> 会员专属服务 <view class="member-title-right"></view>
        </view>
        <member-benefit></member-benefit>
    </view>
    <view class="mall-member-operate">
        <view class="protocol-agree">
            <view bind:tap="switchProtocolCheck" class="protocol-agree-box">
                <radio checked="{{protocolCheck}}" class="protocol-radio"></radio> 我已阅读并同意 </view>
            <navigator class="protocol-link" hoverClass="none" url="../auth_protocol/auth_protocol?openid={{openId}}&mch_id={{mchCode}}">《会员功能服务协议》</navigator>
        </view>
        <button bind:tap="confirmOpenMallMember" class="confirm-buttom {{protocolCheck?'':'disabled'}}" data-report-data="{{({event_id:mallServiceUpgradeStatus?1002:1001})}}" disabled="{{!protocolCheck}}" type="primary">立即{{mallServiceUpgradeStatus?'升级':'开通'}}</button>
    </view>
</view>
