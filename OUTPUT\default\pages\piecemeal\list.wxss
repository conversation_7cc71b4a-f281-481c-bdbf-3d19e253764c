@import "..\..\colorui.wxss";

.cust-color {
    color: #c8aa82!important
}

.top-tab {
    background: #fff;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 10
}

.margin-cust {
    margin-top: 90rpx
}

.coupon-item {
    padding: 30rpx 30rpx 0 20rpx;
    width: 100%
}

.xian {
    background: #c8aa82;
    display: flex;
    height: 200rpx
}

.right-wrapper {
    background: #fff;
    flex: 1;
    padding: 20rpx;
    position: relative
}

.coupon-title {
    color: #000;
    font-size: 28rpx;
    font-weight: 500;
    height: 40rpx
}

.tap-kuoda {
    bottom: 0rpx;
    padding: 20rpx;
    position: absolute;
    right: 0
}

.shiyong {
    line-height: 34rpx;
    width: 150rpx
}

.invoice,.shiyong {
    background: #fff;
    border: 1rpx solid #c8aa82;
    border-radius: 35rpx;
    color: #c8aa82;
    font-size: 22rpx;
    height: 47rpx;
    text-align: center
}

.invoice {
    line-height: 47rpx;
    width: 193rpx
}

.unuse {
    border: 1rpx solid #d8d8d8;
    color: #d8d8d8
}

.no-data {
    margin-top: 300rpx;
    text-align: center
}
