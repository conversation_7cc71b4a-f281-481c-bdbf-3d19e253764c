@import "..\..\colorui.wxss";

.head-topa {
    padding-top: 130rpx
}

.head {
    background: #6cb55a;
    display: flex;
    flex-direction: column;
    height: auto;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000
}

.select-mall {
    display: flex;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0;
    vertical-align: center
}

.head_search_con {
    box-sizing: border-box;
    padding: 16rpx 0;
    position: relative;
    right: auto;
    top: auto;
    width: 100%;
    z-index: 100
}

.head_seach {
    background: #f4f4f4;
    border-radius: 50rpx;
    box-sizing: border-box;
    font-size: 24rpx;
    height: 56rpx;
    line-height: 56rpx;
    margin: 0 20rpx;
    padding: 0 28rpx 0 68rpx
}

.head_seach_icon {
    height: 32rpx;
    left: 40rpx;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 32rpx
}

.prime-wrapper {
    padding: 10rpx 20rpx
}

.prime {
    background: linear-gradient(180deg,#89b841,#bbe894);
    border-radius: 16rpx;
    display: flex;
    height: 100rpx;
    justify-content: space-between;
    padding: 0 22rpx 0 28rpx
}

.left-prime {
    color: #43701e;
    font-size: 28rpx;
    font-weight: 700;
    line-height: 100rpx
}

.middle-prime {
    background: #84b564;
    border-radius: 2rpx;
    color: #fff;
    font-size: 24rpx;
    height: 38rpx;
    line-height: 40rpx;
    margin-top: 30rpx;
    opacity: .57;
    text-align: center;
    width: 244rpx
}

.right-prime {
    background: #f8e47c;
    border-radius: 38rpx;
    color: #101010;
    font-size: 28rpx;
    font-weight: 700;
    height: 60rpx;
    line-height: 60rpx;
    text-align: center;
    width: 156rpx
}

.right-prime,.spz-con {
    margin-top: 20rpx
}

.scroll-group-zhan {
    height: 94rpx
}

.scroll-group-wrapper {
    padding: 14rpx 0 0;
    width: 750rpx
}

.scroll-group-wrapper-fixed {
    background: #f1f1f1;
    padding: 14rpx 0;
    position: fixed;
    top: 80rpx;
    z-index: 1000
}

.scroll-group-top {
    display: block;
    padding: 4rpx 0 0;
    white-space: nowrap
}

.group-it:first-child {
    border-left: none;
    margin-left: 16rpx
}

.group-it {
    border-left: 1rpx solid rgba(0,0,0,.1);
    display: inline-block;
    min-width: 156rpx;
    padding: 0 12rpx;
    text-align: center;
    vertical-align: top
}

.group-title {
    font-size: 28rpx;
    font-weight: 700
}

.group-subtitle {
    color: #999;
    font-size: 22rpx;
    line-height: 32rpx;
    margin-top: 12rpx
}

.group-it-choosed .group-title {
    color: #6da174
}

.group-it-choosed .group-subtitle {
    background: linear-gradient(270deg,#4cb955,#61cc5c);
    border-radius: 28rpx;
    color: #fff;
    display: inline-block;
    padding: 0 12rpx
}

.sp-list {
    padding: 0 20rpx
}
