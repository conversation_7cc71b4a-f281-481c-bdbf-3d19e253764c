@font-face {
    font-family: WeChatSansStd-Medium;
    src: url(https://res.wx.qq.com/a/wx_fed/cdn_libs/res/WeChatSans/1.1.0/WeChatSansStd-Medium.ttf)
}

.mask {
    background-color: rgba(0,0,0,.5)
}

.hide__placeholder__element {
    position: relative;
    left: -9999px
}

.hide__element {
    position: absolute;
    left: -9999px
}

.link {
    font-size: 14px;
    color: #576b95;
    font-weight: 450
}

.mode-filter {
    position: relative;
    z-index: 1
}

.mode-filter:before {
    background: inherit;
    -webkit-filter: blur(2px);
    filter: blur(2px)
}

.mode-filter:after,.mode-filter:before {
    content: " ";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    border-radius: inherit
}

.mode-filter:after {
    background: rgba(0,0,0,.25)
}

.mode-filter-black {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px)
}

.mode-filter-black,.mode-opacity-black {
    background-color: rgba(0,0,0,.25);
    border-radius: inherit
}

.mode-filter-black-half-screen {
    background: rgba(9,9,9,.8);
    -webkit-backdrop-filter: blur(24px);
    backdrop-filter: blur(24px);
    border-radius: inherit
}

.mode-filter-white {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px)
}

.mode-filter-white,.mode-opacity-white {
    background-color: hsla(0,0%,100%,.2);
    border-radius: inherit
}

.err .err__inner {
    padding-top: 142px;
    text-align: center;
    color: rgba(0,0,0,.5);
    font-weight: 450
}

.err__title {
    font-size: 22px;
    color: rgba(0,0,0,.9);
    text-align: center;
    line-height: 35.2px
}

.err__desc {
    font-size: 14px
}

.mode__navigation__with__white-icon .weui-navigation-bar__body .weui-navigation-bar__left .weui-navigation-bar__btn_goback {
    background-image: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/backWhite-021be8be29.svg)
}

.weui-btn {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding: 8px 24px;
    box-sizing: border-box;
    font-weight: 700;
    font-size: 17px;
    text-align: center;
    text-decoration: none;
    color: #fff;
    line-height: 1.41176471;
    border-radius: 4px;
    -webkit-tap-highlight-color: rgba(0,0,0,0);
    overflow: hidden;
    -webkit-flex: 1;
    flex: 1
}

.weui-btn+.weui-btn {
    margin-left: 15px
}

.weui-btn_default {
    color: #06ae56;
    background-color: #f2f2f2
}

.weui-btn_default:not(.weui-btn_disabled):visited {
    color: #06ae56
}

.weui-btn_default:not(.weui-btn_disabled):active {
    color: #06ae56;
    background-color: #d9d9d9
}

.weui-btn_primary {
    background-color: #07c160
}

.weui-btn_primary:not(.weui-btn_disabled):visited {
    color: #fff
}

.weui-btn_primary:not(.weui-btn_disabled):active {
    color: #fff;
    background-color: #06ad56
}

.weui-btn_disabled {
    color: rgba(0,0,0,.18);
    background-color: #fafafa
}

.mode__lottery-push-list {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 20
}

.mode__forbid__list {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    height: 100%
}

.half-screen-dialog__follow-teach__sub-title {
    font-size: 12px;
    color: rgba(0,0,0,.3)
}

.half-screen-dialog__follow-teach__sub-title .link {
    font-size: 12px
}

.half-screen-dialog__follow-teach__content {
    margin: 24px 0 28px
}

.weui-btn.weui-btn_default {
    color: #06ae56;
    background-color: #f2f2f2
}

.mode-count-time {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    width: 100%;
    margin-top: -36px
}

@media screen and (max-height:667px) {
    .mode-count-time {
        margin-top: -46px
    }
}