<view class="^pa-1 ^active5" style="display: flex;flex-wrap:wrap;justify-content: center;">
    <view bindtap="viewMoreActNew" data-systype="{{aItem.activeTypeNum}}" data-type="{{aItem.type}}" data-zone_id="{{aItem.id}}" style="{{aItem.showType>=5?'float: left':''}}" wx:if="{{aItem.sys_type!=4}}" wx:for="{{activesList}}" wx:for-index="key" wx:for-item="aItem" wx:key="key">
        <view class="^floor flex_col ^width1" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;min-height: 200px;" wx:if="{{aItem.showType==1}}">
            <navigator hoverClass="none">
                <view class="f_title">
                    <view class="title_main">{{aItem.activeType}}</view>
                </view>
            </navigator>
            <view class="max202" style="margin: 0rpx 24rpx;">
                <scroll-view>
                    <list class="grid no-border col-1" style="background: none;">
                        <item wx:for="{{aItem.gList}}" wx:for-item="agItem" wx:key="key">
                            <view>
                                <view style="width: 100%;">
                                    <view style="width: 40%;float: left;">
                                        <image class="br5" lazyLoad="true" src="{{agItem.cover_img}}" style="width:216rpx;height: 216rpx;border-radius: 5px;"></image>
                                    </view>
                                </view>
                                <view style="width: 60%;float: left;">
                                    <view class="f_goods_name_one price">{{agItem.name}}</view>
                                    <view class="flex_row price">
                                        <view class="f_price">￥<span style="font-size: 30rpx;line-height: 60rpx;">{{agItem.act_price||agItem.price}}</span>
                                            <span class="best_price" wx:if="{{appId==2}}">到手价</span>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </list>
                </scroll-view>
            </view>
        </view>
        <view class="^floor flex_col ^pa-1 ^max-width5" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;min-height: 150px;" wx:if="{{aItem.showType==2}}">
            <navigator hoverClass="none">
                <view class="f_title">
                    <view class="title_main">{{aItem.activeType}}</view>
                </view>
            </navigator>
            <view class="max202" style="margin: 0rpx 24rpx;">
                <scroll-view>
                    <list class="grid no-border col-2" style="background: none;">
                        <item wx:for="{{aItem.gList}}" wx:for-item="agItem" wx:key="key">
                            <view>
                                <view style="width: 100%">
                                    <view class="f_goods_img flex_center">
                                        <image class="br5" lazyLoad="true" src="{{agItem.cover_img}}" style="width:200rpx;height: 200rpx;"></image>
                                    </view>
                                </view>
                                <view style="width: 85%;margin: auto;">
                                    <view class="f_goods_name_new">{{agItem.name}}</view>
                                    <view class="flex_row">
                                        <view class="f_price">￥<span style="font-size: 30rpx;line-height: 40rpx;">{{agItem.act_price||agItem.price}}</span>
                                            <span class="best_price" wx:if="{{appId==2}}">到手价</span>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </list>
                </scroll-view>
            </view>
        </view>
        <view class="^floor flex_col" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;min-height: 180px;" wx:if="{{aItem.showType==3}}">
            <navigator hoverClass="none">
                <view class="f_title">
                    <view class="title_main">{{aItem.activeType}}</view>
                </view>
            </navigator>
            <view class="max202" style="margin: 0rpx 24rpx;">
                <scroll-view>
                    <list class="grid no-border col-3" style="background: none;">
                        <item wx:for="{{aItem.gList}}" wx:for-item="agItem" wx:key="key">
                            <view>
                                <view style="width: 100%">
                                    <view class="flex_center">
                                        <image class="br5" lazyLoad="true" src="{{agItem.cover_img}}" style="width:130rpx;height: 130rpx;"></image>
                                    </view>
                                </view>
                                <view style="width: 100%">
                                    <view class="f_goods_name price">{{agItem.name}}</view>
                                    <view class="flex_row price">
                                        <view class="f_price" style="font-size: 18rpx;">￥<span style="font-size: 24rpx;line-height: 60rpx;">{{agItem.act_price||agItem.price}}</span>
                                            <span class="best_price_small" wx:if="{{appId==2}}">到手价</span>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </list>
                </scroll-view>
            </view>
        </view>
        <view class="^floor flex_col" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;min-height: 150px;" wx:if="{{aItem.showType==4}}">
            <navigator hoverClass="none">
                <view class="f_title">
                    <view class="title_main">{{aItem.activeType}}</view>
                </view>
            </navigator>
            <view class="max202" style="margin: 0rpx 24rpx;">
                <scroll-view>
                    <list class="grid no-border col-4" style="background: none;">
                        <item wx:for="{{aItem.gList}}" wx:for-item="agItem" wx:key="key">
                            <view>
                                <view style="width: 100%">
                                    <view class="flex_center">
                                        <image class="br5" lazyLoad="true" src="{{agItem.cover_img}}" style="width:110rpx;height: 110rpx;"></image>
                                    </view>
                                </view>
                                <view style="width: 100%">
                                    <view class="f_goods_name price">{{agItem.name}}</view>
                                    <view class="flex_row price">
                                        <view class="f_price" style="font-size: 18rpx;">￥<span style="font-size: 24rpx;line-height: 60rpx;">{{agItem.act_price||agItem.price}}</span>
                                            <span class="best_price_small" wx:if="{{appId==2}}">到手价</span>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </item>
                    </list>
                </scroll-view>
            </view>
        </view>
        <view class="active22 mr20 mt20" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;float: left;margin-bottom: 20rpx;" wx:if="{{aItem.showType==5}}">
            <view class="tl1" style="font-size: 30rpx;">{{aItem.activeType}}</view>
            <block wx:for="{{aItem.gList}}" wx:for-item="pgItem" wx:key="key">
                <view>
                    <image class="img1 br5" lazyLoad="true" src="{{pgItem.cover_img}}" style="border-radius: 5px;"></image>
                </view>
                <view class="name1 ml24"> {{pgItem.name}} </view>
                <view class="ml24 pdt10">
                    <text class="pr1">￥{{pgItem.act_price||pgItem.price}}</text>
                    <text class="best_price_small reds" wx:if="{{appId==2}}">到手价</text>
                </view>
            </block>
        </view>
        <view class="active22 mr20 mt20" style="background:url('{{aItem.bg_img}}') no-repeat center;background-size: 100% 100%;float: left;margin-bottom: 20rpx;" wx:if="{{aItem.showType==6}}">
            <view class="active_title mb32 mt24 ml24" style="font-size: 30rpx;">{{aItem.activeType}}</view>
            <view class="mb32 flex_row ml24" wx:for="{{aItem.gList}}" wx:for-item="pgItem" wx:key="key">
                <view style="margin-right: 16rpx;">
                    <image class="active_img2 br5" lazyLoad="true" src="{{pgItem.cover_img}}"></image>
                </view>
                <view class="flex_col">
                    <view class="active_name2">{{pgItem.name}}</view>
                    <view class="reds">
                        <text class="fu2">￥</text>
                        <text class="price2" style="font-size: 26rpx;">{{pgItem.act_price||pgItem.price}}</text>
                        <text class="best_price_small" wx:if="{{appId==2}}">到手价</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view style="clear: both;"></view>
</view>
