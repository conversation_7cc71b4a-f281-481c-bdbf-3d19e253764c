var imgPath = 'https://obs.springland.com.cn';
var filter = ({
  addImgPath: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value)
  }),
  addImgPath140x140: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value + '?x-image-process\x3dstyle/style-140x140')
  }),
  addImgPath710x240: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value + '?x-image-process\x3dstyle/style-720x240')
  }),
  addImgPath350x350: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value + '?x-image-process\x3dstyle/style-350x350')
  }),
  addImgPath750x750: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value + '?x-image-process\x3dstyle/style-750x750')
  }),
  addImgPathW750: (function(value) {
    if (value.indexOf("http") != -1) {
      return (value)
    };
    return (imgPath + value + '?x-image-process\x3dstyle/style-w350')
  }),
  priceSum: (function(value1, value2) {
    value1 = Number(value1 || 0);
    value2 = Number(value2 / 100).toFixed(2) || 0;
    var sq1;
    var sq2;
    var sqlArray = value1.toString().split(".");
    if (sqlArray.length > 1) {
      sq1 = sqlArray[(1)].length
    } else {
      sq1 = 0
    };
    var sq2Array = value2.toString().split(".");
    if (sq2Array.length > 1) {
      sq2 = sq2Array[(1)].length
    } else {
      sq2 = 0
    };
    var m = Math.pow(10, Math.max(sq1, sq2));
    return ((value1 * m + value2 * m) / m)
  }),
  priceToFixed2: (function(value) {
    return (Number)(value / 100).toFixed(2)
  }),
  firstImgPath: (function(value) {
    var firstImg = value.split(',')[(0)];
    return (imgPath + firstImg)
  }),
  calcTotal: (function(goodsList) {
    if (!goodsList || goodsList.length == 0) {
      return (0)
    };
    var totalNum = 0;
    for (var i = 0; i < goodsList.length; i++) {
      totalNum += goodsList[((nt_3 = (i), null == nt_3 ? undefined : 'number' === typeof nt_3 ? nt_3 : "" + nt_3))].num
    };
    return (totalNum)
  }),
  cutTimeStr: (function(timeStr) {
    return (timeStr.substring(11, 16))
  }),
  absValue: (function(value) {
    if (value < 0) {
      return (-value)
    };
    return (value)
  }),
  toPercent: (function(point) {
    if (point == 0) {
      return (0 + '%')
    };
    return (Number)(point * 100).toFixed(2) + '%'
  }),
  changePercent: (function(value) {
    return (Number)(value * 100).toFixed(2) + '%'
  }),
  priceToNoFixed2: (function(value) {
    return (Number)(value / 100)
  }),
});
module.exports = ({
  addImgPath: filter.addImgPath,
  addImgPath140x140: filter.addImgPath140x140,
  addImgPath710x240: filter.addImgPath710x240,
  addImgPath350x350: filter.addImgPath350x350,
  addImgPath750x750: filter.addImgPath750x750,
  addImgPathW750: filter.addImgPathW750,
  priceToNoFixed2: filter.priceToNoFixed2,
  priceToFixed2: filter.priceToFixed2,
  firstImgPath: filter.firstImgPath,
  calcTotal: filter.calcTotal,
  absValue: filter.absValue,
  cutTimeStr: filter.cutTimeStr,
  changePercent: filter.changePercent,
  toPercent: filter.toPercent,
  priceSum: filter.priceSum,
});