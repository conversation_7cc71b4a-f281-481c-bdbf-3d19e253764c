<view class="margin radius bg-white">
    <view class="padding-sm text-lg">收款单号：{{piecemeal.id}}</view>
    <view class="padding-sm text-xl fr">￥{{piecemeal.price}}</view>
    <view class="padding-sm text-xl ">{{piecemeal.itemName}}</view>
    <view class="padding-sm text-lg">收款单位：{{piecemeal.shopTitle?piecemeal.shopTitle:''}}</view>
    <view class="padding-sm text-lg">备注：{{piecemeal.remark?piecemeal.remark:''}}</view>
    <view class="padding-sm text-lg">创建时间：{{piecemeal.createTime}}</view>
    <view class="padding-xs flex flex-direction">
        <button bindtap="pay" class="bg-green lg" disabled="{{piecemeal.status!=1}}">立即支付</button>
        <picker bindchange="invoice" class="bg-blue lg margin-top" disabled="{{piecemeal.status!=2||piecemeal.invoiceStatus!=1}}" range="{{pickerRange[piecemeal.canInvoice]}}">
            <view class="invoice"> 申请开票/收据 </view>
        </picker>
    </view>
</view>
<view class="margin radius bg-white" wx:if="{{piecemeal.invoice}}">
    <view class="padding-sm text-lg text-bold">开票信息</view>
    <view class="padding-sm text-lg">抬头类型：{{invoiceType[piecemeal.invoice.invoiceType]}}</view>
    <view class="padding-sm text-lg">发票抬头：{{piecemeal.invoice.title}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.taxNo}}">发票抬头：{{piecemeal.invoice.taxNo}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.createTime}}">创建时间：{{piecemeal.invoice.createTime}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.address}}">注册地址：{{piecemeal.invoice.address}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.phone}}">注册电话：{{piecemeal.invoice.phone}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.bank}}">开户银行：{{piecemeal.invoice.bank}}</view>
    <view class="padding-sm text-lg" wx:if="{{piecemeal.invoice.account}}">银行账号：{{piecemeal.invoice.account}}</view>
</view>
