<view bindtap="changeCityShow" class="top-city">
    <view class="local-img">
        <image class="" src="../../../img/icon/dingwei.png"></image>
    </view>
    <view class="chengshi" wx:if="{{dingweiFlag}}">{{city.name}}</view>
    <view class="chengshi" wx:else>定位失败</view>
    <view class="{{cityChooseShow?'jiantou1':'jiantou'}}">
        <text class="icon-right lg text-gray cus"></text>
    </view>
</view>
<view class="dwsb-wrapper" wx:if="{{!dingweiFlag}}">
    <image class="dwsb-img" src="../../../img/icon/dwsb.png"></image>
    <view class="huise-tishi">获取地理位置失败，</view>
    <view class="huise-tishi">请开启定位，或手动搜索选择门店</view>
    <button bindtap="tapKqdw" class="kqdw-btn">开启定位</button>
    <view class="hongse-tishi">若小程序定位已开启</view>
    <view class="hongse-tishi">请检查微信定位服务是否开启</view>
</view>
<scroll-view scrollY class="scroll-wrapper" scrollTop="{{scrollTop}}" wx:if="{{dingweiFlag}}">
    <list class="menu">
        <item bindtap="chooseStore" class="arrow" data-mall="{{item}}" wx:for="{{city.storeList}}">
            <view class="content">
                <text class="">{{item.storeName}}</text>
            </view>
            <view class="action" wx:if="{{item.distance}}">
                <text class="text-grey text-sm">距{{item.distance}}km</text>
            </view>
        </item>
    </list>
</scroll-view>
<scroll-view scrollY class="scroll-wrapper" scrollTop="{{scrollTop}}" wx:if="{{cityChooseShow}}">
    <block wx:for="{{cityMap}}">
        <view class="suoyin">{{index}}</view>
        <list class="menu">
            <item bindtap="chooseCity" class="city-item" data-id="{{itemcity.id}}" wx:for="{{item}}" wx:for-item="itemcity">
                <view class="content">
                    <text class="">{{itemcity.name}}</text>
                </view>
            </item>
        </list>
    </block>
</scroll-view>
