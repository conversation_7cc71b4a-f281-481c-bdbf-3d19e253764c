<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{index==TabCur?'cur cust-color':''}}" data-id="{{index}}" wx:for="{{4}}"> {{titleList[index]}} </item>
    </view>
</scroll-view>
<view class="margin-cust" wx:if="{{hasLogin}}">
    <view bindtap="gotoDetail" class="coupon-item" data-index="{{index}}" wx:for="{{showList}}">
        <view class="xian">
            <view class="right-wrapper">
                <view class="coupon-title {{item.status==1?'text-red':item.status==2?'text-green':'text-blue'}} fr">{{item.statusName}}</view>
                <view class="coupon-title">收款单号：{{item.id}}</view>
                <view class="coupon-title fr">￥{{item.price}}</view>
                <view class="coupon-title">{{item.itemName}}</view>
                <view class="coupon-title">收款单位：{{item.shopTitle?item.shopTitle:''}}</view>
                <view class="tap-kuoda">
                    <button catchtap="pay" class="shiyong" data-index="{{index}}" wx:if="{{item.status==1}}">立即支付</button>
                    <picker bindchange="invoice" catchtap="applyInvoice" class="" data-index="{{index}}" range="{{pickerRange[item.canInvoice]}}" wx:if="{{item.status==2&&item.invoiceStatus==1}}">
                        <view class="invoice"> 申请开票/收据 </view>
                    </picker>
                </view>
            </view>
        </view>
    </view>
    <view class="no-data">暂无数据</view>
</view>
<login-com wx:else></login-com>
