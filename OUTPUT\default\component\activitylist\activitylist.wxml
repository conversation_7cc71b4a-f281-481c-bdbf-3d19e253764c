<view bindtap="toActivity" class="view-top" data-activityid="{{it.id}}" wx:for="{{activitylist}}" wx:for-item="it">
    <view class="activityimage">
        <image src="{{it.imgurl}}"></image>
        <view class="activitytitle">{{it.title}}</view>
    </view>
    <view class="timeaddress">
        <view class="timeaddress-item">
            <text class="icon-timefill lg" style="color:#E2CEB3"></text>
            <text> 报名时间：{{it.reportstarttime}} - {{it.reportendtime}}</text>
        </view>
        <view class="timeaddress-item">
            <text class="icon-locationfill lg" style="color:#E2CEB3"></text>
            <text> 活动地点：{{it.address}}</text>
        </view>
    </view>
</view>
<view class="nodata" wx:if="{{activitylist.length==0}}">—————— 暂无数据 ——————</view>
