<movable-area class="movable-1" wx:if="{{show}}">
    <movable-view bindchange="onChange" class="movable-2" damping="10000" direction="all" x="{{x}}" y="{{y}}">
        <view catchtap="goCart" class="to-cart kuoda">
            <view class="cart-img">
                <image class="buoy-img" src="{{filter.addImgPath(buoy.imgurl)}}"></image>
            </view>
        </view>
        <view class="cart-wenzi">{{buoy.name}}</view>
    </movable-view>
</movable-area>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>