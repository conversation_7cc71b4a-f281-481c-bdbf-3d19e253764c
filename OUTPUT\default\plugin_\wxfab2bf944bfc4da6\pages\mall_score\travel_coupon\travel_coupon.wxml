<view class="component-travel-coupon" hidden="{{!couponList.length}}">
    <view class="travel-coupon-title"> 出行优惠 <text class="travel-coupon-tips">(今日有消费可领）</text>
    </view>
    <view class="travel-coupon-wrapper">
        <view class="travel-coupon-box">
            <view class="travel-coupon-item {{item.state===5?'has-receive-coupon-state':''}}" wx:if="{{index<3||isExpandAllCoupon}}" wx:for="{{couponList}}" wx:key="stock_id">
                <view bind:tap="openCouponInfoDialog" class="travel-coupon-info exposure-report-dom" data-coupon="{{item}}" data-exposure-data="{{({ event_id:100006,stock_id:item.stock_id,stock_name:item.coupon_name,button_state:$couponReportMap[item.state] })}}" data-open-type="1" data-report-data="{{({ event_id:4001,stock_id:item.stock_id,stock_name:item.coupon_name,button_state:$couponReportMap[item.state] })}}" id="id-{{index}}">
                    <image class="travel-coupon-item-logo" mode="aspectFit" src="{{item.bank_logo}}"></image>
                    <view class="travel-coupon-item-name">{{item.coupon_name}}</view>
                </view>
                <view class="receive-coupon-box">
                    <button bind:tap="openCouponInfoDialog" class="receive-coupon-btn {{item.state!==2?'btn-disabled':''}} {{item.state===1?'icon-lock':''}}" data-coupon="{{item}}" data-open-type="2" data-report-data="{{({ event_id:4002,stock_id:item.stock_id,stock_name:item.coupon_name,button_state:$couponReportMap[item.state] })}}" type="primary" wx:if="{{item.state!==5}}">领取</button>
                    <view class="receive-coupon-btn-tips" wx:if="{{item.state===3||item.state===4}}"> {{item.state===3?'今日已领完':'本月已领完'}} </view>
                </view>
            </view>
        </view>
        <view class="expand-all-box" wx:if="{{couponList.length>3}}">
            <view bind:tap="expandAllBankCoupon" class="expand-all-text {{isExpandAllCoupon?'arrow-up':'arrow-down'}}"> {{isExpandAllCoupon?'收起':'展开全部'}} </view>
        </view>
    </view>
</view>
