<form bindsubmit="formSubmit">
    <input class="radius" name="mdid" style="display:none;" value="{{mdid}}"></input>
    <form-group>
        <view class="title">手机</view>
        <input bindinput="phoneInput" class="radius" maxlength="11" name="mobile" placeholder="输入手机号码" value="{{mobile}}"></input>
    </form-group>
    <form-group class="">
        <view class="title">
            <text space="ensp">姓名</text>
        </view>
        <input class="radius" disabled="{{xinxiDisabled}}" name="name" placeholder="输入您的姓名" type="text" value="{{name}}"></input>
        <button bindgetuserinfo="bindGetUserInfo" class="bg-default shadow" openType="getUserInfo" wx:if="{{canIUse}}">获取用户信息</button>
    </form-group>
    <form-group>
        <view class="title">性别</view>
        <picker bindchange="pickerChange" disabled="{{xbDisabled}}" range="{{picker}}" value="{{gender}}">
            <view class="picker"> {{picker[gender]}} <input name="gender" style="display:none;" value="{{gender}}"></input>
            </view>
        </picker>
    </form-group>
    <form-group>
        <view class="title">生日</view>
        <picker bindchange="DateChange" disabled="{{birthDisabled}}" end="{{endDate}}" mode="date" value="{{birthday}}">
            <view class="picker"> {{birthday?birthday:'请选择您的生日'}} <input name="birthday" style="display:none;" value="{{birthday}}"></input>
            </view>
        </picker>
    </form-group>
    <form-group>
        <view class="title">
            <text space="ensp">验证码</text>
        </view>
        <input class="radius" maxlength="4" name="validationCode" placeholder="输入验证码" type="number"></input>
        <button bindtap="getValidationCode" class="{{isSend?'bg-gray':'bg-default'}} shadow" disabled="{{getYzmDisabled}}">{{isSend?'已发送':'验证码'}}</button>
    </form-group>
    <view class="padding flex flex-direction bg-white solid-top">
        <button class="bg-default lg" disabled="{{btnDisabled}}" formType="submit">确定</button>
    </view>
</form>
<gift id="gift-id"></gift>
