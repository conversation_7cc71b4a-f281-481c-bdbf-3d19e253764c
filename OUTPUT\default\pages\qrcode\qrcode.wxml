<view class="container">
    <view class="main-wrapper">
        <image class="top-img" src="../../img/icon/img_prime_qr_top.png"></image>
        <view class="hym">会员码</view>
        <canvas bindlongtap="save" canvasId="canvas" class="canvas"></canvas>
        <view class="tishi">付款前出示二维码，即可享受会员积分</view>
        <button bindtap="goCoupon" style="border-radius: 30rpx;left: 70rpx;width: 30%; margin-top: 30rpx;">查看券包</button>
        <button bindtap="goMyBrand" style="border-radius: 30rpx;left: 80rpx;width: 45%; margin-top: 20rpx;">查看我的联名品牌</button>
    </view>
    <swiper autoplay="true" circular="true" class="screen-swiper square-dot" current="{{currentTap}}" duration="500" indicatorDots="true" interval="5000" wx:if="{{bannerList.length>0}}">
        <swiper-item bindtap="navPicClick" data-jumptype="{{it.jumpType}}" data-url="{{it.linkUrl}}" wx:for="{{bannerList}}" wx:for-item="it" wx:key="{{it.imgUrl}}">
            <image mode="widthFix" src="{{it.imgUrl}}"></image>
        </swiper-item>
    </swiper>
    <view class="window">
        <view bindtap="navPicClick" class="window-it" data-jumptype="{{it.jumpType}}" data-url="{{it.linkUrl}}" wx:for="{{picList}}" wx:for-index="idx" wx:for-item="it" wx:key="{{it.imgUrl}}">
            <image mode="scaleToFill" src="{{it.imgUrl}}" wx:if="{{idx<2}}"></image>
        </view>
    </view>
</view>
