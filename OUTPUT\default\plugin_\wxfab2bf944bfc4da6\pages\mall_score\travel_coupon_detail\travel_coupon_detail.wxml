<half-dialog bind:dialogClose="closeCouponInfoDialog" show="{{showDialog}}">
    <view class="component-travel-coupon-detail travel-coupon-dialog">
        <view class="travel-coupon-detail" hoverClass="none" hoverStopPropagation="false">
            <image class="travel-coupon-logo" mode="aspectFill" src="{{couponInfo.bank_logo}}"></image>
            <view class="travel-coupon-name">{{couponInfo.coupon_name}}</view>
            <view class="travel-coupon-amount-wrapper">
                <text class="travel-coupon-amount-num">{{couponInfo.amount_desc}}</text>
                <text class="travel-coupon-amount-type">元</text>
            </view>
        </view>
        <view class="travel-coupon-rules">
            <view class="travel-coupon-rules-title">使用规则</view>
            <view class="travel-coupon-rules-des">
                <view class="travel-coupon-rules-text" wx:for="{{couponInfo.coupon_rule}}" wx:key="item">{{item}}</view>
            </view>
        </view>
        <view class="travel-coupon-dialog-buttom" hoverClass="none" hoverStopPropagation="false">
            <button bind:tap="receiveCoupon" class="travel-coupon-dialog-btn {{couponInfo.state!==2?'btn-disabled':''}} {{couponInfo.state===2?'exposure-report-dom':''}}" data-exposure-data="{{({ event_id:100007,stock_id:couponInfo.stock_id,stock_name:couponInfo.coupon_name,button_state:$couponReportMap[couponInfo.state] })}}" data-report-data="{{({ event_id:4003,stock_id:couponInfo.stock_id,stock_name:couponInfo.coupon_name,button_state:$couponReportMap[couponInfo.state] })}}" disabled="{{couponInfo.state!==2}}" id="travel-coupon-dialog-btn" type="primary"> {{couponDialogBtnText[couponInfo.state]||'领取'}} </button>
            <view class="travel-coupon-dialog-button-tips">
                <text wx:if="{{couponInfo.state===1}}">暂不可领，如已消费请先点击提交今日快速积分</text>
                <text wx:else>本月已领{{couponInfo.sended_num}}/{{couponInfo.total_num}}张</text>
            </view>
        </view>
    </view>
</half-dialog>
