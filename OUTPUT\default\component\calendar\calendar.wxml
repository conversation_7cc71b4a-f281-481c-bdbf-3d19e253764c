<view class="header space-between" wx:if="{{showHeader}}">
    <view bindtap="dataBefor" class="iconfont" data-id="0">
        <view class="left-color"></view>
    </view>
    <view bindtap="dateSelection" class="btn flex-center">
        <view class="text">{{selectDay}}</view>
        <view class=""></view>
    </view>
    <view bindtap="dataBefor" class="iconfont" data-id="1">
        <view class="right-color"></view>
    </view>
</view>
<view class="{{isOpen?'':'calendar-box'}} {{dateShow?'active':''}}" wx:if="{{!calShow}}">
    <view class="calendar-wrapper {{dateShow?'active':''}}">
        <view class="calendar-panel">
            <view bindtap="dataBefor" class="date-befor" data-id="0" data-type="month">
                <view class="iconfont">
                    <view class="left-color"></view>
                </view>
            </view>
            <view class="calendar-panel-box">
                <view>{{canlender.year}}年</view>
                <view>{{canlender.month}}月</view>
            </view>
            <view bindtap="dataBefor" class="date-after" data-id="1" data-type="month">
                <view class="iconfont">
                    <view class="right-color"></view>
                </view>
            </view>
        </view>
        <view class="calendar-header {{mini?'mini':''}}">
            <view>日</view>
            <view>一</view>
            <view>二</view>
            <view>三</view>
            <view>四</view>
            <view>五</view>
            <view>六</view>
        </view>
        <view class="calendar-body">
            <view class="calender-body-date-week" wx:for="{{canlender.weeks}}" wx:for-index="week" wx:for-item="weeks" wx:key="weeks">
                <view bindtap="selectDay" class="date {{mini?'mini':''}} {{canlender.month==day.month&&(nowMonth===day.month&&day.date-nowDate>=0||day.month-nowMonth>0)?'':''}} {{day.checked&&canlender.month==day.month?'date-current':''}}" data-index="{{index}}" data-ischeck="{{canlender.month==day.month&&(nowMonth===day.month&&day.date-nowDate>=0||day.month-nowMonth>0)}}" data-week="{{week}}" wx:for="{{weeks}}" wx:for-item="day" wx:key="day">
                    <text class="datenum"> {{day.date}} </text>
                    <view class="dot" wx:if="{{day.had}}">•</view>
                </view>
            </view>
        </view>
    </view>
</view>

<wxs module="filters">
var strcontains = (function(str, c) {
  if (str.indexOf(c) >= 0) {
    return (true)
  } else {
    return (false)
  }
});
var formatNumber = (function(n) {
  n = n.toString();
  return (n[(1)] ? n : '0' + n)
});
module.exports = ({
  formatNumber: formatNumber,
  strcontains: strcontains,
});
</wxs>