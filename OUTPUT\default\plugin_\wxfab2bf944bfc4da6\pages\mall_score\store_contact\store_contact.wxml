<view class="~content-box-container component-store-contact" wx:if="{{storeTypeList.length>1}}">
    <view class="~content-box-header">
        <view class="~content-box-title">联系门店</view>
    </view>
    <view class="~content-box-body">
        <view class="store-type-list">
            <view bind:tap="switchStoreType" class="store-type-item {{storeTypeActive===item.id?'active':''}}" data-item="{{item}}" wx:for="{{storeTypeList}}" wx:key="id">{{item.name}}</view>
        </view>
        <view class="store-guider-list">
            <view wx:for="{{storeContactInfo.list}}" wx:for-item="storePage" wx:key="index">
                <store-contact-item bind:invokeGuideList="invokeEvent" bind:invokeGuideWechat="invokeEvent" storeInfo="{{item}}" wx:for="{{storePage}}" wx:key="store_id"></store-contact-item>
            </view>
            <view bindtap="loadMore" class="load-more-store" hidden="{{storeContactInfo.pageNum>=storeContactInfo.totalPageNum&&storeContactInfo.list.length}}"> {{storeContactInfo.loadFailStatus?'加载失败，点击重试':'加载中...'}} </view>
        </view>
    </view>
</view>
