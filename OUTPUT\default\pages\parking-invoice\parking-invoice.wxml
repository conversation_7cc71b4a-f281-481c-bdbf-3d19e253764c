<view class="t1">请输入车牌号</view>
<view class="chepai-wrapper">
    <view class="chepai-con">
        <view class="chepai-input">
            <view class="chepai-img">
                <image src="/img/icon/qiche.png"></image>
            </view>
            <view catchtap="showJianpan" class="chapai-item {{isShow&&activeIndex==item?'chepai-active':''}} {{item==7&&!plateNo[item]?'xinnengyuan':''}}" data-xh="{{item}}" wx:for="{{8}}">
                <block wx:if="{{item==7}}">{{plateNo[item]?plateNo[item]:'新能源'}}</block>
                <block wx:else>{{plateNo[item]}}</block>
            </view>
        </view>
        <view class="zuijin-chepai" wx:if="{{plateNoRecentList.length>0}}">
            <view class="chepai-item" wx:for="{{plateNoRecentList}}" wx:key="plateNo">
                <view catchtap="chooseZuijinChepai" class="che-text kuoda" data-item="{{item}}">{{item.plateNo}}</view>
                <image catchtap="deleteZuijinChepai" class="kuoda" data-item="{{item}}" src="/img/icon/shanchu-x.png"></image>
            </view>
        </view>
        <view bindtap="bindQuery" class="query">查询</view>
    </view>
</view>
<view bindtap="goInvoiceList" class="record">查看开票记录</view>
<view class="t1">缴费90天内可申请开票</view>
<v-panel backgroundColor="white" binddelete="inputdelete" bindinputchange="inputChange" isShow="{{isShow}}" keyBoardType="{{keyBoardType}}" qty="{{plateNo.length}}"></v-panel>
