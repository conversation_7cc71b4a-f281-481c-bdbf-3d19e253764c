/**
 * 验证Authorization加密方式
 * 重新分析：Authorization可能就是登录token的变体
 */

const crypto = require('crypto');

/**
 * SHA1加密实现（小写版本）
 */
function sha1_lowercase(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

/**
 * SHA1加密实现（大写版本）
 */
function sha1_uppercase(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex').toUpperCase();
}

/**
 * MD5加密实现
 */
function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

/**
 * 基于utils/sha1.js的签名算法（大写）
 */
function generateSignFromSha1Utils(url, params) {
    const REQUEST_KEY = "SPRINGLAND";
    const REQUEST_SECRET = "springland*&^0627@";

    const sortedKeys = Object.keys(params).sort();
    const signArray = [REQUEST_KEY];
    signArray.push("http://ygxcxtest.springland.cn:8089/api" + url);

    sortedKeys.forEach(key => {
        signArray.push(key + params[key]);
    });

    signArray.push(REQUEST_SECRET);
    const signString = signArray.join('');

    console.log("SHA1Utils签名字符串:", signString);
    return sha1_uppercase(signString);
}

/**
 * 从ajax.js中提取的签名生成函数
 */
function generateSignFromAjax(params) {
    const sortedKeys = Object.keys(params).sort();

    let paramStr = "";
    for (let i = 0; i < sortedKeys.length; i++) {
        const key = sortedKeys[i];
        const value = params[key];
        if (typeof value === 'object') {
            paramStr += key + JSON.stringify(value);
        } else {
            paramStr += key + value;
        }
    }

    const signString = "2osDKbBC*sjdOOsa" + paramStr + "2osDKbBC*sjdOOsa";
    console.log("Ajax签名字符串:", signString);
    return sha1_lowercase(signString);
}

/**
 * 测试不同的Authorization生成方式
 */
function testAuthorizationGeneration() {
    console.log("=== 验证Authorization加密方式 ===\n");

    // 从抓包数据中提取的信息
    const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
    const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
    const requestParams = {
        hyid: "820536796",
        mdid: "6021"
    };

    console.log("目标Authorization:", targetAuth);
    console.log("原始Token:", originalToken);
    console.log("请求参数:", requestParams);
    console.log();

    // 方法1: 直接使用原始token
    console.log("--- 方法1: 直接使用原始token ---");
    console.log("原始token:", originalToken);
    console.log("是否匹配:", originalToken === targetAuth);
    console.log();

    // 方法2: 基于token和参数生成签名
    console.log("--- 方法2: token + 参数组合 ---");
    const combinations = [
        originalToken + requestParams.hyid + requestParams.mdid,
        originalToken + requestParams.hyid,
        originalToken + requestParams.mdid,
        requestParams.hyid + originalToken + requestParams.mdid,
        requestParams.hyid + requestParams.mdid + originalToken,
    ];

    for (let i = 0; i < combinations.length; i++) {
        const combo = combinations[i];
        const sha1Result = sha1_lowercase(combo);
        const md5Result = md5(combo);
        console.log(`组合${i+1} (${combo.substring(0, 50)}...): `);
        console.log(`  SHA1: ${sha1Result} ${sha1Result === targetAuth ? '✓ 匹配!' : ''}`);
        console.log(`  MD5:  ${md5Result} ${md5Result === targetAuth ? '✓ 匹配!' : ''}`);
    }
    console.log();

    // 方法3: 使用utils/sha1.js的签名算法
    console.log("--- 方法3: utils/sha1.js签名算法 ---");
    const sha1UtilsResult = generateSignFromSha1Utils("/sign/sign", requestParams);
    console.log("生成结果:", sha1UtilsResult);
    console.log("是否匹配:", sha1UtilsResult.toLowerCase() === targetAuth);
    console.log();

    // 方法4: 使用ajax.js签名算法
    console.log("--- 方法4: ajax.js签名算法 ---");
    const ajaxResult = generateSignFromAjax(requestParams);
    console.log("生成结果:", ajaxResult);
    console.log("是否匹配:", ajaxResult === targetAuth);
    console.log();

    // 方法5: 尝试token的部分变换
    console.log("--- 方法5: token变换 ---");
    // 观察到两个值的前8位相同：1fb508f5
    // 可能是对token的某种变换
    const tokenPrefix = originalToken.substring(0, 8); // 1fb508f5
    const tokenSuffix = originalToken.substring(8);    // c099c83322274fe5a8ad1f46c7f5882a
    const authSuffix = targetAuth.substring(8);        // a4e6557df6cb447aa314544f308a7a5d

    console.log("Token前缀:", tokenPrefix);
    console.log("Token后缀:", tokenSuffix);
    console.log("Auth后缀:", authSuffix);

    // 尝试对token后缀进行各种变换
    const transformations = [
        sha1_lowercase(tokenSuffix),
        sha1_lowercase(tokenSuffix + requestParams.hyid),
        sha1_lowercase(tokenSuffix + requestParams.mdid),
        sha1_lowercase(tokenSuffix + requestParams.hyid + requestParams.mdid),
        md5(tokenSuffix),
        md5(tokenSuffix + requestParams.hyid + requestParams.mdid),
    ];

    for (let i = 0; i < transformations.length; i++) {
        const transformed = transformations[i];
        const newAuth = tokenPrefix + transformed.substring(0, 32); // 取前32位
        console.log(`变换${i+1}: ${newAuth} ${newAuth === targetAuth ? '✓ 匹配!' : ''}`);
    }
}

// 运行测试
testAuthorizationGeneration();
