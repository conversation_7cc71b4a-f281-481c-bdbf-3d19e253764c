@import "..\..\colorui.wxss";

.sale-item,.sale-list,page {
    background-color: #f9f9f8
}

.sale-item {
    margin-top: 20rpx
}

.sale-content {
    background-color: #fff;
    height: 200rpx;
    width: 100%
}

.sale-head {
    height: 70rpx;
    position: relative
}

.sale-type {
    background-color: #fc7412;
    border-radius: 6rpx;
    color: #fff;
    height: 40rpx;
    left: 20rpx;
    line-height: 40rpx;
    text-align: center;
    top: 20rpx;
    width: 74rpx
}

.sale-shop,.sale-type {
    font-size: 24rpx;
    position: absolute
}

.sale-shop {
    color: #dc6c5d;
    height: 70rpx;
    line-height: 70rpx;
    right: 20rpx;
    text-align: right;
    top: 0rpx;
    width: 300rpx
}

.sale-date {
    font-size: 26rpx;
    height: 60rpx;
    line-height: 60rpx;
    margin-left: 20rpx;
    width: 100%
}

.sale-foot {
    height: 70rpx;
    position: relative;
    width: 100%
}

.sale-price {
    left: 20rpx
}

.sale-integral,.sale-price {
    font-size: 26rpx;
    height: 60rpx;
    line-height: 60rpx;
    position: absolute;
    top: 0rpx
}

.sale-integral {
    right: 20rpx
}

.sale-detail {
    background-color: #fff;
    margin-top: 10rpx;
    padding-bottom: 20rpx;
    padding-top: 20rpx
}

.detail-title {
    height: 60rpx
}

.detail-title,.goods-item {
    display: flex;
    font-size: 26rpx;
    font-weight: 700;
    justify-content: space-between;
    line-height: 60rpx;
    margin: auto;
    width: 670rpx
}

.goods-item {
    min-height: 60rpx
}

.l-cell {
    overflow: hidden;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 450rpx
}

.m-cell {
    text-align: center;
    width: 100rpx
}

.r-cell {
    text-align: right;
    width: 120rpx
}

.bottom {
    color: #c0c2ca;
    font-size: 24rpx;
    margin: 20rpx 0 110rpx;
    text-align: center
}
