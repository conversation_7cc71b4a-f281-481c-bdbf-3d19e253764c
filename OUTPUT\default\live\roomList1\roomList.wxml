<view class="head">
    <swiper autoplay="false" duration="500" indicatorActiveColor="#fff" indicatorColor="rgba(255, 255, 255, 0.3)" indicatorDots="true" interval="5000">
        <swiper-item bindtap="gotoRoom" data-roomid="{{it.roomid}}" wx:if="{{inx<4}}" wx:for="{{roomList}}" wx:for-index="inx" wx:for-item="it">
            <view class="main-img">
                <image class="img1" src="{{it.shareImg}}"></image>
                <view class="img1-title">
                    <view class="ti1">{{it.name}}</view>
                    <view class="ti2">{{it.date}} {{it.startHour}}-{{it.endHour}}</view>
                </view>
            </view>
            <image class="img2" src="{{it.goods[0].coverImg}}"></image>
            <image class="img3" src="{{it.goods[1].coverImg}}"></image>
        </swiper-item>
    </swiper>
    <view class="list">
        <view bindtap="gotoRoom" class="room" data-roomid="{{it.roomid}}" wx:for="{{roomList}}" wx:for-item="it">
            <image class="pic1" lazyLoad="true" src="{{it.shareImg}}"></image>
            <view class="status status-{{it.liveStatus}}">
                <text decode="true">&ensp;</text>{{it.liveStatus==101?'直播中':it.liveStatus==102?'未开始':it.liveStatus==107?'已过期':it.liveStatus==103?'已结束':it.liveStatus==104?'禁播':it.liveStatus==105?'暂停中':it.liveStatus==106?'异常':''}}<text decode="true">&ensp;</text>
            </view>
            <view class="title">
                <view class="name">{{it.name}}</view>
                <view class="time">{{it.date}} {{it.startHour}}</view>
            </view>
            <view class="goods">
                <view class="good" wx:for="{{it.goods}}" wx:for-item="it2">
                    <image class="goodpic" lazyLoad="true" src="{{it2.coverImg}}"></image>
                </view>
            </view>
        </view>
    </view>
    <view class="buttom-line"> - 我是有底线的 - </view>
</view>
