<view class="component-card-member">
    <view class="card-member-box">
        <view class="card-member-title" wx:if="{{cardFieldList.length}}"> 请完善以下信息用于开通会员 </view>
        <view class="card-member-form">
            <block wx:for="{{cardFieldList}}" wx:key="name">
                <view class="weui-cell" wx:if="{{item.field_name==='手机'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">手机号</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="weui-element phone-input-box">
                            <input bind:blur="phoneNumberBlur" bind:input="phoneNumberInput" class="weui-input" data-field-index="{{index}}" data-field-item="{{item}}" maxlength="11" placeholder="请输入手机号" placeholderClass="input-placeholder" type="number" value="{{item.value}}"></input>
                            <view class="valid-code-box" wx:if="{{changePhoneMode}}">
                                <view bind:tap="sendPhoneValidCode" class="valid-code-button" data-phone-num="{{item.value}}" wx:if="{{!hasSendValidCode}}">获取验证码</view>
                                <view class="valid-code-button disabled" wx:else>{{validCodeTimes}}s后重新发送</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='手机'&&changePhoneMode}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">验证码</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="weui-element">
                            <input bind:input="inputValidCode" class="weui-input" placeholder="请输入验证码" placeholderClass="input-placeholder" type="number"></input>
                        </view>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='性别'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">性别</view>
                    </view>
                    <view class="weui-cell__bd">
                        <picker bindchange="sexPickerChange" data-field-index="{{index}}" data-field-item="{{item}}" mode="selector" range="{{sexList}}" value="{{sexSelectIndex}}">
                            <view class="weui-select weui-select_in-select-after {{item.value?'':'input-placeholder'}}">{{item.value||'请选择'}}</view>
                        </picker>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='姓名'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">姓名</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="weui-element">
                            <input bind:input="inputModel" class="weui-input" data-field-index="{{index}}" data-field-item="{{item}}" placeholder="请输入姓名" placeholderClass="input-placeholder" value="{{item.value}}"></input>
                        </view>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='详细地址'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">地址</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="weui-element">
                            <input bind:input="inputModel" class="weui-input" data-field-index="{{index}}" data-field-item="{{item}}" placeholder="请填写地址" placeholderClass="input-placeholder" value="{{item.value}}"></input>
                        </view>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='城市'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">城市</view>
                    </view>
                    <view class="weui-cell__bd">
                        <picker bindchange="pickerChange" data-field-index="{{index}}" data-field-item="{{item}}" mode="region" value="{{item.value_list}}">
                            <view class="weui-select weui-select_in-select-after {{item.value_list.length?'':'input-placeholder'}}">{{item.value_list.length?item.value_list:'请选择'}}</view>
                        </picker>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='电子邮箱'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">邮箱</view>
                    </view>
                    <view class="weui-cell__bd">
                        <view class="weui-element">
                            <input bind:input="inputModel" class="weui-input" data-field-index="{{index}}" data-field-item="{{item}}" placeholder="请输入邮箱" placeholderClass="input-placeholder" value="{{item.value}}"></input>
                        </view>
                    </view>
                </view>
                <view class="weui-cell" wx:if="{{item.field_name==='生日'}}">
                    <view class="weui-cell__hd">
                        <view class="weui-label">生日</view>
                    </view>
                    <view class="weui-cell__bd">
                        <picker bindchange="pickerChange" data-field-index="{{index}}" data-field-item="{{item}}" mode="date" value="{{item.value}}">
                            <view class="weui-select weui-select_in-select-after {{item.value?'':'input-placeholder'}}">{{item.value||'请选择'}}</view>
                        </picker>
                    </view>
                </view>
            </block>
        </view>
    </view>
    <view class="protocol-confirm-box">
        <view class="protocol-agree">
            <view bindtap="switchProtocolCheck" class="protocol-agree-box">
                <radio checked="{{cardOpenProtocolCheck}}" class="protocol-radio"></radio> 我已阅读并同意 </view>
            <navigator class="protocol-link" hoverClass="none" url="../auth_protocol/auth_protocol?openid={{openId}}&mch_id={{mchCode}}">《会员功能服务协议》</navigator>
        </view>
        <button bindtap="openMallCard" class="confirm-buttom {{cardOpenProtocolCheck?'':'disabled'}}" data-report-data="{{({event_id:cardServiceAuth?1003:1004})}}" disabled="{{!cardOpenProtocolCheck}}" type="primary">立即开通</button>
    </view>
</view>
