<template name="wxParserVideo">
    <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}">
        <video autoplay="{{item.attr.autoplay}}" class="{{item.classStr}} wxParser-{{item.tag}}-video" controls="{{item.attr.controls}}" loop="{{item.attr.loop}}" muted="{{item.attr.muted}}" src="{{item.attr.src}}"></video>
    </view>
</template>
<template name="wxParserAudio">
    <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}">
        <view class="{{item.classStr}} wxParser-{{item.tag}}-video audio-container">
            <view class="audio-status">
                <image class="audio-poster" src="{{item.attr.poster}}"></image>
                <image bindtap="handleClickAudio" class="audio-btn" data-src="{{item.attr.src}}" src="{{item.isPlaying?'../../images/play.png':'../../images/pause.png'}}"></image>
            </view>
            <view class="audio-info">
                <view class="audio-author-name">
                    <text class="audio-author">{{item.attr.name}}</text>
                    <text>{{item.attr.author}}</text>
                </view>
                <text>{{item.currentTime}}</text>
            </view>
        </view>
    </view>
</template>
<template name="wxParserImg">
    <image bindload="bindImgLoad" bindtap="tapWxParserImg" class="{{item.classStr}} wxParser-{{item.tag}}" data-from="{{item.from}}" data-idx="{{item.imgIndex}}" data-src="{{item.attr.src}}" lazyLoad="{{item.imageLazyLoad}}" mode="widthFix" src="{{item.attr.src}}" style="{{item.styleStr}}"></image>
</template>
<template name="wxParserText">
    <view class="{{item.classStr}} wxParserText wxParser-inline" style="{{item.styleStr}}">
        <text userSelect="true" wx:if="{{item.node=='text'}}">{{item.text}}</text>
    </view>
</template>
<template name="wxParser">
    <template is="wxParser0" data="{{item:item}}" wx:for="{{wxParserData}}" wx:key="*this"></template>
</template>
<template name="wxParser0">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser1" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser1">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="item"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser2" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser2">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser3" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser3">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser4" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser4">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser5" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser5">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser6" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser6">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser7" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser7">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser8" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser8">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser9" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser9">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser10" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser10">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser11" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser11">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser12" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser12">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser13" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser13">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser14" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser14">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser15" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser15">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser16" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser16">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser17" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser17">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser18" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser18">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser19" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
<template name="wxParser19">
    <block wx:if="{{item.node=='element'}}">
        <button size="mini" type="default" wx:if="{{item.tag=='button'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </button>
        <view class="{{item.classStr}} wxParser-ol-li" wx:elif="{{item.tag=='li'&&item.parent=='ol'}}">
            <view class="{{item.classStr}} wxParser-ol-li-inner">
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <view class="{{item.classStr}}">{{item.order}}.</view>
                </view>
                <view class="{{item.classStr}} wxParser-ol-li-text">
                    <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <view class="{{item.classStr}} wxParser-li" wx:elif="{{item.tag=='li'&&item.parent=='ul'}}">
            <view class="{{item.classStr}} wxParser-li-inner">
                <view class="{{item.classStr}} wxParser-li-text">
                    <view class="{{item.classStr}} wxParser-li-circle"></view>
                </view>
                <view class="{{item.classStr}} wxParser-li-text">
                    <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
                </view>
            </view>
        </view>
        <template is="wxParserVideo" data="{{item:item}}" wx:elif="{{item.tag=='video'}}"></template>
        <template is="wxParserAudio" data="{{item:item}}" wx:elif="{{item.tag=='audio'}}"></template>
        <template is="wxParserImg" data="{{item:item}}" wx:elif="{{item.tag=='img'}}"></template>
        <view bindtap="tapWxParserLink" class="wxParser-inline {{item.classStr}} wxParser-{{item.tag}}" data-href="{{item.attr.href}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='a'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='table'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='pre'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tag=='code'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}}" style="{{item.styleStr}}" wx:elif="{{item.tagType=='block'}}">
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
        <view class="{{item.classStr}} wxParser-{{item.tag}} wxParser-{{item.tagType}}" style="{{item.styleStr}}" wx:else>
            <template is="wxParser20" data="{{item:item}}" wx:for="{{item.nodes}}" wx:key="*this"></template>
        </view>
    </block>
    <template is="wxParserText" data="{{item:item}}" wx:elif="{{item.node=='text'}}"></template>
</template>
