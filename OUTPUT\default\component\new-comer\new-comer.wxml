<view class="newcomer-wrapper {{newcomerCouponsList&&newcomerCouponsList.length>0?'':'pad-new'}}">
    <view class="newc-ie-wra" wx:if="{{newcomerGoodsList&&newcomerGoodsList.length>0}}">
        <view bindtap="tapZhuanxiang" class="newc-xiao-title">
            <view class="goods-l">新人尝鲜品</view>
            <view class="goods-right">
                <text>查看更多></text>
            </view>
        </view>
        <scroll-view scrollX class="goods-list">
            <view bindtap="tapZhuanxiang" class="goods-item" wx:for="{{newcomerGoodsList}}">
                <view class="goods-img-wra">
                    <image lazyLoad src="{{filter.addImgPath350x350(item.picture)}}"></image>
                </view>
                <view class="goods-xinxi">
                    <view class="sp-mc">{{item.goodsName}}</view>
                    <view class="goods-xj">
                        <text>￥</text>{{filter.priceToFixed2(item.newcomerPrice)}}</view>
                    <view class="goods-yj">￥{{filter.priceToFixed2(item.markingPrice)}}</view>
                    <view catchtap="tapAddCart" class="add-icon kuoda" data-item="{{item}}">
                        <image src="/img/smicon/xrsp.png" wx:if="{{item.stock>0&&item.status!=3}}"></image>
                        <image src="/img/smicon/xrsp-hui.png" wx:else></image>
                    </view>
                </view>
            </view>
            <view bindtap="tapZhuanxiang" class="goods-item">
                <view class="ckgengduo">
                    <image src="../../img/smicon/see-more.png"></image>
                    <view class="see-gengduo">查看更多</view>
                </view>
            </view>
        </scroll-view>
    </view>
    <block wx:if="{{newcomerCouponsList&&newcomerCouponsList.length>0}}">
        <view class="newc-ie-wra margin-top-xs">
            <view bindtap="tapMyCoupon" class="newc-xiao-title goods-l">新人专享券</view>
            <scroll-view scrollX class="newc-coupon-list">
                <view bindtap="tapMyCoupon" class="newc-coupon-item" wx:for="{{newcomerCouponsList}}">
                    <view class="coupon-top" wx:if="{{item.couponType!=3}}">{{item.amount/100}}<text>元</text>
                    </view>
                    <view class="coupon-top" wx:if="{{item.couponType==3}}">
                        <view class="title">{{item.couponTitle}}</view>
                    </view>
                    <view class="coupon-bottom">{{item.subTitle}}</view>
                </view>
            </scroll-view>
        </view>
        <view class="dibu-kuang">
            <view class="miaobian"></view>
            <view bindtap="tapMyCoupon" class="dibu-qsy">
                <image src="https://obs.springland.com.cn/profile/upload/2023/02/16/dfa92d30b61edc298be135d0ca68847f.png"></image>
            </view>
        </view>
    </block>
</view>
<goods-spec bind:changeCartNum="changeCartNum" bind:chooseGuige="chooseGuige" bind:hideSpec="hideSpec" goodsInfo="{{goodsInfo}}" paramData="{{paramData}}" specShow="{{specShow}}"></goods-spec>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>