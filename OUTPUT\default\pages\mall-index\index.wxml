<view class="head">
    <input bindtap="clickSearchLocation" class="head_seach" disabled="true" placeholder="搜索商品"></input>
    <image class="head_seach_icon" mode="widthFix" src="./images/index_search.png"></image>
</view>
<view>
    <view class="container">
        <scroll-view class="nav" scrollLeft="{{navScrollLeft}}" scrollWithAnimation="{{true}}" scrollX="true">
            <view bindtap="switchNav" class="nav-item {{currentTab==idx?'active':''}} {{navItem.id==-1?'red_class':''}}" data-current="{{idx}}" data-id="{{navItem.id}}" data-type="1" wx:for="{{navData}}" wx:for-index="idx" wx:for-item="navItem" wx:key="idx">{{navItem.text}}</view>
        </scroll-view>
        <view bindtap="clickOpen" class="index_hd_rt">
            <image class="index_arrow" mode="widthFix" src="./images/index_arrow.png"></image>
        </view>
    </view>
    <view class="list clearfix" hidden="{{hiddenName}}">
        <view class="list_close">
            <text bindtap="clickClose">关闭</text>
        </view>
        <view class="list_tab clearfix">
            <view bindtap="switchNav" class="list_flex {{currentTab==idx?'list_flex_on':''}}" data-current="{{idx}}" data-id="{{navItem.id}}" data-type="2" wx:for="{{navData}}" wx:for-index="idx" wx:for-item="navItem" wx:key="idx">{{navItem.text}}</view>
        </view>
    </view>
</view>
<view class="index_content" wx:if="{{homeShow}}">
    <view class="head_cast">
        <swiper autoplay="{{autoplay}}" circular="true" class="swiper-box" duration="{{duration}}" indicatorDots="{{indicatorDots}}" interval="{{interval}}">
            <swiper-item wx:if="{{banners.length>0}}" wx:for="{{banners}}" wx:key="idx">
                <image bindtap="bannerLocation" class="slide-image" data-linkurl="{{item.url}}" mode="widthFix" src="{{item.img_url}}" wx:if="{{item.img_url}}"></image>
            </swiper-item>
        </swiper>
    </view>
    <view class="index_notice">
        <view class="notice_title">公告</view>
        <swiper autoplay="true" circular="true" class="swiper_container" interval="4000" vertical="true">
            <swiper-item wx:for="{{msgList}}" wx:key="idx">
                <view bindtap="articleLocation" class="swiper_item" data-id="{{item.id}}">{{item.title}}</view>
            </swiper-item>
        </swiper>
        <view bindtap="goNews" class="notice_more">更多</view>
    </view>
    <view class="floor">
        <view class="banner" wx:if="{{firstAdvert.img_url}}">
            <image bindtap="advertLocation" class="index_banner" data-linkurl="{{firstAdvert.url}}" mode="widthFix" src="{{firstAdvert.img_url}}"></image>
        </view>
        <view class="content" wx:if="{{isShow}}">
            <view class="title">
                <text class="title_text">邮寄爆品</text>
                <text bindtap="goMore" class="more" data-id="0">更多</text>
            </view>
            <view class="clearfix">
                <view bindtap="goodsDetailLocation" class="goods" data-id="{{item.id}}" wx:for="{{showGoods}}" wx:key="idx">
                    <view class="goods_img" data-id="{{item.id}}">
                        <image class="goods_image" data-id="{{item.id}}" src="{{item.img_url}}"></image>
                        <view class="goods_label" wx:if="{{item.prime==1}}">Prime会员专享</view>
                    </view>
                    <view class="goods_title" data-id="{{item.id}}">{{item.name}}</view>
                    <view class="goods_price" data-id="{{item.id}}">
                        <text>¥</text>{{item.price}} <view class="goods_buy">已售<text class="goods_num">{{item.sales_num}}</text>件</view>
                    </view>
                    <view class="goods_price_on">
                        <block wx:if="{{item.market_price}}"> ¥{{item.market_price}} </block>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="floor" wx:for="{{floors}}" wx:key="idx">
        <view class="banner">
            <image bindtap="advertLocation" class="index_banner" data-linkurl="{{item.advert.url}}" mode="widthFix" src="{{item.advert.img_url}}" wx:if="{{item.advert.url}}"></image>
        </view>
        <view class="content">
            <view class="title">
                <text class="title_text">{{item.name}}</text>
                <text bindtap="goMore" class="more" data-id="{{item.id}}">更多</text>
            </view>
            <view class="clearfix">
                <view bindtap="goodsDetailLocation" class="goods" data-id="{{item.id}}" wx:for="{{item.goods}}" wx:key="idxs">
                    <view class="goods_img" data-id="{{item.id}}">
                        <image class="goods_image" data-id="{{item.id}}" mode="widthFix" src="{{item.img_url}}"></image>
                        <view class="goods_label" wx:if="{{item.prime==1}}">Prime会员专享</view>
                    </view>
                    <view class="goods_title" data-id="{{item.id}}">{{item.name}}</view>
                    <view class="goods_price" data-id="{{item.id}}">
                        <text>¥</text>{{item.price}} <view class="goods_buy">已售<text class="goods_num">{{item.sales_num}}</text>件</view>
                    </view>
                    <view class="goods_price_on">
                        <block wx:if="{{item.market_price}}"> ¥{{item.market_price}} </block>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>
<view class="other_content" wx:if="{{show}}">
    <view class="other_hd">
        <scroll-view scrollX class="scroll_header">
            <view class="scroll_view_item" wx:for="{{sonCategory}}" wx:key="key">
                <view class="sort" wx:for="{{item}}" wx:key="key">
                    <view bindtap="clickCate" data-id="{{item.id}}">
                        <image class="sort_image" mode="scaleToFill" src="{{item.img_url}}"></image>
                        <view class="sort_title {{cateTab==item.id?'cate_active':''}}">{{item.name}}</view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
    <view class="tab">
        <view bindtap="clickSort" class="tab_flex {{sortType==1?'tab_choose':''}}" data-sort="normal" data-type="1">
            <text>综合</text>
        </view>
        <view bindtap="clickSort" class="tab_flex {{sortType==2?'tab_choose':''}}" data-sort="{{sortSale}}" data-type="2">
            <view>销量<image class="index_up" mode="widthFix" src="{{saleSrc}}" wx:if="{{saleShow}}"></image>
            </view>
        </view>
        <view bindtap="clickSort" class="tab_flex {{sortType==3?'tab_choose':''}}" data-sort="{{sortPrice}}" data-type="3">
            <view>价格<image class="index_up" mode="widthFix" src="{{priceSrc}}" wx:if="{{priceShow}}"></image>
            </view>
        </view>
    </view>
    <view class="content">
        <view class="clearfix">
            <view bindtap="goodsDetailLocation" class="goods" data-id="{{item.id}}" wx:for="{{goods}}" wx:key="key">
                <view class="goods_img" data-id="{{item.id}}">
                    <image class="goods_image" data-id="{{item.id}}" src="{{item.img_url}}"></image>
                    <view class="goods_label" data-id="{{item.id}}" wx:if="{{item.prime==1}}">Prime会员专享</view>
                </view>
                <view class="goods_title" data-id="{{item.id}}">{{item.name}}</view>
                <view class="goods_price" data-id="{{item.id}}">
                    <text>¥</text>{{item.price}}<view class="goods_buy">已售<text class="goods_num">{{item.sales_num}}</text>件</view>
                </view>
                <view class="goods_price_on">
                    <block wx:if="{{item.market_price}}"> ¥{{item.market_price}} </block>
                </view>
            </view>
        </view>
    </view>
</view>
<view class="index_car">
    <image class="index_car_bg" mode="widthFix" src="https://obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591955940930946.png"></image>
    <image bindtap="goCart" class="index_car_go" mode="widthFix" src="./images/index_bottom_car.png"></image>
</view>
<view class="index_content" wx:if="{{emptyShow}}">
    <image class="search_none" mode="widthFix" src="./images/search_none.png"></image>
    <view class="none_title">抱歉，暂无相应商品~</view>
</view>
