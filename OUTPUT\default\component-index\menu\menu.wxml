<view class="menu" wx:if="{{navigations.length!=0}}">
    <scroll-view bindscroll="onScroll" class="menu-swiper" enhanced="{{true}}" scrollX="{{true}}" showScrollbar="{{false}}">
        <view class="menu-group">
            <block wx:for="{{navigations}}" wx:key="key">
                <view catchtap="nav_link_url" class="menu-item" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url+'?mdid='+mdid}}" wx:if="{{item.name==='更多分类'||item.name==='积分商城'}}">
                    <image lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                    <view class="menu-name">{{item.name}}</view>
                </view>
                <view catchtap="nav_link_url" class="menu-item" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:else>
                    <image lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
                    <view class="menu-name">{{item.name}}</view>
                </view>
            </block>
        </view>
    </scroll-view>
    <progress activeColor="#505050" borderRadius="50" percent="{{per}}" strokeWidth="4" style="width: 100rpx;margin: auto;"></progress>
</view>
