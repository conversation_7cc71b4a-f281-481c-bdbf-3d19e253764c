<view class="container">
    <view class="shadow-wrapper">
        <view class="info-item">
            <view class="info-left">发票状态</view>
            <view class="info-right" wx:if="{{invoiceInfo.result==0}}">开票中</view>
            <view class="info-right" wx:elif="{{invoiceInfo.fphm&&invoiceInfo.fpdm&&!invoiceInfo.url}}">开票中</view>
            <view class="info-right" wx:elif="{{invoiceInfo.fphm&&invoiceInfo.fpdm&&invoiceInfo.url}}">开票成功</view>
            <view class="info-right" wx:else>开票失败</view>
        </view>
    </view>
    <view class="shadow-wrapper">
        <view class="info-item">
            <view class="info-left">订单状态</view>
            <view class="info-right">
                <block wx:if="{{invoiceInfo.isCancel==0&&invoiceInfo.isPay==0}}">待支付</block>
                <block wx:elif="{{invoiceInfo.isCancel==1}}">已取消</block>
                <block wx:elif="{{invoiceInfo.isPay==1}}">已购买</block>
                <block wx:else>未知状态</block>
            </view>
        </view>
        <view class="info-item">
            <view class="info-left">订单编号</view>
            <view class="info-right">{{invoiceInfo.orderNo}}</view>
        </view>
        <view class="info-item">
            <view class="info-left">下单时间</view>
            <view class="info-right">{{invoiceInfo.createTime}}</view>
        </view>
        <view class="info-item">
            <view class="info-left">发票类型</view>
            <view class="info-right">电子普通发票</view>
        </view>
    </view>
    <view class="shadow-wrapper">
        <view class="info-item">
            <view class="info-left">发票抬头</view>
            <view class="info-right">{{invoiceInfo.title}}</view>
        </view>
        <view class="info-item" wx:if="{{invoiceInfo.taxNumber}}">
            <view class="info-left">公司税号</view>
            <view class="info-right">{{invoiceInfo.taxNumber}}</view>
        </view>
        <view class="chakan"></view>
    </view>
</view>
<view class="padding flex flex-direction">
    <button bindtap="applyAgain" class="bg-chaoshi margin-tb-sm lg" wx:if="{{invoiceInfo.result!=0&&(!invoiceInfo.fphm||!invoiceInfo.fpdm)}}">重新申请</button>
    <button bindtap="showModal" class="bg-chaoshi1 margin-tb-sm lg" wx:if="{{invoiceInfo.result!=0&&invoiceInfo.fphm&&invoiceInfo.fpdm&&invoiceInfo.url}}">发送邮箱</button>
</view>
<modal-box class="{{modalShow?'show':''}}">
    <dialog>
        <bar class="justify-end">
            <view class="content">邮箱地址</view>
            <view bindtap="hideModal" class="action">
                <text class="icon-close text-red"></text>
            </view>
        </bar>
        <view class="padding">
            <view class="email-input">
                <input bindinput="emailInput" class="radius" placeholder="请填写正确的邮箱地址" value="{{invoiceInfo.email}}"></input>
            </view>
            <view class="btn-send-email">
                <button catchtap="sendEmail" class="bg-chaoshi11">发送</button>
            </view>
        </view>
    </dialog>
</modal-box>
