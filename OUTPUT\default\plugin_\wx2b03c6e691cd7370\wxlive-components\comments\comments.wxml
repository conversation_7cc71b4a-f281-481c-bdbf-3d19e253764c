
<wxs module="wxs">
module.exports = ({
  loadAvatar: (function(newValue, oldValue, ownerInstance, instance) {
    if (!newValue) {
      return
    };
    if (newValue !== oldValue) {
      var bgUrl = instance.getDataset().defaultAvatar;
      instance.setStyle(({
        'background-image': 'url(' + bgUrl + ')',
      }));
      instance.requestAnimationFrame((function() {
        instance.setStyle(({
          'background-image': 'url(' + newValue + ')',
        }))
      }))
    }
  }),
});
</wxs>