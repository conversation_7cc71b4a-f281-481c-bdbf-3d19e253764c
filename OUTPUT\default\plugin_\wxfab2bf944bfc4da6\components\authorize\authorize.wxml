<navigation-bar background="white" ctrl="back"></navigation-bar>
<view class="authorization">
    <view class="skip">
        <view class="skip-hd">
            <view class="skip-logo">
                <view class="skip-logo__mch">
                    <image src="{{logo}}"></image>
                </view>
                <view class="skip-logo__to">
                    <text class="arrow-to"></text>
                </view>
                <view class="skip-logo__pay">
                    <image src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images/wechat_logo.png"></image>
                </view>
            </view>
            <view class="skip-title"> 用微信支付，享快速积分 </view>
        </view>
        <view class="skip-bd">
            <view class="skip-info">
                <view class="skip-info__title">你授权<view class="skip-info__name">
                        <text class="name">{{circlename}}</text>
                        <text bindtap="showDialog" class="icon icon-ask"></text>
                    </view>使用以下信息，以开通快速积分服务</view>
                <view class="skip-info__des">会员信息(昵称、头像、手机号)</view>
                <view class="skip-info__des">商圈内消费的微信支付记录</view>
                <view class="skip-info__des">商圈内消费时的地理位置信息</view>
            </view>
        </view>
        <view class="skip-ft">
            <view class="skip-protocol">确认开通视为同意<text bindtap="goProtocol" class="link">《用户授权协议》</text>
            </view>
            <view class="btn__area">
                <button bindtap="goAuth" hoverClass="btn-hover" type="primary" wx:if="{{confirm_btn_status==0}}"> 确认开通 </button>
                <button disabled class="with-loading" hoverClass="btn-hover" type="primary" wx:else>
                    <i class="loading"></i>开通中 </button>
                <button bindtap="goBack" disabled="{{cancel_btn_status==1?true:false}}" hoverClass="btn-hover" style=" display: " type="default"> 暂不开通 </button>
            </view>
        </view>
    </view>
</view>
<view class="dialog-wrp" hidden="{{modalHidden}}">
    <view class="mask"></view>
    <view class="dialog">
        <view class="dialog-hd">
            <view class="dialog-title"> 获取位置信息失败 </view>
        </view>
        <view class="dialog-bd">
            <view class="dialog-bd__desc text-center">请前往设置打开位置信息授权后重试</view>
        </view>
        <view class="dialog-ft">
            <button bindtap="closeModal" hoverClass="other-button-hover" plain="true" type="default"> 关闭 </button>
            <button bindtap="closeModal" hoverClass="other-button-hover" openType="openSetting" plain="true" type="primary"> 前往授权 </button>
        </view>
    </view>
</view>
<view class="dialog-wrp" hidden="{{modal2Hidden}}">
    <view class="mask"></view>
    <view class="dialog">
        <view class="dialog-hd">
            <view class="dialog-title"> 获取位置信息失败 </view>
        </view>
        <view class="dialog-bd">
            <view class="dialog-bd__desc">解决方法：</view>
            <view class="dialog-bd__desc">1、检查手机定位功能是否开启</view>
            <view class="dialog-bd__desc">2、检查是否允许微信使用定位功能</view>
        </view>
        <view class="dialog-ft2">
            <view bindtap="closeModal" class="weui-dialog__btn">关闭</view>
        </view>
    </view>
</view>
