<form bindsubmit="invoiceSubmit">
    <form-group class="margin-top">
        <view class="title">订单编号</view>
        <input disabled class="radius" value="{{orderNo}}"></input>
    </form-group>
    <form-group>
        <view class="title">开票号</view>
        <input disabled class="radius" value="{{orderInfo.invNo}}"></input>
    </form-group>
    <form-group>
        <view class="title">金额（不含运费）</view>
        <input disabled class="radius" value="{{filter.priceToFixed2(orderInfo.payAmount)}}元"></input>
    </form-group>
    <form-group class="margin-top">
        <view class="title">发票类型</view>
        <input disabled class="radius" value="电子普通发票"></input>
    </form-group>
    <form-group>
        <view class="title">*抬头类型</view>
        <picker bindchange="invoiceTypeChange" range="{{invoiceType}}" value="{{invoiceIndex}}">
            <view class="picker {{invoiceIndex?'':'huise'}}"> {{invoiceIndex?invoiceType[invoiceIndex]:'请选择抬头类型'}} </view>
        </picker>
    </form-group>
    <form-group>
        <view class="title">*发票抬头</view>
        <picker disabled bindtap="chooseInvoice">
            <view class="picker {{invoiceInfo&&invoiceInfo.title?'':'huise'}}"> {{invoiceInfo&&invoiceInfo.title?invoiceInfo.title:'请选择发票抬头'}} </view>
        </picker>
    </form-group>
    <block wx:if="{{invoiceIndex==1}}">
        <form-group>
            <view class="title">单位税号</view>
            <input class="radius" name="taxNumber" placeholder="非必填" value="{{invoiceInfo.taxNumber}}"></input>
        </form-group>
        <form-group>
            <view class="title">注册地址</view>
            <input class="radius" name="address" placeholder="非必填" value="{{invoiceInfo.companyAddress}}"></input>
        </form-group>
        <form-group>
            <view class="title">注册电话</view>
            <input class="radius" name="phone" placeholder="非必填" value="{{invoiceInfo.telephone}}"></input>
        </form-group>
        <form-group>
            <view class="title">开户银行</view>
            <input class="radius" name="bank" placeholder="非必填" value="{{invoiceInfo.bankName}}"></input>
        </form-group>
        <form-group>
            <view class="title">银行账号</view>
            <input class="radius" name="bankAccount" placeholder="非必填" value="{{invoiceInfo.bankAccount}}"></input>
        </form-group>
    </block>
    <form-group class="margin-top">
        <view class="title">手机号码</view>
        <input disabled class="radius" name="mobile" value="{{orderInfo.phone}}"></input>
    </form-group>
    <form-group>
        <view class="title">电子邮箱</view>
        <input class="radius" name="email" placeholder="请填写您的电子邮箱"></input>
    </form-group>
    <view class="padding flex flex-direction">
        <button class="bg-chaoshi margin-tb-sm lg" formType="submit">申请开票</button>
    </view>
</form>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>