<view>
    <view>
        <image class="top-img" src="{{item.image}}" style="750rpx"></image>
    </view>
    <view class="info">
        <view class="salename">{{item.giftName}}</view>
        <view class="salenum">
            <text wx:if="{{fullGift.showStock}}">剩余库存：{{item.num-item.receiveNum}}</text>
        </view>
    </view>
</view>
<view class="saledetail">
    <view class="title"> 领取规则 </view>
    <view class="main">
        <view class="container">
            <view class="uinn">
                <text class="icon-time text-grey"></text>
                <text class="text-grey timetext">消费时间：{{fullGift.startTime}} 至 {{fullGift.endTime}}
</text>
                <block wx:if="{{fullGift.type==1}}">
                    <text class="icon-roundright text-grey" wx:if="{{item.moneyQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.moneyQuota>0}}">今日</text>
                    <text class="text-grey timetext" wx:if="{{item.moneyQuota>0}}">单笔消费满{{item.moneyQuota}}元
</text>
                    <text class="icon-roundright text-grey" wx:if="{{item.cashQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.cashQuota>0}}">今日</text>
                    <text class="text-grey timetext" wx:if="{{item.cashQuota>0}}">单笔现金消费满{{item.cashQuota}}元
</text>
                    <text class="icon-roundright text-grey" wx:if="{{item.integralQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.integralQuota>0}}">今日</text>
                    <text class="text-grey timetext" wx:if="{{item.integralQuota>0}}">单笔消费产生积分大于{{item.integralQuota}}分
</text>
                </block>
                <block wx:else>
                    <text class="icon-roundright text-grey" wx:if="{{item.moneyQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.moneyQuota>0}}">今日</text>
                    <text class="text-grey" wx:if="{{!fullGift.today&&item.moneyQuota>0}}">单日</text>
                    <text class="text-grey timetext" wx:if="{{item.moneyQuota>0}}">累计消费满{{item.moneyQuota}}元
</text>
                    <text class="icon-roundright text-grey" wx:if="{{item.cashQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.cashQuota>0}}">今日</text>
                    <text class="text-grey" wx:if="{{!fullGift.today&&item.cashQuota>0}}">单日</text>
                    <text class="text-grey timetext" wx:if="{{item.cashQuota>0}}">累计现金消费满{{item.cashQuota}}元
</text>
                    <text class="icon-roundright text-grey" wx:if="{{item.integralQuota>0}}"></text>
                    <text class="text-grey" wx:if="{{fullGift.today&&item.integralQuota>0}}">今日</text>
                    <text class="text-grey" wx:if="{{!fullGift.today&&item.integralQuota>0}}">单日</text>
                    <text class="text-grey timetext" wx:if="{{item.integralQuota>0}}">累计消费产生积分大于{{item.integralQuota}}分
</text>
                </block>
                <text class="icon-roundright text-grey" wx:if="{{item.maxTimes>0}}"></text>
                <text class="text-grey timetext" wx:if="{{item.maxTimes>0}}">活动期内最多可领取{{item.maxTimes}}次</text>
            </view>
        </view>
    </view>
</view>
<view class="saledetail">
    <view class="title"> 核销地点 </view>
    <view class="main">
        <view class="container">
            <view class="uinn">
                <text>{{item.checkPlace}}</text>
            </view>
        </view>
    </view>
</view>
<view class="saledetail">
    <view class="title"> 礼品详情 </view>
    <view class="main">
        <view class="container">
            <view class="uinn">
                <text>{{item.description}}</text>
            </view>
        </view>
    </view>
</view>
<view class="blank"></view>
<view class="paybar">
    <view class="bar-left">
        <text decode="{{true}}" space="{{true}}">&nbsp;&nbsp;</text>
        <text></text>
    </view>
    <button bindtap="confirm" class="userInfo-btn-wrapper" disabled="{{!receiveStatus}}">
        <view class="bar-right">领取</view>
    </button>
</view>
