/**
 * 最终Authorization验证测试
 * 基于OUTPUT文件夹中找到的实际代码进行验证
 */

const crypto = require('crypto');

// 从抓包数据中提取的信息
const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
const hyid = "820536796";
const mdid = "6021";

// 从OUTPUT/default/utils/constant.js中的常量
const REQUEST_KEY = "SPRINGLAND";
const REQUEST_SECRET = "springland*&^0627@";

function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

function sha1(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

/**
 * 基于OUTPUT/default/utils/sha1.js的getSign函数实现
 * 这是小程序中实际使用的签名算法
 */
function getSignFromSha1Utils(url, params) {
    const t = REQUEST_KEY;
    const u = REQUEST_SECRET;
    const a = params;
    
    // 获取参数key并排序
    const i = [];
    for (const f in a) {
        i.push(f);
    }
    i.sort();
    
    // 构建签名数组
    const o = [];
    o.push(t); // SPRINGLAND
    o.push("http://ygxcxtest.springland.cn:8089/api" + url); // API URL
    
    // 添加排序后的参数
    for (const s in i) {
        const p = i[s];
        o.push(p + a[p]);
    }
    
    o.push(u); // springland*&^0627@
    
    const h = o.join("");
    console.log("SHA1Utils签名字符串:", h);
    
    // 进行SHA1加密并转大写（原代码最后有.toUpperCase()）
    return sha1(h).toUpperCase();
}

/**
 * 基于OUTPUT/default/yjh-config/config/ajax.js的签名算法
 * 这个算法返回小写的SHA1
 */
function getSignFromAjax(params) {
    const sortedKeys = Object.keys(params).sort();
    
    let paramStr = "";
    for (let i = 0; i < sortedKeys.length; i++) {
        const key = sortedKeys[i];
        const value = params[key];
        if (typeof value === 'object') {
            paramStr += key + JSON.stringify(value);
        } else {
            paramStr += key + value;
        }
    }
    
    const signString = "2osDKbBC*sjdOOsa" + paramStr + "2osDKbBC*sjdOOsa";
    console.log("Ajax签名字符串:", signString);
    
    // 返回小写SHA1（原代码没有.toUpperCase()）
    return sha1(signString);
}

/**
 * 测试所有可能的Authorization生成方式
 */
function testAllMethods() {
    console.log("=== 最终Authorization验证测试 ===");
    console.log("目标Authorization:", targetAuth);
    console.log("原始Token:", originalToken);
    console.log("HYID:", hyid);
    console.log("MDID:", mdid);
    console.log();
    
    const requestParams = {
        hyid: hyid,
        mdid: mdid
    };
    
    // 测试1: 使用utils/sha1.js的getSign方法
    console.log("--- 测试1: utils/sha1.js的getSign方法 ---");
    const sha1UtilsResult = getSignFromSha1Utils("/sign/sign", requestParams);
    console.log("生成结果:", sha1UtilsResult);
    console.log("小写结果:", sha1UtilsResult.toLowerCase());
    console.log("是否匹配:", sha1UtilsResult.toLowerCase() === targetAuth);
    console.log();
    
    // 测试2: 使用ajax.js的签名方法
    console.log("--- 测试2: ajax.js的签名方法 ---");
    const ajaxResult = getSignFromAjax(requestParams);
    console.log("生成结果:", ajaxResult);
    console.log("是否匹配:", ajaxResult === targetAuth);
    console.log();
    
    // 测试3: 验证我们之前发现的MD5模式
    console.log("--- 测试3: MD5模式验证 ---");
    const hyidMd5 = md5(hyid);
    console.log("HYID的MD5:", hyidMd5);
    console.log("前8位:", hyidMd5.substring(0, 8));
    console.log("目标前8位:", targetAuth.substring(0, 8));
    console.log("前8位匹配:", hyidMd5.substring(0, 8) === targetAuth.substring(0, 8));
    console.log();
    
    // 测试4: 尝试基于token的各种组合
    console.log("--- 测试4: 基于token的组合 ---");
    const tokenCombinations = [
        originalToken,
        originalToken + hyid,
        originalToken + mdid,
        originalToken + hyid + mdid,
        hyid + originalToken,
        mdid + originalToken,
        hyid + mdid + originalToken,
        originalToken.substring(8), // 去掉前8位
        originalToken.substring(8) + hyid,
        originalToken.substring(8) + mdid,
        originalToken.substring(8) + hyid + mdid,
    ];
    
    tokenCombinations.forEach((combo, i) => {
        const md5Result = md5(combo);
        const sha1Result = sha1(combo);
        
        if (md5Result === targetAuth || sha1Result === targetAuth) {
            console.log(`✓ 找到匹配! 组合${i+1}: "${combo.substring(0, 50)}..."`);
            if (md5Result === targetAuth) console.log("  MD5匹配");
            if (sha1Result === targetAuth) console.log("  SHA1匹配");
        }
    });
    
    // 测试5: 尝试特殊的组合模式
    console.log("\n--- 测试5: 特殊组合模式 ---");
    
    // 基于前8位匹配的发现，尝试构造完整的Authorization
    const prefix = hyidMd5.substring(0, 8); // 1fb508f5
    const targetSuffix = targetAuth.substring(8); // a4e6557df6cb447aa314544f308a7a5d
    
    console.log("尝试构造Authorization:");
    console.log("前缀 (MD5(hyid)前8位):", prefix);
    console.log("目标后缀:", targetSuffix);
    
    // 尝试各种方式生成后32位
    const suffixCombinations = [
        mdid,
        hyid + mdid,
        mdid + hyid,
        originalToken.substring(8),
        originalToken.substring(8) + hyid,
        originalToken.substring(8) + mdid,
        originalToken.substring(8) + hyid + mdid,
        REQUEST_KEY + hyid + mdid,
        hyid + mdid + REQUEST_SECRET,
        REQUEST_KEY + hyid + mdid + REQUEST_SECRET,
    ];
    
    suffixCombinations.forEach((combo, i) => {
        const md5Result = md5(combo);
        const sha1Result = sha1(combo);
        
        // 检查MD5的前32位
        if (md5Result.substring(0, 32) === targetSuffix) {
            const fullAuth = prefix + md5Result.substring(0, 32);
            console.log(`✓ MD5匹配! 组合${i+1}: "${combo}"`);
            console.log(`  完整Authorization: ${fullAuth}`);
            console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
        }
        
        // 检查SHA1的前32位
        if (sha1Result.substring(0, 32) === targetSuffix) {
            const fullAuth = prefix + sha1Result.substring(0, 32);
            console.log(`✓ SHA1匹配! 组合${i+1}: "${combo}"`);
            console.log(`  完整Authorization: ${fullAuth}`);
            console.log(`  验证: ${fullAuth === targetAuth ? '✓ 完全匹配!' : '✗ 不匹配'}`);
        }
        
        // 检查完整的32位
        if (md5Result === targetSuffix) {
            console.log(`✓ MD5完整匹配! 组合${i+1}: "${combo}"`);
        }
        
        if (sha1Result === targetSuffix) {
            console.log(`✓ SHA1完整匹配! 组合${i+1}: "${combo}"`);
        }
    });
    
    console.log("\n=== 总结 ===");
    console.log("1. Authorization前8位 = MD5(hyid)的前8位 ✓ 已确认");
    console.log("2. 后32位的生成方法仍需进一步分析");
    console.log("3. 可能涉及服务器端的动态参数或会话信息");
}

// 运行测试
testAllMethods();
