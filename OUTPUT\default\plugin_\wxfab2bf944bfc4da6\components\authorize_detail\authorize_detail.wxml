<view class="authorize-detail">
    <view class="plugin-container">
        <view class="container-hd">
            <view class="container-hd__logo">
                <image src="{{main_logo}}"></image>
            </view>
            <view class="container-hd__title">{{main_name}}</view>
        </view>
        <view class="container-bd">
            <view class="form-preview">
                <view class="form-preview__hd">服务状态</view>
                <view class="form-preview__bd" wx:if="{{auth_status==1}}">
                    <text class="green">已开通</text>
                </view>
                <view class="form-preview__bd" wx:if="{{auth_status==2}}">
                    <text>已关闭</text>
                </view>
            </view>
            <view class="form-preview">
                <view class="form-preview__hd">开通时间</view>
                <view class="form-preview__bd">{{auth_time}}</view>
            </view>
        </view>
        <view class="container-ft" wx:if="{{auth_status==1}}">
            <text bindtap="openModal" class="link">关闭服务</text>
        </view>
    </view>
</view>
<view class="dialog-wrp " style="display:{{modalHidden?'none':''}} ">
    <view class="mask"></view>
    <view class="dialog">
        <view class="dialog-bd">
            <view class="dialog-bd__desc">关闭后，将终止商圈快速积分服务</view>
            <view class="dialog-bd__desc">（已累积积分不受影响）</view>
        </view>
        <view class="dialog-ft">
            <button bindtap="closeModal" hoverClass="other-button-hover" plain="true" type="default"> 取消 </button>
            <button bindtap="closeAuth" hoverClass="other-button-hover" plain="true" type="warn"> 确认关闭 </button>
        </view>
    </view>
</view>
