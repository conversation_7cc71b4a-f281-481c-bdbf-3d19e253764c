@import "..\..\colorui.wxss";

.container {
    padding: 30rpx 20rpx 0
}

.shadow-wrapper {
    background: #fff;
    border-radius: 8rpx;
    margin-bottom: 20rpx;
    padding: 20rpx
}

.info-item {
    display: flex;
    font-size: 28rpx;
    line-height: 56rpx
}

.info-left {
    color: #666;
    flex: 0 0 130rpx
}

.info-right {
    flex: 1
}

.chakan {
    margin: 10rox 0;
    text-align: right
}

.chakan-btn {
    border: 1rpx solid #999;
    border-radius: 50rpx;
    color: #777;
    display: inline-block;
    font-size: 28rpx;
    line-height: 50rpx;
    margin-right: 20rpx;
    padding: 0 20rpx
}

.bg-chaoshi {
    background: #ee2424!important
}

.bg-chaoshi,.bg-chaoshi1 {
    color: #fff;
    font-size: 36rpx
}

.bg-chaoshi1,.bg-chaoshi11 {
    background: #6cb55a!important
}

.bg-chaoshi11 {
    color: #fff;
    margin-top: 30rpx;
    padding: 18rpx 46rpx
}

.email-input {
    background: #eee;
    border-radius: 8rpx;
    padding: 10rpx 20rpx;
    text-align: left
}
