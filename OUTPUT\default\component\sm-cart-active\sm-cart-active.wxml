<view class="container {{cartList.length>0?'':'no-data-con'}}">
    <checkbox-group bindchange="checkboxChange">
        <view class="cart-list" wx:if="{{cartList.length>0}}">
            <view class="hd_city">
                <view catchtap="showStoreList" class="city_cur">
                    <image class="city-left" src="/img/csicon/cart-dw.png"></image>超市门店：{{storeName}}<image class="city-right" src="/img/csicon/cart-qh.png"></image>
                </view>
                <view bindtap="showCouponModel" class="city_cho" wx:if="{{couponList&&couponList.length>0}}">可用券<image class="city_icon" src="/img/csicon/cart-gd.png"></image>
                </view>
            </view>
            <view class="cart-item" wx:for="{{showCartList}}" wx:for-index="in" wx:for-item="it" wx:key="id">
                <view bindtap="goFullminusList" class="cart-bt" data-infoid="{{it.id}}" data-ruleid="{{it.ruleId}}" data-setmealid="{{it.setMealId}}" wx:if="{{it.id}}">
                    <view class="manjian {{it.setMealId?'':'manjian-wei'}}">
                        <view class="check-wra" wx:if="{{it.setMealId}}">
                            <checkbox checked="{{it.list[0].checked}}" disabled="{{cs_checkbox_disable}}" value="tc_{{it.setMealId}}"></checkbox>
                        </view>
                        <view class="manjian-left">
                            <text wx:if="{{it.ruleId}}">满减</text>
                            <text wx:elif="{{it.setMealId}}">套餐</text>
                            <text wx:elif="{{it.id<0}}">套装</text>
                            <text wx:else>活动</text>{{it.theme}}<block wx:if="{{it.discountInfo}}">，{{it.discountInfo}}</block>
                        </view>
                        <view class="goods_choose_top" wx:if="{{it.setMealId}}">
                            <view catchtap="reduceTcNum" class="goods_add kuoda" data-it="{{it}}">
                                <image class="" src="../../img/smicon/icon_reduce.png"></image>
                            </view>
                            <input bindblur="cartNumTcBlur" class="goods_int" data-in="{{in}}" maxlength="3" type="number" value="{{it.list[0].setMealNum}}"></input>
                            <view catchtap="addTcNum" class="goods_add kuoda" data-it="{{it}}">
                                <image class="" src="../../img/smicon/icon_plus.png"></image>
                            </view>
                        </view>
                        <view class="manjian-right" wx:else>去凑单</view>
                    </view>
                </view>
                <view class="sps">
                    <mp-slideview bindbuttontap="slideButtonTap" buttons="{{slideButtons}}" class="sp-item" data-id="{{item.id}}" data-setmealid="{{item.setMealId}}" disable="{{item.setMealId}}" wx:for="{{it.list}}" wx:key="id">
                        <view class="zhu-con">
                            <view class="check-wra">
                                <checkbox checked="{{item.checked}}" disabled="{{cs_checkbox_disable}}" value="sp_{{item.id}}" wx:if="{{!item.setMealId}}"></checkbox>
                            </view>
                            <view class="sp-img">
                                <image lazyLoad class="sp-url" src="{{filter.addImgPath350x350(item.goodsPic)}}"></image>
                                <image class="sp-freeze" mode="heightFix" src="{{filter.addImgPath(item.freezeUrl)}}" wx:if="{{item.freezeUrl}}"></image>
                                <image class="sp-sale" mode="heightFix" src="{{filter.addImgPath(item.saleUrl)}}" wx:if="{{item.saleUrl}}"></image>
                            </view>
                            <view bindtap="goGoodsDetail" class="sp-main" data-newcomerid="{{item.newcomerId}}" data-presaleid="{{item.preSaleId}}" data-sellid="{{item.sellId}}">
                                <view class="sp-name">{{item.goodsName}} <view class="jinyouji" wx:if="{{item.mailOnly==1}}">仅邮寄</view>
                                    <view class="jinyouji" wx:elif="{{item.mailOnly==2}}">仅自提</view>
                                    <view class="jinyouji" wx:elif="{{item.mailOnly==3}}">仅配送</view>
                                </view>
                                <view class="guige" wx:if="{{item.remark}}">{{item.remark}}</view>
                                <view>
                                    <view class="sp-manjian" wx:if="{{item.preSaleId}}">预售</view>
                                    <view class="sp-manjian" wx:if="{{item.newcomerId}}">新人专享</view>
                                </view>
                                <view class="sp-jg">￥{{filter.priceToFixed2(item.price)}}<text wx:if="{{item.markingPrice>item.price}}">￥{{filter.priceToFixed2(item.markingPrice)}}</text>
                                </view>
                            </view>
                        </view>
                        <view class="num-chang">
                            <view class="goods_choose1" wx:if="{{item.setMealId}}"> ×{{item.num}} </view>
                            <view class="goods_choose" wx:else>
                                <view catchtap="reduceNum" class="goods_add kuoda" data-id="{{item.id}}">
                                    <image class="" src="../../img/smicon/icon_reduce.png"></image>
                                </view>
                                <input bindblur="cartNumBlur" class="goods_int" data-item="{{item}}" maxlength="3" type="number" value="{{item.num}}"></input>
                                <view catchtap="addNum" class="goods_add kuoda" data-id="{{item.id}}">
                                    <image class="" src="../../img/smicon/icon_plus.png"></image>
                                </view>
                            </view>
                        </view>
                    </mp-slideview>
                </view>
            </view>
        </view>
        <slot></slot>
        <view class="shixiao-list" wx:if="{{cartList.length>0&&shixiaoList.length>0}}">
            <view class="cart-item">
                <view class="cart-bt bt-padding">
                    <view>超市已失效</view>
                    <view bindtap="clearCart" class="qingkong-gwc">清空失效商品</view>
                </view>
                <view class="sps">
                    <view class="sp-item" wx:for="{{shixiaoList}}">
                        <view class="zhu-con">
                            <view class="sp-img">
                                <image lazyLoad class="sp-url" src="{{filter.addImgPath350x350(item.goodsPic)}}"></image>
                            </view>
                            <view class="sp-main">
                                <view class="sp-name">{{item.goodsName}}</view>
                                <view class="sp-jg">￥{{filter.priceToFixed2(item.price)}}<text wx:if="{{item.markingPrice>item.price}}">￥{{filter.priceToFixed2(item.markingPrice)}}</text>
                                </view>
                            </view>
                        </view>
                        <view class="num-chang">
                            <view class="goods_choose1">×<block wx:if="{{item.setMealId}}">{{item.setMealNum}}</block>
                                <block wx:else>{{item.num}}</block>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </checkbox-group>
</view>
<modal-box class="bottom-modal {{storeListShow?'show':''}}">
    <view bindtap="hideStoreList" class="beijing"></view>
    <dialog class="bg-white">
        <view class="coupon_popup">
            <view class="coupon_hd">请选择门店<image catchtap="hideStoreList" class="shop_close" src="/yjh-config/images/shop_close.png"></image>
            </view>
            <view class="city_content">
                <radio-group bindchange="storeChange" class="radio-group">
                    <label class="radio" wx:for="{{storeList}}" wx:for-index="key" wx:key="key">
                        <view class="radio_list">
                            <radio checked="{{item.checked}}" value="{{key}}"></radio>
                            <view class="city_list">{{item.storeName}}</view>
                        </view>
                    </label>
                </radio-group>
            </view>
            <view catchtap="storeChangeConfirm" class="coupon_btn">确定</view>
        </view>
    </dialog>
</modal-box>
<modal-box class="bottom-modal {{couponShow?'show':''}}">
    <view bindtap="hideCouponModel" class="beijing"></view>
    <dialog class="bg-white">
        <view class="coupon_popup1">
            <view class="coupon_hd1">优惠券<image catchtap="hideCouponModel" class="shop_close1" src="/yjh-config/images/shop_close.png"></image>
            </view>
            <scroll-view scrollY class="city_content1">
                <view class="coupon-list">
                    <view bindtap="receiveCoupon" class="coupon-item" data-coupon="{{item}}" wx:for="{{couponList}}" wx:key="id">
                        <view class="coupon-left">
                            <image src="/img/smicon/coupon-lf.png"></image>
                            <view class="jiage-info">
                                <view class="coupon-mz">￥<text>{{filter.priceToFixed2(item.couponValue)}}</text>
                                </view>
                                <view class="coupon-fbt">{{item.subTitle}}</view>
                            </view>
                        </view>
                        <view class="coupon-right">
                            <view class="coupon-right-bg" wx:if="{{item.hasQty>0}}">
                                <image src="https://obs.springland.com.cn/mg-mp/image/20240220153400.png"></image>
                            </view>
                            <view>
                                <view class="coupon-title">{{item.title}}</view>
                                <view class="coupon-jinxian">[仅限线上购物使用]</view>
                                <view class="coupon-xianpinlei" wx:if="{{item.deptNote}}">限品类：{{item.deptNote}}</view>
                                <view class="coupon-sj">有效期：{{item.useBeginTime}}~{{item.useEndTime}}</view>
                            </view>
                            <view class="cp-rg-bottom">
                                <view></view>
                                <view class="coupon-qsy" wx:if="{{item.type==2}}">{{item.hasQty>0?'去凑单':item.canReceiveNumber>0?'立即兑换':'已兑换'}}</view>
                                <view class="coupon-qsy" wx:else>{{item.hasQty>0?'去凑单':item.canReceiveNumber>0?'立即领取':'已领取'}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </dialog>
</modal-box>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>