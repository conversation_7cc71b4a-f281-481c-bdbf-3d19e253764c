<view class="switchtype">
    <view class="type-item">
        <view bindtap="switchtype" class="typetab {{type=='JX'?'type-active':''}}" data-type="JX">精选直播</view>
    </view>
    <view class="type-item">
        <view bindtap="switchtype" class="typetab {{type=='BH'?'type-active':''}}" data-type="BH">八佰伴精选</view>
    </view>
    <view class="type-item">
        <view bindtap="switchtype" class="typetab {{type=='CS'?'type-active':''}}" data-type="CS">超市精选</view>
    </view>
</view>
<view class="head">
    <view bindtap="chooseLocation" class="mall" wx:if="{{type=='BH'}}">观看门店：{{mallname}} <image src="/img/icon/change.png"></image>
    </view>
    <swiper autoplay="false" duration="500" indicatorColor="rgba(0, 0, 0, 0.3)" indicatorDots="true" interval="5000">
        <swiper-item bindtap="gotoRoom" data-roomid="{{it.roomid}}" wx:for="{{banner}}" wx:for-index="inx" wx:for-item="it">
            <view class="main-img">
                <image class="img1" src="{{it.imgurl}}"></image>
            </view>
        </swiper-item>
    </swiper>
    <view class="tab">
        <view bindtap="switchtab" class="tab-item {{islive=='1'?'tab-active':''}}" data-islive="1"> 精彩直播 </view>
        <view bindtap="switchtab" class="tab-item {{islive=='2'?'tab-active':''}}" data-islive="2"> 直播回放 </view>
    </view>
    <view class="list">
        <view bindtap="gotoRoom" class="room" data-roomid="{{it.roomid}}" wx:for="{{roomList}}" wx:for-item="it">
            <image class="pic1" lazyLoad="true" src="{{it.shareImg}}"></image>
            <view class="status status-{{it.liveStatus}}">
                <text decode="true">&ensp;</text>{{it.liveStatus==101?'直播中':it.liveStatus==102?'未开始':it.liveStatus==107?'已过期':it.liveStatus==103?'已结束':it.liveStatus==104?'禁播':it.liveStatus==105?'暂停中':it.liveStatus==106?'异常':''}}<text decode="true">&ensp;</text>
            </view>
            <view class="title">
                <view class="name">{{it.name}}</view>
                <view class="time">{{it.date}} {{it.startHour}}</view>
                <view catchtap="sharelive" class="share-box" data-roomid="{{it.roomid}}">
                    <view catchtap="sharelive" class="txt-share" data-roomid="{{it.roomid}}">分享</view>
                    <image catchtap="sharelive" class="img-share" data-roomid="{{it.roomid}}" src="/img/live/share-red.png"></image>
                </view>
            </view>
            <view class="goods">
                <view class="good" wx:for="{{it.goods}}" wx:for-item="it2">
                    <image class="goodpic" lazyLoad="true" src="{{it2.coverImg}}"></image>
                </view>
            </view>
        </view>
    </view>
    <view class="buttom-line"> - 我是有底线的 - </view>
    <view class="shareview" wx:if="{{isShare==1}}">
        <view class="shadow"></view>
        <view class="share-model">
            <view class="share-main">
                <view class="share-app">八佰伴智慧购</view>
                <view class="share-name">{{shareRoom.name}}</view>
                <image class="share-image" src="{{shareRoom.shareImg}}"></image>
                <image class="share-qrcode" src="{{shareQrcode}}"></image>
                <view class="share-des">长按识别小程序码观看直播</view>
            </view>
            <button class="share-friend" openType="share">
                <view class="st1">微信好友</view>
            </button>
            <view catchtap="savehaibao" class="share-haibao">
                <view class="st1">保存海报</view>
            </view>
            <image catchtap="choseShare" class="closeShare" src="/img/icon/<EMAIL>"></image>
        </view>
    </view>
</view>
