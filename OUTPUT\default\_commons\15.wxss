page {
    width: 100%;
    height: 100%
}

.no-events .component {
    pointer-events: auto
}

.live-player-room__body__hidden .component {
    pointer-events: none
}

.live-player {
    position: relative;
    width: 100%;
    height: 100%
}

.no-events .component .live-player.live-player__operation .live-page-1:after {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background-color: rgba(0,0,0,.3)
}

.live-page-0 {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    z-index: 0
}

.live-page-0:before,.live-page-cover:before {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20%;
    z-index: 1;
    background-image: linear-gradient(0deg,transparent,rgba(0,0,0,.16) 40%,rgba(0,0,0,.24))
}

.live-player__no-comments .mode-subscribe-card {
    position: static;
    bottom: auto
}

.live-bottom-page__playing {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden
}

.live-page-0:after,.live-page-cover:after {
    content: " ";
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30%;
    z-index: 1;
    background-image: linear-gradient(0deg,rgba(0,0,0,.24),rgba(0,0,0,.16) 40%,transparent)
}

.live-bottom-page__backup-cover .live-bottom-page__cover__image,.live-bottom-page__backup-cover__image,.live-bottom-page__cover__image {
    display: block;
    height: 100%;
    width: 100%;
    background-color: var(--bgColor-cover)
}

.live-bottom-page__cover {
    position: relative
}

.live-bottom-page__cover__image__cover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: rgba(0,0,0,.8);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px)
}

.live-page-1 {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 20;
    padding-bottom: 8px;
    box-sizing: border-box
}

.live-player__bottom-area-safe .live-page-1 {
    padding-bottom: 41px;
    box-sizing: border-box
}

.mode__attention-guide {
    position: fixed;
    z-index: 34
}

.live-page-1.live-player__replay {
    padding-bottom: 59px
}

.live-player__bottom-area-safe .live-player-room__body__msg-list.live-player-room__body__msg-list__with-push-comment {
    position: relative;
    bottom: -41px
}

.live-player-ready-countdown {
    background-color: rgba(0,0,0,.3)
}

.live-player-room {
    width: 100%;
    height: 100%
}

.live-player-room__inner {
    height: 100%;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: stretch;
    align-items: stretch
}

.live-player-normal__head {
    position: relative;
    z-index: 1;
    padding: 0 16px;
    display: -webkit-flex;
    display: flex
}

.live-player__end .live-player-normal__head {
    z-index: 3
}

.mod-navigation-bar {
    margin-right: 18px;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.mode-normal-navigation-bar__title-center .weui-navigation-bar__body .weui-navigation-bar__center {
    position: absolute;
    left: 0;
    right: 0;
    pointer-events: none
}

.mod-comments {
    position: relative;
    display: block;
    width: 100%;
    margin-top: 8px;
    -webkit-mask-image: -webkit-gradient(linear,left top,left 20,from(transparent),to(#000))
}

.mod-comments.layer_top {
    z-index: 1
}

.mod-activity-card {
    display: block
}

.mod__activity-store-card {
    display: block;
    width: 100%
}

.mod-profile-modal {
    height: 100%;
    z-index: 33
}

.mod-lottery-oper,.mod-profile-modal {
    position: absolute;
    top: 0;
    left: 0;
    right: 0
}

.mod-lottery-oper {
    bottom: 0;
    z-index: 1003
}

.live-page-2col {
    width: 100%;
    height: 100%
}

.live-page-2col__inner {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    color: #fff;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    height: 100%
}

.mode__navigation__empty .button__share,.mode__navigation__empty .weui-navigation-bar__center,.mode__navigation__empty .weui-navigation-bar__left {
    position: absolute;
    left: -999px
}

.mode-person-operation {
    width: 100%
}

.live-player-main {
    position: relative;
    z-index: 2
}

.live-player-main.live-player-main__with-user-data {
    top: -36px
}

.live-player-main.live-player-main__with-recommend-area {
    top: 45px;
    -webkit-flex: 1;
    flex: 1;
    -webkit-justify-content: flex-start;
    justify-content: flex-start
}

.live-player-main.live-player-main__with-user-data .mode__count-tim {
    position: relative;
    top: -36px
}

.mode-normal-navigation-bar {
    display: block;
    color: rgba(0,0,0,.9);
    background-color: #fff
}

.profile__extend {
    position: absolute;
    top: 48px;
    left: 24px
}

.profile__extend-no-back {
    left: 0
}

.live-player-room__page {
    padding: 0 12px;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -webkit-flex-direction: column;
    flex-direction: column
}

.live-player-room__body__hidden {
    opacity: 0
}

.live-player-room__body.live-player-room__body__comment {
    padding: 0 20px
}

.live-player-room__system-msg {
    position: relative
}

.live-player-room__body__store-list {
    position: relative;
    z-index: 21
}

.live-player-living .live-player-room__body.live-player-room__body__store-list,.live-player__replay .live-player-room__body.live-player-room__body__store-list {
    z-index: 3
}

.live-player-living .live-player-room__body.live-player-room__body__store-list,.live-player__replay .live-player-room__body.live-player-room__body__store-list.live-player-room__body__hidden {
    z-index: 2
}

.live-player-room__body.live-player-room__body__forbid-list {
    padding: 0 16px;
    position: relative;
    z-index: 2
}

.live-player-room__body__store-list.live-player-room__body__hidden,.live-player__replay__container .live-page-1 .live-player-room__body__store-list.live-player-room__body__hidden,.live-player__replay__container.live-player__bottom-area-safe .live-page-1 .live-player-room__body__store-list.live-player-room__body__hidden {
    position: relative;
    z-index: -2
}

.live-player-room__body__activity-item {
    width: 100%
}

.live-player-room__body__operation {
    min-height: 56px;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: flex-end;
    align-items: flex-end
}

.live-player-room__body__operation__hidden {
    opacity: 0;
    position: fixed
}

.live-player-room__body__msg-list {
    position: relative;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -webkit-align-items: flex-start;
    align-items: flex-start;
    width: calc(100% - 63px);
    transition: opacity .3s
}

.live-player-room__body__msg-list.live-player-room__body__msg-list__cross {
    width: 100%
}

.live-player-room__body__msg-list.live-player-room__body__msg-list__cross .live-player-room__body__activity-item:first-child {
    width: calc(100% - 63px)
}

.live-player-room__body__msg-list.live-player-room__body__msg-list__with-coupon,.live-player-room__body__msg-list.live-player-room__body__msg-list__with-store {
    position: relative;
    bottom: -9px
}

.live-player-room__body__msg-list.live-player-room__body__msg-list__with-push-comment {
    position: relative;
    bottom: -8px;
    padding-bottom: 46px;
    transition: all .25s cubic-bezier(.25,.5,.5,.9)
}

.live-player-room__body__msg-list__with-push-comment .mod-comments {
    margin-bottom: 16px
}

.live-player-room__body__authorize,.live-player-room__body__store-list {
    display: block
}

.live-player-room__body__authorize {
    position: relative;
    bottom: -24px;
    padding: 0
}

.live-player__bottom-area-safe .live-player-room__body__authorize {
    position: relative;
    bottom: -36px
}

.live-page-status__msg,.live-page-tips,.live-page-toast,.live-page-top-toast {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin: -36px auto 0;
    text-align: center;
    font-size: 14px;
    color: #fff;
    z-index: 100;
    padding: 0 8px;
    text-shadow: 0 0 4px rgba(0,0,0,.3)
}

.live-page-tips {
    background-color: rgba(0,0,0,.5);
    width: -webkit-fit-content;
    width: fit-content;
    line-height: 31px;
    border-radius: 4px
}

.live-page-loading {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin: -36px auto 0;
    z-index: 100;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center
}

.live-page-loading .icon_loading {
    margin-right: 4px;
    width: 14px;
    height: 14px
}

.live-page-loading__info {
    margin-top: 12px;
    font-size: 12px;
    color: hsla(0,0%,100%,.9);
    text-align: center;
    line-height: 23.8px
}

.live-page-loading__with-info {
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    align-items: center
}

.live-page-loading__with-info .page__icon__loading {
    width: 28px;
    height: 28px;
    background: none
}

.mode-store-rank {
    width: 100%
}

.live-player__end .mode__store-list {
    position: static
}

.mode__store-list {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 21;
    pointer-events: auto
}

.mode__store-list.component__hidden {
    left: -9999px;
    right: auto;
    pointer-events: none
}

.forbid__user__list,.mode__mic__panel,.mode__setting__more {
    position: relative;
    z-index: 1000
}

.mode__count-time {
    position: fixed;
    left: 0;
    right: 0;
    top: 50%;
    -webkit-transform: translate3d(0,-50%,0);
    transform: translate3d(0,-50%,0);
    margin-top: -76px
}

.mode__count-time-tiny {
    position: fixed;
    top: 50%;
    margin-top: -76px;
    left: 50%;
    margin-left: -40px
}

.mode-push-comment-outside {
    position: absolute;
    bottom: 0;
    z-index: 23;
    display: block;
    width: calc(100% + 87px);
    left: 0;
    right: 0;
    margin-left: -12px
}

.live-player__support-bounding wx-button.button__share {
    right: 7px
}

wx-button.button__share {
    position: absolute;
    top: 50%;
    margin-top: -15.5px;
    right: 93px;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    width: 31px;
    height: 31px;
    line-height: 31px;
    padding: 0;
    min-width: 31px;
    max-width: 31px;
    text-align: center;
    border-radius: 50%;
    min-height: 0;
    box-sizing: border-box;
    background: rgba(0,0,0,.25);
    border: 1px solid hsla(0,0%,100%,.05)
}

wx-button.button__share:after {
    display: none
}

wx-button.button__share:active {
    opacity: .8
}

.button__share__icon {
    display: block;
    position: absolute;
    top: 50%;
    margin-top: -8.5px;
    left: 50%;
    margin-left: -7px;
    width: 14px;
    height: 17px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_share-51f9585cf9.svg) no-repeat 50%
}

.watermark {
    position: absolute;
    bottom: 0;
    margin-bottom: -17px;
    margin-right: 9px;
    right: 0;
    width: 94px;
    height: 24px;
    text-align: right;
    color: hsla(0,0%,100%,.7);
    text-shadow: 0 0 2px rgba(0,0,0,.8);
    background: url(https://res.wx.qq.com/op_res/DEY9KNQYjdxAQGAwqfKpTa5Evz6w24XStkvCLZUfB7vuu9Li8v5KUQ-BWQBj4wti) no-repeat 50%;
    background-size: contain
}

.component__operate-paster {
    position: absolute;
    bottom: 0;
    margin-bottom: -107px;
    right: 0;
    margin-right: 12px
}

.live-pusher .weui-navigation-bar__body .weui-navigation-bar__left {
    width: inherit
}

.live-playing__horizontal-button {
    position: absolute;
    right: 11px;
    bottom: 6px;
    width: 34px;
    height: 34px;
    z-index: 19
}

.live-playing__horizontal-button:after {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    content: " ";
    display: block;
    width: 18px;
    height: 18px;
    background: url(http://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/switch_horizontal-c0645383aa.svg) no-repeat 50%;
    background-size: contain
}

.live-playing__pc__shadow {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 88px;
    background-image: linear-gradient(transparent,rgba(0,0,0,.5));
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}

.live-playing__esc-fullscreen-button,.live-playing__pc__shadow {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center
}

.live-playing__esc-fullscreen-button {
    margin-right: 32px;
    font-size: 22px;
    color: #fff
}

.esc-fullscreen__icon {
    width: 40px;
    height: 40px;
    background: url(https://res.wx.qq.com/a/fed_upload/d5808097-ad35-48ce-a72a-b71a921de76b/esc-fullscreen.svg) no-repeat 50%/contain;
    margin-right: 16px
}

.live-replay-video {
    width: 100%;
    height: 100%;
    background-color: transparent
}

.live-player__replay .live-page-1 {
    padding-bottom: 59px
}

.live-player__replay__container .live-page-1 .live-player-room__body__store-list {
    bottom: -35px
}

.live-player__replay__container.live-player__bottom-area-safe .live-page-1 .live-player-room__body__store-list {
    bottom: -52px
}

.live-page__id__watermark {
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: inherit;
    font-size: 12px;
    color: hsla(0,0%,100%,.2);
    text-shadow: 0 1px 2px rgba(0,0,0,.5)
}

.live-page__id__watermark__animation {
    -webkit-animation: move 80s linear infinite alternate;
    animation: move 80s linear infinite alternate
}

@-webkit-keyframes move {
    0% {
        -webkit-transform: translate3d(10%,10%,0);
        transform: translate3d(10%,10%,0)
    }

    50% {
        -webkit-transform: translate3d(50%,90%,0);
        transform: translate3d(50%,90%,0)
    }

    to {
        -webkit-transform: translate3d(85%,10%,0);
        transform: translate3d(85%,10%,0)
    }
}

@keyframes move {
    0% {
        -webkit-transform: translate3d(10%,10%,0);
        transform: translate3d(10%,10%,0)
    }

    50% {
        -webkit-transform: translate3d(50%,90%,0);
        transform: translate3d(50%,90%,0)
    }

    to {
        -webkit-transform: translate3d(85%,10%,0);
        transform: translate3d(85%,10%,0)
    }
}

.live-player-unusual {
    position: relative;
    height: 100%;
    text-align: center
}

.live-player-unusual__container {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    box-sizing: border-box;
    padding: 0 16px;
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    margin-top: -117px
}

.live-player-unusual__info {
    font-size: 22px;
    color: hsla(0,0%,100%,.9);
    margin-top: 32px
}

.mode__follow-teach {
    position: relative;
    z-index: 101
}

.live-player-room__body__mic-tag__container {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}

.mode__mic-tag,.mode__mic-tag-movable-area {
    position: absolute
}

.mode__mic-tag {
    z-index: 999
}

.mode__mic-tag_inviting {
    position: absolute;
    z-index: 1002
}

.mode__notice-toptips {
    position: absolute;
    left: 8px;
    right: 8px;
    z-index: 10000
}

.mode__advance {
    width: 100%
}

@media screen and (max-width:330px) {
    wx-button.button__share {
        right: 87px
    }
}

@media screen and (max-height:380px) {
    .live-page-1 {
        padding-bottom: 8px
    }

    .mod-comments {
        margin-top: 8px
    }
}

@media screen and (max-width:394px) {
    wx-button.button__share {
        right: 96px
    }
}

@media screen and (min-width:411px) and (max-width:413px) {
    wx-button.button__share {
        right: 110px
    }
}

@media screen and (min-width:700px) {
    wx-button.button__share {
        right: 120px
    }
}

@media screen and (min-width:768px) {
    .mod-comments__allenLive {
        margin-top: 12px
    }
}

.live_player_transform_duration {
    transition-duration: 2s
}

@supports (bottom:constant(safe-area-inset-bottom)) or (bottom:env(safe-area-inset-bottom)) {
    .live-page-1.live-player__replay {
        padding-bottom: calc(env(safe-area-inset-bottom) + 59px)
    }
}

.live-player__push-stream.live-player__playing {
    background: none;
    background-color: #191919
}

.live-bottom-page__playing__horizontal-button__clean:after,.live-bottom-page__playing__horizontal-button__show:after {
    content: " ";
    display: block;
    height: 48px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-image: linear-gradient(transparent,rgba(0,0,0,.5))
}

.live-bottom-page__playing__horizontal-button__clean:before,.live-bottom-page__playing__horizontal-button__show:before {
    display: none
}

.live-bottom-page__playing__horizontal-button__show:after {
    -webkit-animation: verticalShow .3s 1 forwards;
    animation: verticalShow .3s 1 forwards
}

.live-bottom-page__playing__horizontal-button__clean:after {
    -webkit-animation: verticalClean .3s 1 forwards;
    animation: verticalClean .3s 1 forwards
}

.live-playing__horizontal-button__show {
    -webkit-animation: verticalShow .3s 1 forwards;
    animation: verticalShow .3s 1 forwards;
    pointer-events: auto
}

.live-playing__horizontal-button__clean {
    -webkit-animation: verticalClean .3s 1 forwards;
    animation: verticalClean .3s 1 forwards;
    pointer-events: none
}

@-webkit-keyframes verticalShow {
    0% {
        opacity: 0;
        display: none
    }

    to {
        opacity: 1
    }
}

@keyframes verticalShow {
    0% {
        opacity: 0;
        display: none
    }

    to {
        opacity: 1
    }
}

@-webkit-keyframes verticalClean {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        display: none
    }
}

@keyframes verticalClean {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        display: none
    }
}

.live-player__push-stream__horizontal .live-player-room__page {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    height: 50%;
    padding-left: 44px;
    padding-right: 44px;
    box-sizing: border-box
}

.live-player__push-stream__horizontal .mode__store-list {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    width: auto
}

.live-player__push-stream__horizontal .mode-push-comment-outside {
    width: 100vw;
    bottom: -27px;
    margin-left: -44px
}

.live-player-room__clean .live-player-room__body,.live-player-room__clean .mode__navigation__inner__button,.live-player-room__clean .mode__navigation__inner__with-home,.page-live-replay__clean-screen .live-page-0:after,.page-live-replay__clean-screen .live-page-0:before,.page-live-replay__clean-screen .mode_video-control {
    -webkit-animation: horizontalClean .3s 1 forwards;
    animation: horizontalClean .3s 1 forwards
}

@-webkit-keyframes horizontalClean {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 0
    }
}

@keyframes horizontalClean {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 1
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        opacity: 0
    }
}

.live-player-room__show .live-player-room__body,.live-player-room__show .mode__navigation__inner__button,.live-player-room__show .mode__navigation__inner__with-home,.page-live-replay__cancel-clean-screen .live-page-0:after,.page-live-replay__cancel-clean-screen .live-page-0:before,.page-live-replay__cancel-clean-screen .mode_video-control {
    -webkit-animation: horizontalShow .3s 1 forwards;
    animation: horizontalShow .3s 1 forwards
}

@-webkit-keyframes horizontalShow {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes horizontalShow {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.live-player__push-stream__horizontal .live-player-room__body__activity-item {
    width: 50%
}

.live-player__push-stream__horizontal .live-player-room__body__msg-list__with-push-comment .live-player-room__body__activity-item:first-child {
    bottom: 0
}

.live-player__push-stream__horizontal .live-player-room__body__msg-list {
    position: relative;
    bottom: -8px;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-flex-direction: row;
    flex-direction: row;
    -webkit-align-items: flex-end;
    align-items: flex-end;
    width: 100%
}

.live-player__push-stream__horizontal .live-player-room__body__msg-list .live-player-room__body__activity-item__with-push {
    position: relative;
    bottom: -1px;
    z-index: 22
}

.live-player__push-stream__horizontal .live-player-room__body__activity-item__with-coupon {
    bottom: -15px
}

.live-player__push-stream__horizontal .mode__navigation__with__white-icon .mode__lottery {
    margin-left: 0
}

.live-player__push-stream__horizontal .live-player-room__body.live-player-room__body__store-list {
    padding: 0;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    height: 100%
}

.live-player__push-stream__horizontal .live-player-room__body.live-player-room__body__store-list[hidden] {
    display: none
}

.live-player__push-stream__horizontal .mod__activity-store-card {
    right: -7px;
    width: calc(100% - 50px)
}

.live-player__push-stream__horizontal .mode__navigation__inner__with-home .mode__lottery {
    margin-bottom: -78px
}

.live-player__push-stream__horizontal .mode__navigation__inner__with-home.mode__navigation__inner__with-help-list .mode__lottery {
    margin-bottom: -123px
}

.live-player__push-stream__horizontal .mode__navigation__inner__with-home .mode__help-list-entr {
    margin-bottom: -44px
}

.live-player__push-stream__horizontal .live-player-end__body .mode__help-list-entr,.live-player__push-stream__horizontal .live-player-end__body .mode__lottery {
    margin-left: 40px
}

.weui-navigation-bar__horizontal .watermark {
    margin-right: 18px
}

.weui-navigation-bar__horizontal .button__share {
    right: 108px
}

.live-player__push-stream__horizontal .live-player-room__body__activity-item__with-coupon {
    position: relative;
    bottom: 0
}

.live-player__push-stream__horizontal .live-player-room__body__msg-list.live-player-room__body__msg-list__with-push-comment {
    bottom: 17px;
    padding-bottom: 19px
}

.live-player__with-safe-bottom.live-player__push-stream__horizontal .live-player-room__body__msg-list.live-player-room__body__msg-list__with-push-comment {
    bottom: -17px
}

.mode__follow-teach__horizontal .half-screen-dialog__follow-teach__content {
    margin: 24px 0 32px
}

.live-page__id__watermark {
    margin-top: 0;
    left: 40px
}

.page-live-replay__horizontal .live-page__id__watermark {
    left: 40px
}

.page-live-player__horizontal,.page-live-replay__horizontal {
    height: 100%
}

.live-player__push-stream__horizontal.live-player__record__container .live-player-room__body__msg-list {
    position: relative;
    bottom: -50px
}
