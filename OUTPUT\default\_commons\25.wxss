page {
    --bgColor-lottery: #ffefd5
}

.lottery-oper__dialog__mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background-color: rgba(0,0,0,.5)
}

.lottery-oper__dialog {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    transition: all .2s
}

.lottery-oper__dialog-hide {
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.lottery-oper__dialog__container {
    position: fixed;
    left: 50%;
    top: 50%;
    color: var(--color-lottery);
    -webkit-transform: translate3d(-50%,-50%,0);
    transform: translate3d(-50%,-50%,0);
    background-image: linear-gradient(to bottom right,#fff8eb,#ffecd1);
    background-size: 100% auto;
    width: 85%;
    max-width: 414px;
    min-height: 331px;
    max-height: 496px;
    text-align: center;
    border-radius: 12px;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    overflow: hidden;
    transition: all .2s
}

.lottery-oper__dialog__noreward .lottery-oper__dialog__container {
    background: var(--bgColor-lottery) url(https://res.wx.qq.com/a/fed_upload/094c5d45-6b0a-4556-8b97-f5bb2d3eee8b/lotteryNoreward.jpg) no-repeat top;
    background-size: 100% auto
}

.lottery-oper__dialog__team .lottery-oper__dialog__container {
    min-height: 384px;
    height: auto
}

.lottery-oper__dialog__team__share .lottery-oper__dialog__container {
    height: 442px
}

.lottery-oper__dialog__rewardlist .lottery-oper__dialog__container {
    background: none;
    background: var(--bgColor-large-lottery)!important
}

.lottery-oper__dialog__large .lottery-oper__dialog__container {
    height: 496px
}

.lottery-oper__dialog__bd__for-hide-scroll {
    height: inherit;
    overflow-x: hidden;
    overflow-y: auto
}

.lottery-oper__dialog__bd__for-hide-scroll__inner {
    height: inherit
}

.lottery-oper__dialog__close {
    position: absolute;
    width: 44px;
    height: 44px;
    top: 9px;
    left: 7px
}

.lottery-oper__dialog__close:active,.lottery-oper__dialog__close:active:after {
    opacity: .7
}

.lottery-oper__dialog__close:after {
    position: absolute;
    top: 50%;
    left: 50%;
    content: " ";
    display: block;
    width: 16px;
    height: 16px;
    margin-top: -12px;
    margin-left: -12px;
    background: url(https://res.wx.qq.com/a/fed_upload/eb5e6456-cb0d-4a81-ae8c-0731b6393d7b/lottery_dialog_close.svg) no-repeat 50%;
    background-size: contain
}

.lottery-oper__dialog__head {
    min-height: 48px
}

.lottery-oper__dialog__head__title {
    line-height: 56px;
    font-size: 17px;
    color: var(--color-lottery-desc)
}

.lottery-oper__luck-num {
    position: relative;
    display: inline-block;
    padding-left: 8px;
    margin-left: 6px;
    white-space: nowrap
}

.lottery-oper__luck-num:before {
    content: " ";
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: var(--color-lottery);
    position: absolute;
    top: 50%;
    margin-top: -1px;
    left: 0;
    margin-left: 1px
}

.lottery-oper__dialog__title {
    font-size: 17px;
    color: var(--color-lottery);
    margin-top: 12px;
    padding: 0 16px;
    word-break: break-all
}

.lottery-oper__dialog__remark {
    padding: 0 24px;
    font-size: 14px;
    margin-bottom: 30px;
    color: var(--color-lottery-desc);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.lottery-oper__dialog__head__extend {
    position: absolute;
    top: 0;
    right: 0;
    padding-top: 16px;
    padding-right: 15px;
    color: var(--color-lottery-desc);
    font-size: 14px;
    text-align: right;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center
}

.lottery-oper__dialog__head__extend:after {
    content: " ";
    display: block;
    width: 10px;
    height: 20px;
    margin-left: 4px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M7.588 12.43l-1.061 1.06L.748 7.713a.996.996 0 010-1.413L6.527.52l1.06 1.06-5.424 5.425 5.425 5.425z' id='a'/%3E%3C/defs%3E%3Cuse fill-opacity='.5' fill='%237D4A09' transform='rotate(-180 5.02 9.505)' xlink:href='%23a' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.lottery-oper__dialog__head__extend:active {
    opacity: .7
}

.lottery-oper__dialog__info {
    font-size: 17px;
    color: #fe735b;
    margin-top: 19px;
    font-weight: 450
}

.lottery-oper__dialog__time {
    font-size: 48px;
    line-height: 66px;
    font-weight: 450;
    color: #ff735c
}

.lottery-oper__dialog__time__waiting {
    font-size: 28px;
    font-weight: 400;
    margin-top: 8px
}

.lottery-oper__dialog__inner {
    -webkit-flex: 1;
    flex: 1;
    max-height: calc(100% - 48px);
    overflow-y: auto
}

.lottery-oper__dialog__large .lottery-oper__dialog__title {
    margin-top: 4px
}

.lottery-oper__unstart {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.lottery-oper__result__user__noreward .lottery-oper__result-noreward {
    margin-top: 23px
}

.lottery-oper__result-noreward.move-top {
    position: relative;
    top: -10px
}

.lottery-oper-result__horizontal .lottery-oper__result-noreward.move-top {
    top: -20px
}

.lottery-oper__collect {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between
}

.lottery-oper__link {
    margin-top: 16px;
    text-align: center;
    color: var(--color-lottery-desc)
}

.lottery-oper__unstart__foot {
    margin-bottom: 24px
}

.lottery-oper__unstart__foot__desc {
    font-size: 14px;
    margin: 16px 0 0;
    color: var(--color-lottery-desc)
}

.lottery-oper__btn,wx-button.lottery-oper__btn {
    width: 157px;
    line-height: 40px;
    padding: 0;
    text-align: center;
    border-radius: 4px;
    margin: 0 auto;
    font-weight: 450;
    text-indent: 1px
}

.lottery-oper__btn:active {
    opacity: .7
}

.lottery-oper__btn-primary {
    background-color: var(--color-theme);
    color: #fff;
    font-weight: 450
}

.lottery-oper__btn-default {
    color: var(--color-theme);
    border: 1px solid var(--color-light);
    font-weight: 450
}

.lottery-oper__btn-info {
    color: var(--color-light);
    background-color: var(--color-theme);
    border: none
}

.lottery-oper__btn-primary.lottery-oper__btn-disabled {
    background-color: var(--color-theme-disabled);
    color: var(--color-light-disabled)
}

.lottery-oper__btn-primary.lottery-oper__btn-disabled:active {
    opacity: 1
}

.lottery-oper__result-noreward {
    font-size: 17px;
    color: var(--color-lottery);
    text-align: center;
    text-shadow: 0 0 4px rgba(0,0,0,.3)
}

.lottery-oper__result__user__avatar__container {
    position: relative;
    display: block;
    width: 64px;
    height: 64px;
    margin: 32px auto 0;
    border-radius: 50%;
    box-sizing: border-box
}

.lottery-oper__result__user__avatar__container:before {
    content: " ";
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -49.5px;
    margin-left: -68.5px;
    width: 137px;
    height: 99px;
    background: url(https://res.wx.qq.com/a/fed_upload/7384ac67-2d22-448a-bc37-dba77dc88191/iconReward.svg) no-repeat 50%;
    background-size: cover
}

.lottery-oper__result__user__noreward {
    height: 184px;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-flex-direction: column;
    flex-direction: column
}

.lottery-oper__result__user__noreward .lottery-oper__result__user__info {
    margin-top: 0;
    color: var(--color-lottery)
}

.lottery-oper__result__user__avatar {
    position: relative;
    z-index: 2;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    background: #dbdbdb url(https://mmbiz.qpic.cn/mmbiz/a5icZrUmbV8p5jb6RZ8aYfjfS2AVle8URwBt8QIu6XbGewB9wiaWYWkPwq4R7pfdsFibuLkic16UcxDSNYtB8HnC1Q/0) no-repeat 50%;
    background-size: cover
}

.lottery-oper__result__user__avatar__container-team {
    position: relative
}

.lottery-oper__result__user__avatar__container-team:before {
    width: 185px;
    height: 107px;
    margin-top: -53.5px;
    margin-left: -92.5px;
    background: url(https://res.wx.qq.com/a/fed_upload/ea50d068-d302-45a2-8407-858f2246b269/lotteryTeamReward.svg) no-repeat 50%/contain
}

.lottery-oper__result__user__avatar__container-team:after {
    content: " ";
    display: block;
    width: 62px;
    height: 62px;
    position: absolute;
    top: -2px;
    left: 50%;
    margin-left: -33px;
    border: 2px solid var(--bgColor-lottery);
    border-radius: 50%
}

.lottery-oper-result__horizontal .lottery-oper__result__user__avatar__container-team {
    margin-top: 28px
}

.lottery-oper__result__other-user__avatar {
    position: absolute;
    bottom: 0;
    left: 0;
    margin-left: -30px;
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #dbdbdb url(https://mmbiz.qpic.cn/mmbiz/a5icZrUmbV8p5jb6RZ8aYfjfS2AVle8URwBt8QIu6XbGewB9wiaWYWkPwq4R7pfdsFibuLkic16UcxDSNYtB8HnC1Q/0) no-repeat 50%;
    background-size: cover
}

.lottery-oper__result__other-user__avatar:last-child {
    left: auto;
    right: 0;
    margin-right: -30px
}

.lottery-oper__result__user__info {
    font-size: 17px;
    margin-top: 23px;
    color: var(--color-theme);
    font-weight: 450
}

.lottery-oper__result__user__unpartin__info {
    color: var(--color-lottery-desc);
    margin: 80px 0 90px;
    font-size: 14px
}

.lottery-oper__result__user__word {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    width: 204px;
    height: 56px;
    margin: 16px auto 0;
    border-radius: 8px;
    background-color: var(--color-theme);
    background-size: cover;
    color: var(--color-lottery)
}

.lottery-oper__result__user__word__page {
    -webkit-flex: 1;
    flex: 1;
    text-align: center
}

.lottery-oper__result__user__word__title {
    font-size: 12px;
    color: hsla(0,0%,100%,.5)
}

.lottery-oper__result__user__word__main {
    font-size: 17px;
    color: hsla(0,0%,100%,.9);
    line-height: 23px;
    margin-top: 2px
}

.lottery-oper__result__user__word__foot {
    position: relative;
    width: 51px;
    line-height: 56px;
    font-size: 14px;
    color: hsla(0,0%,100%,.7)
}

.lottery-oper__result__user__word__foot:active {
    opacity: .8
}

.lottery-oper__result__user__word__foot:before {
    position: absolute;
    top: 50%;
    margin-top: -19px;
    content: " ";
    display: block;
    height: 38px;
    width: 1px;
    background-color: rgba(0,0,0,.08)
}

.lottery-oper__result__user__word__info {
    margin-top: 16px;
    color: var(--color-lottery-desc);
    font-size: 14px
}

.lottery-oper__result__user__oper {
    padding: 0 40px
}

.lottery-oper__result__user__oper-button {
    margin-bottom: 56px
}

.lottery-oper__result__user__addr {
    margin-top: 16px
}

.lottery-oper__result__user__addr .lottery-oper__btn-default {
    margin-top: 15px
}

.lottery-oper__result__user__addr__info {
    margin-top: 8px;
    font-size: 14px;
    color: var(--color-lottery-desc)
}

.lottery-oper__result__user__addr .lottery-oper__btn {
    margin-top: 32px
}

.lottery-oper__result__user__addr__info__item {
    display: block
}

.lottery-oper__result__user__oper-cell {
    margin-top: 24px;
    margin-bottom: 56px
}

.lottery-oper__result__user__oper-cell .lottery-oper__result__user__addr {
    margin-top: 0
}

.lottery-oper__operation__cell {
    padding: 16px 0;
    position: relative;
    text-align: left
}

.lottery-oper__result__user__oper-cell .lottery-oper__result__user__addr__info {
    margin-top: 12px
}

.lottery-oper__operation__cell:after,.lottery-oper__operation__cell:before {
    position: absolute;
    content: " ";
    display: block;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--color-lottery-decora);
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5)
}

.lottery-oper__operation__cell:before {
    top: 0
}

.lottery-oper__operation__cell:after {
    bottom: 0
}

.lottery-oper__operation__cell__operation {
    padding-right: 10px
}

.lottery-oper__operation__cell__operation,.lottery-oper__operation__cell__operation:after {
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.lottery-oper__operation__cell__operation:after {
    content: " ";
    display: block;
    width: 6px;
    height: 12px;
    background: url(https://res.wx.qq.com/a/fed_upload/297ef185-5200-49cc-8a07-a2dabe7f68ac/lotteryAddrCellMore.png) no-repeat 50%/contain
}

.lottery-oper__rewards {
    margin-top: 48px;
    text-align: left;
    font-size: 14px;
    padding: 0 24px
}

.lottery-oper__rewards__head {
    position: relative;
    margin: 1px auto 25px;
    width: 2px;
    height: 2px;
    border-radius: 50%;
    background-color: var(--bgColor-lottery)
}

.lottery-oper__rewards__head:before {
    content: " ";
    display: block;
    position: absolute;
    top: 0;
    left: -75px;
    width: 55px;
    height: 1px;
    background-color: var(--bgColor-lottery)
}

.lottery-oper__rewards__title {
    margin-bottom: 8px;
    color: var(--color-lottery-desc);
    word-break: break-all;
    vertical-align: top
}

.lottery-oper__rewards__title .lottery-oper__luck-num:before {
    background-color: var(--color-lottery)
}

.lottery-oper__rewards__item {
    position: relative;
    padding: 8px 0;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: stretch;
    align-items: stretch;
    color: rgba(0,0,0,.5)
}

.lottery-oper__rewards__item__avatar {
    display: inline-block;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 16px;
    box-sizing: border-box;
    background: #dbdbdb url(https://mmbiz.qpic.cn/mmbiz/a5icZrUmbV8p5jb6RZ8aYfjfS2AVle8URwBt8QIu6XbGewB9wiaWYWkPwq4R7pfdsFibuLkic16UcxDSNYtB8HnC1Q/0) no-repeat 50%;
    background-size: cover;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.lottery-oper__rewards__item__info {
    line-height: 24px
}

.lottery-oper__my-result-list {
    margin-top: 2px;
    padding: 0 16px;
    max-height: calc(100% - 60px)
}

.lottery-oper__my-result-item {
    position: relative;
    padding: 16px 32px 16px 0;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    font-size: 14px
}

.lottery-oper__my-result-item:after {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 32px;
    content: " ";
    display: block;
    background-color: var(--bgColor-lottery);
    height: 1px;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0
}

.lottery-oper__my-result-item__foot {
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    display: inline-block;
    padding: 0 12px;
    line-height: 28px;
    border-radius: 14px;
    background-color: var(--color-theme);
    text-align: center;
    color: var(--color-light)
}

.lottery-oper__my-result-item__foot:active {
    opacity: .7
}

.lottery-oper__my-result-item__foot_disabled {
    color: var(--color-light-disabled);
    background-color: var(--color-theme-disabled)
}

.lottery-oper__my-result-item__page {
    max-width: 188px;
    text-align: left
}

.lottery-oper__my-result__title {
    margin-bottom: 5px;
    font-size: 17px;
    width: inherit;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: var(--color-lottery)
}

.lottery-oper__my-result__info {
    color: var(--color-lottery-desc)
}

.lottery-oper__luck-result-list {
    padding: 0 24px 0 16px
}

.lottery-oper__luck-list__title {
    position: relative;
    font-size: 17px;
    font-weight: 450;
    color: var(--color-theme);
    padding-top: 6px;
    margin-bottom: 13px
}

.lottery-oper__luck-list__title:before {
    display: block;
    content: " ";
    position: absolute;
    top: 0;
    left: 50%;
    width: 211px;
    height: 406px;
    margin-left: -105.5px;
    background: url(https://res.wx.qq.com/a/fed_upload/f76c832a-12ef-489d-b825-3d441a31a9ef/reward_avatar_decorate.svg) no-repeat top/contain
}

.lottery-oper__luck-list__info {
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--color-lottery-desc)
}

.lottery-oper__luck-list {
    padding: 0 24px 0 16px
}

.lottery-oper__luck-item {
    position: relative;
    padding: 8px 0 8px 8px;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    text-align: left
}

.lottery-oper__result__user__unpartin__extend {
    position: absolute;
    bottom: 32px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    font-size: 14px;
    color: var(--color-lottery-desc)
}

.lottery-oper__luck__avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    margin-right: 16px;
    border: 1px solid hsla(0,0%,100%,.3);
    box-sizing: border-box
}

.lottery-oper__luck__avatar,.lottery-oper__luck__avatar__image {
    background: #dbdbdb url(https://mmbiz.qpic.cn/mmbiz/a5icZrUmbV8p5jb6RZ8aYfjfS2AVle8URwBt8QIu6XbGewB9wiaWYWkPwq4R7pfdsFibuLkic16UcxDSNYtB8HnC1Q/0) no-repeat 50%;
    background-size: cover
}

.lottery-oper__luck__avatar__image {
    display: block;
    width: inherit;
    height: inherit;
    border-radius: inherit
}

.lottery-oper__luck__info {
    -webkit-flex: 1;
    flex: 1;
    color: #fff;
    font-size: 17px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.lottery-oper__winner__extend {
    padding-left: 8px;
    text-align: right;
    -webkit-flex-shrink: 0;
    flex-shrink: 0;
    color: var(--color-lottery-desc);
    font-size: 14px
}

.lottery-oper__result__extend {
    margin: 56px 0 40px;
    font-size: 14px;
    text-align: center;
    color: var(--color-lottery-desc)
}

.lottery-oper__result__extend__info:first-child {
    margin-top: 0
}

.lottery-oper__result__extend__info {
    margin-top: 16px
}

.lottery-oper__unstart .lottery-oper__result__extend__info {
    margin-top: 24px
}

.lottery-oper__result__link {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    font-size: 14px;
    font-weight: 400
}

.lottery-oper__result__link:after {
    content: " ";
    display: block;
    width: 10px;
    height: 20px;
    margin-left: 4px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M7.588 12.43l-1.061 1.06L.748 7.713a.996.996 0 010-1.413L6.527.52l1.06 1.06-5.424 5.425 5.425 5.425z' id='a'/%3E%3C/defs%3E%3Cuse fill-opacity='.5' fill='%237D4A09' transform='rotate(-180 5.02 9.505)' xlink:href='%23a' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.lottery-oper__error {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    -webkit-align-items: center;
    align-items: center
}

.lottery-oper__error__main {
    font-size: 17px;
    color: var(--color-lottery);
    line-height: 40px;
    margin-top: 55px;
    margin-bottom: 83px
}

.lottery-oper__error__main__info {
    position: relative;
    padding: 0 35px 0 54px;
    line-height: 20px;
    font-size: 14px;
    display: inline-block;
    text-align: left;
    color: var(--color-lottery-desc)
}

.lottery-oper__error__main__info:before {
    position: absolute;
    top: 2px;
    left: 31px;
    content: " ";
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='28' height='28' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14 .667c7.364 0 13.333 5.97 13.333 13.333 0 7.364-5.97 13.333-13.333 13.333C6.636 27.333.667 21.363.667 14 .667 6.636 6.637.667 14 .667zm0 1.6C7.52 2.267 2.267 7.52 2.267 14S7.52 25.733 14 25.733 25.733 20.48 25.733 14 20.48 2.267 14 2.267zm0 16.27c.635 0 1.113.48 1.113 1.114 0 .625-.478 1.113-1.113 1.113a1.1 1.1 0 01-1.113-1.113c0-.635.488-1.113 1.113-1.113zm.879-11.962l-.117 9.414h-1.524l-.117-9.414h1.758z' fill='%237D4A09' fill-rule='evenodd' fill-opacity='.5'/%3E%3C/svg%3E") no-repeat 50%;
    background-size: contain;
    margin-right: 6px;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.lottery-oper__error__desc {
    font-size: 14px;
    color: var(--color-lottery-desc);
    line-height: normal
}

.lottery-oper__error__info {
    color: var(--color-lottery-desc);
    margin-bottom: 32px;
    font-size: 14px
}

.lottery-oper__result__footer {
    position: absolute;
    bottom: 63px
}

.lottery-oper__team__extend {
    padding-bottom: 40px
}

.lottery-oper__team__extend .lottery-oper__cut-off {
    margin-top: 9px
}

.lottery-oper__dialog__horizontal .lottery-oper__team__extend {
    padding-bottom: 20px
}

@media screen and (max-width:340px) {
    .lottery-oper__dialog__container {
        width: 280px
    }

    .lottery-oper__dialog__large .lottery-oper__dialog__container {
        height: 400px
    }
}

.lottery-oper__dialog__horizontal .lottery-oper__dialog__container,.lottery-oper__dialog__horizontal.lottery-oper__dialog__large .lottery-oper__dialog__container,.lottery-oper__dialog__horizontal .lottery-oper__dialog__middle .lottery-oper__dialog__container {
    height: calc(100% - 26px);
    max-height: 360px;
    max-width: 320px;
    min-height: 0
}

.lottery-oper__dialog__horizontal .lottery-oper__dialog__container {
    height: calc(100% - 40px);
    max-height: 331px;
    min-height: 0
}

.lottery-oper__dialog__horizontal .lottery-oper__dialog__bd__for-hide-scroll {
    height: 100%
}

.lottery-oper__dialog__horizontal .lottery-oper__result {
    position: relative;
    height: calc(100% - 48px)
}

.lottery-oper__dialog__horizontal .lottery-oper__result-swiper {
    height: 100%
}

.lottery-oper__dialog__horizontal .lottery-oper__result-swiper__dots__container {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 0 0 16px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0
}

.lottery-oper__dialog__horizontal .lottery-oper__result-swiper__dot {
    width: 4px;
    height: 4px;
    background-color: var(--color-lottery-dec);
    margin: 0 4px;
    border-radius: 50%
}

.lottery-oper__dialog__horizontal .lottery-oper__result-swiper__dot.current {
    background-color: var(--color-lottery-desc)
}

.lottery-oper__dialog__horizontal .lottery-oper__dialog__head__extend {
    padding-right: 16px
}

.lottery-oper__dialog__horizontal.lottery-oper__dialog__unstart .lottery-oper__dialog__title {
    margin-top: 20px
}

.lottery-oper__dialog__horizontal.lottery-oper__dialog__unstart .lottery-oper__dialog__remark {
    margin-bottom: 28px
}

.lottery-oper__dialog__horizontal.lottery-oper__dialog__error .lottery-oper__dialog__title {
    margin-top: 20px
}

.lottery-oper__dialog__horizontal .lottery-oper__my-result-list {
    margin-top: 0
}

.lottery-oper__dialog__horizontal .lottery-oper__my-result-item:first-child {
    padding-top: 0
}

.lottery-oper__dialog__desc {
    margin-top: 8px;
    color: var(--color-lottery-desc);
    font-size: 14px
}

.lottery-oper__unstart__head__extend {
    margin-top: 8px;
    font-size: 14px;
    color: var(--color-theme);
    font-weight: 450
}

.lottery-oper__team__page {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 48px 0 56px
}

.lottery-oper__team__item {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    align-items: center;
    padding: 0 20px;
    width: 33.33%;
    box-sizing: border-box;
    color: var(--color-lottery)
}

.lottery-oper__team__avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-sizing: border-box;
    -webkit-flex-shrink: 0;
    flex-shrink: 0
}

.lottery-oper__team__avatar__none {
    background: url(https://res.wx.qq.com/a/fed_upload/198422a7-e66f-4765-8e37-a22c66d2592c/avatar_placeholder.svg) no-repeat 50%/contain
}

.lottery-oper__team__item__nickname {
    margin-top: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
    font-size: 14px
}

.lottery-oper__status {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    color: var(--color-lottery);
    font-size: 14px;
    margin-bottom: 10px
}

.lottery-oper__status__success {
    margin-top: 16px
}

.lottery-oper__status__info:before,.lottery-oper__status__success:before {
    content: " ";
    display: block;
    width: 20px;
    height: 20px;
    margin-right: 4px
}

.lottery-oper__status__info:before {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zM11.4 10h1.2v7h-1.2v-7zm.6-1a1 1 0 110-2 1 1 0 010 2z' fill-rule='evenodd' fill='%237D4A09' fill-opacity='.5'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.lottery-oper__status__success:before {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-1.172-6.242l5.809-5.808.848.849-5.95 5.95a1 1 0 01-1.414 0L7 12.426l.849-.849 2.98 2.98z' fill-rule='evenodd' fill='%237D4A09' fill-opacity='.5'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.lottery-oper__access-info {
    font-size: 14px;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    color: var(--color-lottery)
}

.lottery-oper__access-info:after {
    content: " ";
    display: block;
    width: 20px;
    height: 20px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='24' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M7.588 12.43l-1.061 1.06L.748 7.713a.996.996 0 010-1.413L6.527.52l1.06 1.06-5.424 5.425 5.425 5.425z' id='a'/%3E%3C/defs%3E%3Cuse fill-opacity='.5' transform='rotate(-180 5.02 9.505)' xlink:href='%23a' fill='%237D4A09' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.lottery-oper-result__loading {
    margin-top: 163px;
    margin-bottom: 211px
}

.lottery-oper__cut-off {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    font-size: 14px;
    color: var(--color-lottery-desc);
    text-align: center
}

.lottery-oper__cut-off:after,.lottery-oper__cut-off:before {
    content: " ";
    display: block;
    height: 1px;
    width: 63px;
    background-color: var(--color-lottery-desc);
    margin: 0 12px
}

.lottery-oper__extend-operate {
    margin-top: 24px
}

.lottery-oper__extend-operate,wx-button.lottery-oper__extend-operate-item {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center
}

wx-button.lottery-oper__extend-operate-item {
    padding: 0;
    background: none;
    font-size: 10px;
    color: var(--color-lottery);
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    align-items: center;
    text-align: center;
    margin: 0 38px
}

.icon__circle-friend__light,.icon__share__light {
    width: 24px;
    height: 24px;
    margin-bottom: 4px
}

.icon__share__light {
    background: url(https://res.wx.qq.com/a/fed_upload/86cf1bef-f487-4b54-b7d8-423700900bea/lotteryShare.svg) no-repeat 50%/contain
}

.icon__circle-friend__light {
    background: url(https://res.wx.qq.com/a/fed_upload/86cf1bef-f487-4b54-b7d8-423700900bea/lotteryCircle.svg) no-repeat 50%/contain
}

.lottery-oper__rewards-team .lottery-oper__rewards__item__avatar__container {
    position: relative;
    display: inline-block;
    width: 60px
}

.lottery-oper-result__err {
    height: inherit;
    -webkit-flex-direction: column;
    flex-direction: column
}

.lottery-oper-result__err,.lottery-oper-result__err-info {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center
}

.lottery-oper-result__err-info {
    font-size: 14px;
    color: var(--color-lottery-desc)
}

.icon__info {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-1.2a8.8 8.8 0 100-17.6 8.8 8.8 0 000 17.6zm-.66-14.369h1.32l-.089 7.06H11.43l-.088-7.06zM12 17.073a.825.825 0 01-.835-.835.82.82 0 01.835-.835c.476 0 .835.36.835.835a.82.82 0 01-.835.835z' fill-rule='evenodd' fill='%237D4A09' fill-opacity='.5'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.icon__info,.icon__loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px
}

.icon__loading {
    background: url(https://res.wx.qq.com/a/fed_upload/df5f1be1-6615-4998-98d4-5a79fe420084/loading_white.svg) no-repeat 50%/contain;
    -webkit-animation: weuiLoading 1s steps(12) infinite;
    animation: weuiLoading 1s steps(12) infinite
}

.lottery-oper-result__err-button {
    width: 80px;
    line-height: 28px;
    background: #fff;
    border-radius: 14px;
    margin-top: 16px;
    font-size: 14px;
    color: var(--color-theme)
}

.lottery-oper__dialog__horizontal .lottery-oper__team__page {
    padding: 24px 0 15px;
    width: 205px;
    margin: 0 auto
}

.lottery-oper__dialog__horizontal .lottery-oper__team__body .lottery-oper__team__avatar {
    width: 40px;
    height: 40px
}

.lottery-oper__dialog__horizontal .lottery-oper__team__item {
    padding: 0
}

@media screen and (max-height:370px) {
    .lottery-oper__dialog__horizontal.lottery-oper__dialog__unstart .lottery-oper__dialog__title,.lottery-oper__dialog__title {
        margin-top: 0
    }

    .lottery-oper__dialog__info {
        margin-top: 9px
    }

    .lottery-oper__dialog__time {
        font-size: 36px
    }

    .lottery-oper__dialog__horizontal.lottery-oper__dialog__unstart .lottery-oper__dialog__remark,.lottery-oper__dialog__remark {
        margin-bottom: 5px
    }

    .lottery-oper__unstart__foot__desc {
        margin-top: 5px
    }

    .lottery-oper__dialog__time {
        line-height: 70px
    }

    .lottery-oper__unstart__foot {
        margin-bottom: 0
    }

    .lottery-oper__dialog__time__waiting {
        margin-top: 0
    }
}

@media (max-height:320px) {
    .lottery-oper__dialog__horizontal .lottery-oper__dialog__title {
        font-size: 15px
    }

    .lottery-oper__dialog__horizontal .lottery-oper__dialog__desc,.lottery-oper__dialog__horizontal .lottery-oper__team__item__nickname,.lottery-oper__dialog__horizontal .lottery-oper__unstart__head__extend {
        font-size: 12px
    }
}