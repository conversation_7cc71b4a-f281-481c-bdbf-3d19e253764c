<view bindtouchend="onEnd" class="pathway" id="pathway">
    <view class="tips">
        <text style="color: #FFFFFF;" wx:if="{{isOk}}">验证通过</text>
        <text wx:else>拖动滑块验证</text>
    </view>
    <view class="track" style="transform:translateX({{oldx}}px)"></view>
    <movable-area>
        <movable-view bindchange="onChange" class="{{isOk?'active':''}}" direction="horizontal" id="track" x="{{x}}"></movable-view>
    </movable-area>
    <view class="disabled" wx:if="{{isOk}}"></view>
</view>
