<view class="weeee" wx:if="{{modalShow=='common'}}">
    <dialog>
        <view class="info-wrapper">
            <view class="wwww prime-status">
                <image catchtap="hideModal" class="x-icon" src="/img/icon/model-x.png"></image>
                <image class="xr-title-image" src="/img/icon/hyfd.png"></image>
                <scroll-view class="scroll-wrapper" scrollY="true">
                    <view class="coupon-wrapper" wx:if="{{crmqList.length>0}}">
                        <image class="xiangxia" src="../../img/icon/xiangxia.png"></image>
                        <view class="title-lb">门店券</view>
                        <scroll-view class="ssddd" scrollX="true">
                            <view class="coupon-list">
                                <view class="coupon-item" wx:for="{{crmqList}}">
                                    <image class="quan-bg" src="/img/icon/xr-quan1.png"></image>
                                    <view class="money-shu">
                                        <text>{{item.mz}}</text>
                                        <text class="yuan">元</text>
                                    </view>
                                    <view class="da-title">{{item.title}}</view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                    <view class="coupon-wrapper" wx:if="{{xsscqList.length>0}}">
                        <image class="xiangxia" src="../../img/icon/xiangxia.png"></image>
                        <view class="title-lb">线上商城券</view>
                        <scroll-view class="ssddd" scrollX="true">
                            <view class="coupon-list">
                                <view class="coupon-item" wx:for="{{xsscqList}}">
                                    <image class="quan-bg" src="/img/icon/xr-quan1.png"></image>
                                    <view class="money-shu">
                                        <text>{{item.mz}}</text>
                                        <text class="yuan">元</text>
                                    </view>
                                    <view class="da-title">{{item.title}}</view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                    <view class="coupon-wrapper" wx:if="{{xxdkqList.length>0}}">
                        <image class="xiangxia" src="../../img/icon/xiangxia.png"></image>
                        <view class="title-lb">线下抵扣券</view>
                        <scroll-view class="ssddd" scrollX="true">
                            <view class="coupon-list">
                                <view class="coupon-item" wx:for="{{xsscqList}}">
                                    <image class="quan-bg" src="/img/icon/xr-quan1.png"></image>
                                    <view class="money-shu">
                                        <text>{{item.mz}}</text>
                                        <text class="yuan">元</text>
                                    </view>
                                    <view class="da-title">{{item.title}}</view>
                                </view>
                            </view>
                        </scroll-view>
                    </view>
                </scroll-view>
                <button catchtap="receiveGift" class="shouqu-btn btnss" disabled="{{btnDisabled}}">收入囊中</button>
            </view>
        </view>
    </dialog>
</view>
<view class="weeee" style="" wx:if="{{islinkimg&&imgurls.length>0}}">
    <view style="margin-top: 300rpx;">
        <swiper autoplay="{{autoplay}}" circular="true" duration="{{duration}}" indicatorDots="{{indicatorDots}}" interval="{{interval}}" style="width: 600rpx;margin-top: 300rpx;margin:auto;height:800rpx;">
            <swiper-item wx:for="{{imgurls}}" wx:for-item="it" wx:key="key">
                <image bindtap="imglink" class="linkimg" data-out_appid="{{it.outAppid}}" data-screenid="{{it.id}}" data-sendtype="{{it.sendtype}}" mode="widthFix" src="{{it.imgurl}}"></image>
            </swiper-item>
        </swiper>
        <image catchtap="closelink" class="x-icon2" data-screenid="{{screenid}}" src="/img/icon/model-x.png"></image>
    </view>
</view>
