<view class="content">
    <view class="list" wx:if="{{order_info.invoice_type==2}}">发票状态 <text wx:if="{{!invoice_info.fphm&&!invoice_info.fpdm}}">开票中</text>
        <text wx:else>开票成功</text>
    </view>
    <view class="list" wx:else>发票状态 <text wx:if="{{order_info.status==0}}">未开票</text>
        <text wx:else>已开票</text>
    </view>
</view>
<view class="content">
    <view class="list">订单状态<text>{{order_info.order_status}}</text>
    </view>
    <view class="list">订单编号<text>{{order_code}}</text>
    </view>
    <view class="list">下单时间<text>{{order_info.order_time}}</text>
    </view>
    <view class="list">发票类型<text>{{order_info.inv_type}}</text>
    </view>
</view>
<view class="content">
    <view class="list">发票抬头<text>{{order_info.title}}</text>
    </view>
    <view class="list" wx:if="{{order_info.header_type==1}}">公司税号<text>{{order_info.tax_number}}</text>
    </view>
</view>
<view class="content" wx:if="{{order_info.invoice_type==2||order_info.invoice_type==0||order_info.invoice_type==1}}">
    <view class="list">电子邮箱 <view class="list_cho">
            <input focus bindinput="emailDetail" placeholder="用来接收电子邮件(必填)" value="{{order_info.email}}"></input>
        </view>
    </view>
</view>
<view class="content input-under" wx:if="{{order_info.status==0&&order_info.invoice_type==3}}">
    <view class="list">温馨提示 <view class="list_cho"> 您的纸质专票申请已提交成功，我司财务预计在1个月内开出并邮寄给您，请您耐心等待。 </view>
    </view>
</view>
<view class="content input-under" wx:if="{{order_info.status==0&&(order_info.invoice_type==0||order_info.invoice_type==1)}}">
    <view class="list">温馨提示 <view class="list_cho"> 您的发票已申请，预计在48小时内开出,请您耐心等待。 </view>
    </view>
</view>
<view class="content input-under" wx:if="{{order_info.status==1}}">
    <view class="list">温馨提示 <view class="list_cho"> 您的发票已开出，暂不支持更改，如有需求请联系平台客服。 </view>
    </view>
</view>
<view class="list_last"></view>
<button bindtap="reInvoice" class="btn" wx:if="{{order_info.invoice_type==2&&order_info.status!=2}}">发送到邮箱</button>
