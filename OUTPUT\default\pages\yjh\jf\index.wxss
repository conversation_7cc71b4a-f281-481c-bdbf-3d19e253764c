@import "..\..\..\wxss\style.wxss";

page {
    background: #fff
}

.index_bg {
    background: url("https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/07/15936517238250.jpg") no-repeat;
    background-size: 100% 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10000
}

.back_arrow {
    height: 40rpx;
    width: 40rpx
}

.index_name {
    color: #fff;
    font-size: 32rpx;
    text-align: center
}

.log {
    color: #fff;
    font-size: 24rpx;
    height: 34rpx;
    padding: 28rpx 58rpx;
    position: relative
}

.log_icon {
    height: 30rpx;
    left: 20rpx;
    position: absolute;
    top: 31rpx;
    width: 28rpx
}

.head {
    height: 68rpx;
    margin-top: -70rpx;
    position: relative;
    width: 70%
}

.head_seach {
    background: #f4f4f4;
    border-radius: 50rpx;
    font-size: 24rpx;
    height: 56rpx;
    line-height: 56rpx;
    margin: 0 50rpx;
    padding: 0 28rpx 0 60rpx;
    text-align: left
}

.head_seach_icon {
    height: 32rpx;
    left: 60rpx;
    position: absolute;
    top: 14rpx;
    width: 32rpx;
    z-index: 100
}

.container {
    position: relative;
    width: 100%
}

.container,.nav {
    height: 70rpx;
    line-height: 70rpx
}

.nav {
    box-sizing: border-box;
    font-size: 16px;
    left: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    white-space: nowrap;
    width: 80%;
    z-index: 99
}

.nav-item {
    color: #fff;
    display: inline-block;
    font-size: 28rpx;
    font-weight: 400;
    margin: 0 20rpx;
    text-align: center
}

.nav-item.active {
    color: #fff;
    font-size: 32rpx;
    font-weight: 700
}

.index_hd_rt {
    box-sizing: border-box;
    color: #fff;
    font-size: 28rpx;
    height: 70rpx;
    line-height: 70rpx;
    padding-left: 70rpx;
    position: absolute;
    right: 0;
    text-align: left;
    top: 0;
    width: 20%
}

.index_line {
    height: 44rpx;
    left: 0;
    position: absolute;
    top: 13rpx;
    width: 16rpx
}

.index_arrow {
    height: 28rpx;
    left: 26rpx;
    position: absolute;
    top: 21rpx;
    width: 34rpx
}

.whole {
    background: #fff;
    border-radius: 20rpx
}

.head_cast {
    padding: 20rpx 20rpx 0;
    position: relative;
    z-index: 1002
}

wx-swiper {
    height: 312rpx
}

.slide-image,wx-swiper-item {
    border-radius: 8rpx
}

.slide-image {
    display: block;
    height: 312rpx;
    width: 100%
}

.swiper-box .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: 0rpx
}

.swiper-box .wx-swiper-dot {
    display: inline-flex;
    height: 4rpx;
    justify-content: space-between;
    margin-left: -8rpx;
    width: 24rpx
}

.swiper-box .wx-swiper-dot::before {
    background: #fff;
    border-radius: 8rpx;
    content: "";
    flex-grow: 1
}

.swiper-box .wx-swiper-dot-active::before {
    background: rgba(0,0,0,.3)
}

.swiper_tab {
    background: #fff;
    height: 186rpx;
    padding-top: 14rpx
}

.cate_list {
    float: left;
    height: 135rpx;
    width: 20%
}

.cate_icon {
    display: block;
    height: 92rpx;
    margin: 0 auto;
    width: 92rpx
}

.cate_text {
    color: #282828;
    font-size: 26rpx;
    text-align: center
}

.other_content {
    background: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    padding: 20rpx 0
}

.other_con,.other_content {
    margin-bottom: 20rpx
}

.other_con:last-child {
    margin-bottom: 0
}

.mall_hd_title {
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
    margin-bottom: 4rpx;
    padding: 0 18rpx
}

.title_top {
    margin-top: 20rpx
}

.mall_hd_on {
    color: #d59f6a
}

.mall_more {
    color: #282828;
    float: right;
    font-size: 24rpx;
    font-weight: 400
}

.mall_icon {
    float: right;
    height: 20rpx;
    margin-left: 8rpx;
    margin-top: 12rpx;
    width: 12rpx
}

.scroll {
    margin-left: 18rpx
}

.scroll_view_item {
    display: flex;
    white-space: nowrap
}

.scroll_mall_list {
    border-radius: 16rpx;
    box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,.11);
    display: inline-block;
    margin-right: 20rpx;
    overflow: hidden;
    padding: 18rpx;
    position: relative;
    width: 250rpx
}

.scroll_mall_img {
    display: block;
    height: 250rpx;
    margin: 0 auto 16rpx;
    width: 250rpx
}

.scroll_mall_title {
    color: #282828;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx;
    margin-bottom: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.scroll_mall_point {
    color: #b1261e;
    font-size: 24rpx;
    height: 34rpx;
    line-height: 34rpx;
    margin-bottom: 4rpx
}

.scroll_mall_point wx-text:last-child {
    color: #c6c2be;
    font-size: 20rpx;
    margin-left: 10rpx;
    text-decoration: line-through
}

.scroll_mall_num {
    color: #c6c2be;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx
}

.ranking {
    display: flex;
    margin-bottom: 30rpx;
    padding: 0 18rpx
}

.ranking .scroll_mall_list {
    flex: 1;
    margin-right: 18rpx;
    position: relative;
    width: 190rpx
}

.ranking .scroll_mall_list:last-child {
    margin-right: 0
}

.ranking .scroll_mall_img {
    height: 190rpx;
    width: 190rpx
}

.ranking_icon {
    height: 52rpx;
    left: 20rpx;
    position: absolute;
    top: 0;
    width: 36rpx
}

.brand_list {
    background: linear-gradient(180deg,#fff7f0,#fffffe);
    border-radius: 16rpx;
    box-shadow: 0rpx 8rpx 10rpx 0rpx #eff1f4;
    display: inline-block;
    margin-right: 18rpx;
    overflow: hidden;
    padding: 20rpx 0;
    width: 260rpx
}

.brand_img {
    display: block;
    height: 132rpx;
    margin: 0 auto 36rpx;
    width: 132rpx
}

.brand_title {
    color: #282828;
    font-size: 40rpx;
    height: 56rpx;
    line-height: 56rpx;
    margin-bottom: 10rpx;
    overflow: hidden;
    padding: 0 18rpx;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap
}

.brand_point {
    color: #b1261e;
    font-size: 22rpx;
    text-align: center
}

.brand_point,.brand_point_null {
    height: 32rpx;
    line-height: 32rpx;
    margin-bottom: 4rpx;
    padding: 0 18rpx
}

.pay_list {
    border-radius: 16rpx;
    box-shadow: 0 4rpx 8rpx 0 rgba(0,0,0,.11);
    display: inline-block;
    margin-right: 20rpx;
    overflow: hidden;
    padding: 18rpx;
    width: 250rpx
}

.pay_img {
    display: block;
    height: 250rpx;
    margin: 0 auto 16rpx;
    width: 250rpx
}

.pay_title {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    color: #282828;
    display: -webkit-box;
    font-size: 20rpx;
    height: 56rpx;
    line-height: 28rpx;
    margin-bottom: 10rpx;
    overflow: hidden;
    white-space: normal
}

.pay_title wx-text {
    background: #222;
    color: #d59f6a;
    display: inline-block;
    margin-right: 8rpx;
    padding: 0 8rpx
}

.pay_point {
    color: #b1261e;
    font-size: 24rpx;
    height: 34rpx;
    line-height: 34rpx;
    margin-bottom: 4rpx
}

.pay_num {
    color: #c6c2be;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx
}

.mall_content {
    padding: 0 18rpx
}

.mall_hd {
    display: flex;
    height: 96rpx;
    line-height: 96rpx
}

.mal_hd_flex {
    color: #282828;
    flex: 1;
    font-size: 24rpx;
    position: relative;
    text-align: center
}

.mal_hd_on {
    color: #b1261e
}

.madll_hd_img {
    height: 14rpx;
    position: absolute;
    right: 25%;
    top: 43rpx;
    width: 10rpx
}

.mall_list {
    border-radius: 16rpx;
    box-shadow: 0rpx 8rpx 10rpx 0rpx #eff1f4;
    float: left;
    margin-bottom: 24rpx;
    overflow: hidden;
    width: 348rpx
}

.mall_list:nth-child(even) {
    float: right
}

.mall_img {
    display: block;
    height: 348rpx;
    margin-bottom: 16rpx;
    width: 348rpx
}

.mall_title {
    color: #282828;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx;
    margin-bottom: 10rpx;
    overflow: hidden;
    padding: 0 18rpx;
    text-overflow: ellipsis;
    white-space: nowrap
}

.mall_point {
    color: #b1261e;
    font-size: 24rpx;
    height: 34rpx;
    line-height: 34rpx;
    margin-bottom: 4rpx;
    padding: 0 18rpx
}

.mall_num {
    color: #c6c2be;
    font-size: 20rpx;
    height: 28rpx;
    line-height: 28rpx;
    margin-bottom: 20rpx;
    padding: 0 18rpx
}

.only_bg,.only_title {
    display: block;
    width: 100%
}

.only_bg {
    margin: 20rpx 0
}

.cs2 {
    background-color: #ff9049;
    height: 70rpx;
    line-height: 70rpx;
    position: absolute;
    top: 202rpx;
    width: 100%;
    z-index: 1003
}

.index_car {
    bottom: 200rpx;
    height: 203rpx;
    position: fixed;
    right: 0;
    width: 209rpx
}

.index_car_go {
    bottom: 24rpx;
    height: 88rpx;
    position: absolute;
    right: 16rpx;
    width: 88rpx
}

.list {
    background: #fff;
    box-sizing: border-box;
    left: 0;
    padding: 20rpx 18rpx;
    position: absolute;
    top: 270rpx;
    width: 100%;
    z-index: 1003
}

.list_close {
    height: 40rpx;
    line-height: 40rpx;
    margin-bottom: 40rpx
}

.list_close wx-text {
    color: #fff;
    float: right;
    font-size: 26rpx
}

.list_tab {
    padding: 0
}

.list_flex {
    background: #fff;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    box-sizing: border-box;
    color: #282828;
    float: left;
    font-size: 24rpx;
    height: 64rpx;
    line-height: 64rpx;
    margin-bottom: 24rpx;
    margin-right: 24rpx;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 160rpx
}

.list_flex:nth-child(4n+4) {
    margin-right: 0
}

.list_flex_on {
    border: 1rpx solid #d59f6a;
    color: #d59f6a
}

.mall_banner {
    margin-bottom: 20rpx
}

.banner_img {
    display: block;
    width: 100%
}

.index_bg_popup {
    background: url("https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/07/15936517238250.jpg") no-repeat;
    background-size: 100% 100%;
    left: 0;
    padding: 30rpx 0;
    position: fixed;
    top: 84rpx;
    width: 100%;
    z-index: 10000
}

.last_bottom {
    margin-bottom: 120rpx
}

.mall_fixed {
    background: #fff;
    bottom: 0;
    display: flex;
    height: 100rpx;
    left: 0;
    line-height: 100rpx;
    position: fixed;
    text-align: center;
    width: 100%
}

.mall_fixed::after {
    background: #ddd;
    content: "";
    height: 42rpx;
    left: 50%;
    margin-left: -1rpx;
    position: absolute;
    top: 29rpx;
    width: 2rpx
}

.mall_flex {
    color: #282828;
    flex: 1;
    font-size: 32rpx
}

.flex_on {
    color: #d59f6a
}

.cont .wx-swiper-dots.wx-swiper-dots-horizontal {
    margin-bottom: 0rpx
}

.cont .wx-swiper-dot {
    display: inline-flex;
    justify-content: space-between;
    margin-left: -8rpx
}

.cont .wx-swiper-dot::before {
    background: rgba(0,0,0,.3);
    border-radius: 8rpx;
    content: "";
    flex-grow: 1
}

.cont .wx-swiper-dot-active::before {
    background: #ff9049
}

.other_bg {
    margin-bottom: 20rpx;
    padding: 0 20rpx;
    position: relative
}

.other_img {
    display: block;
    width: 100%
}

.other_bg_title {
    font-size: 40rpx;
    top: 76rpx
}

.other_bg_subtitle,.other_bg_title {
    color: #fff;
    left: 0;
    position: absolute;
    text-align: center;
    width: 100%
}

.other_bg_subtitle {
    font-size: 20rpx;
    top: 140rpx
}

.back_lt_img {
    display: block;
    position: absolute;
    top: 0rpx
}

.over_price {
    color: #fff;
    font-size: 18rpx;
    left: 18rpx;
    position: absolute;
    top: 240rpx
}

.over_del {
    color: #d8bfd8;
    font-size: 12rpx;
    left: 88rpx;
    position: absolute;
    text-decoration: line-through;
    top: 248rpx
}

.over_jf {
    color: #fff;
    font-size: 24rpx;
    left: 180rpx;
    position: absolute;
    top: 218rpx
}
