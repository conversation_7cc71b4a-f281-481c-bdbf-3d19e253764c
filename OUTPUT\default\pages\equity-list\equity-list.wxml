<view class="top-margin"></view>
<view class="null" wx:if="{{equityList.length==0}}">暂无权益活动</view>
<view class="equity-wrapper" data-index="{{index}}" wx:for="{{equityList}}">
    <view bindtap="goEquityDetail" class="equity-info {{item.isReceive==0?'':'huise'}}" data-activityEquityId="{{item.activityEquityId}}" data-equityId="{{item.equityId}}">
        <view class="equity-left">
            <view class="radius-top-right"></view>
            <view class="radius-bottom-right"></view>
            <view class="jine">
                <view class="common-hy" wx:if="{{item.needPrime==0}}">普通会员</view>
                <view class="prime-hy" wx:else>Prime会员</view>
                <view class="row-d">
                    <view class="lijian">立减</view>
                    <view class="money">
                        <text class="rmb">￥</text>{{item.jian}}</view>
                </view>
            </view>
            <view class="dagai">
                <view class="name-title">{{item.name}}</view>
                <view class="yxq">{{item.syrqStart}}~{{item.syrqEnd}}</view>
            </view>
        </view>
        <button catchtap="receiveEquity" class="eeee equity-right" data-activityEquityId="{{item.activityEquityId}}" data-equityId="{{item.equityId}}" data-isReceive="{{item.isReceive}}" data-jifen="{{item.jifen}}" data-lqlx="{{item.lqlx}}" disabled="{{btnDisabled}}">
            <view class="dotted-left"></view>
            <view class="radius-top-left"></view>
            <view class="radius-bottom-left"></view>
            <view class="receive" wx:if="{{item.isReceive==0}}">{{item.lqlx==0?'立即领取':'积分兑换'}}</view>
            <view class="receive" wx:else>已领完</view>
        </button>
    </view>
</view>
<back-home homeShow="{{homeShow}}"></back-home>
