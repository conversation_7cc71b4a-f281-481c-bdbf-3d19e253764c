<view class="top-info">
    <view>单号：{{orderno}}</view>
    <view class="right-status">
        <block wx:if="{{order.isCancel==0&&order.isPay==0}}">待支付</block>
        <block wx:elif="{{order.isCancel==1}}">已取消</block>
        <block wx:elif="{{order.isPay==1}}">已购买</block>
        <block wx:else>未知状态</block>
    </view>
</view>
<view class="list">
    <view class="item" wx:for="{{goodList}}" wx:for-index="idx" wx:for-item="it">
        <view class="item01">
            <view>{{it.code}}</view>
            <view>￥{{it.price}}</view>
        </view>
        <view class="item02">
            <view>名称</view>
            <view>{{it.name}}</view>
        </view>
        <view class="item02">
            <view>数量</view>
            <view>x{{it.num}}</view>
        </view>
        <view class="item03">
            <view>折扣</view>
            <view>￥{{it.discount}}</view>
        </view>
        <view class="item03">
            <view>券优惠</view>
            <view>￥{{it.youhui}}</view>
        </view>
        <view class="item03">
            <view>积分抵扣</view>
            <view>￥{{it.point}}</view>
        </view>
        <view class="item03">
            <view>专厅券抵扣</view>
            <view>￥{{it.ztq}}</view>
        </view>
        <view class="item04">
            <view>金额</view>
            <view>￥{{it.totalprice}}</view>
        </view>
    </view>
</view>
<view class="total">
    <view class="item05">
        <view>件数</view>
        <view>x{{allnum}}</view>
    </view>
    <view class="item05">
        <view>总金额</view>
        <view>￥{{allprice}}</view>
    </view>
    <view class="item05">
        <view>总计券优惠</view>
        <view>￥{{onlineorder.couponPay}}</view>
    </view>
    <view class="item05">
        <view>总计积分抵扣</view>
        <view>￥{{onlineorder.pointAmount}}</view>
    </view>
    <view class="item05">
        <view>专厅券抵扣</view>
        <view>￥{{onlineorder.ztqAmount}}</view>
    </view>
</view>
<view class="total" wx:if="{{onlineorder.couponPay>0}}">
    <view class="item05">
        <view>使用的优惠券如下</view>
    </view>
    <view class="coupon" wx:for="{{onlineorder.couponList}}" wx:for-item="it">
        <view class="ckbtext">
            <view class="sysm">名称:{{it.qmc}}</view>
            <view class="sysm">发放:{{it.ffsm}}</view>
            <view class="sysm">使用:{{it.sysm}}</view>
            <view class="sysm">余额:{{it.ye}}元</view>
            <view class="sysm">时间:{{it.ksrq}}-{{it.jsrq}}</view>
        </view>
    </view>
</view>
<view class="btn-wrapper">
    <button bindtap="tapSqkp" class="btn-active" wx:if="{{order.isPay==1&&order.invoiceFlag==0&&order.isRefund==0&&allprice>0&&hyid==order.hyid}}">申请开票</button>
    <button bindtap="tapCkkp" class="btn-active" wx:if="{{order.isPay==1&&order.invoiceFlag==1&&order.isRefund==0&&allprice>0&&hyid==order.hyid}}">查看开票</button>
    <button bindtap="tapLjzf" class="btn-active" wx:if="{{order.isCancel==0&&order.isPay==0&&hyid==order.hyid}}">立即支付</button>
    <view style="color:red;" wx:if="{{order.isCancel==0&&order.isPay==0&&hyid!=order.hyid}}"> 开单会员卡号和本账号不匹配 </view>
</view>
<modal-pay bindpayevent="pay" disabled="{{zfDisabled}}" isShow="{{isModalShow}}" price="{{payprice}}" saleName="{{saleName}}"></modal-pay>
