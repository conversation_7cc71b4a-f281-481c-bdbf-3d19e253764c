<half-dialog bind:dialogClose="closeDialog" data-dialog="showStoreGuide" show="{{showStoreGuide}}">
    <view class="contact-dialog-header" slot="title">
        <image class="contact-dialog-icon" mode="aspectFill" src="{{openStoreGuideList.store_logo}}"></image>
        <view class="contact-dialog-name">{{openStoreGuideList.store_name}}</view>
    </view>
    <view class="contact-dialog-body">
        <view class="guider-list-item" wx:for="{{openStoreGuideList.guides}}" wx:key="index">
            <view class="guider-item-name">
                <auth-image class="guider-item-portrait" src="{{item.portrait}}"></auth-image>
                <view class="guider-name">{{item.name}}</view>
            </view>
            <view bind:tap="openGuideWechat" class="guider-item-contact exposure-report-dom" data-exposure-data="{{({event_id:100009,store_id:openStoreGuideList.store_id,store_name:openStoreGuideList.store_name,guide_id:item.guide_id})}}" data-item="{{item}}" data-report-data="{{({event_id:5003,store_id:openStoreGuideList.store_id,store_name:openStoreGuideList.store_name,guide_id:item.guide_id})}}" id="guider-item-contact-{{index}}">
                <image class="guider-contact-icon" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/icon-wechat-white.png"></image> 联系TA </view>
        </view>
    </view>
</half-dialog>
