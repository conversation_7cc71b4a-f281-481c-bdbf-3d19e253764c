<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{curStatus==it.status?'text-blue cur cust-color':''}} " data-status="{{it.status}}" wx:for="{{titleList}}" wx:for-item="it"> {{it.name}} </item>
    </view>
</scroll-view>
<view class="padding-cust">
    <view class="item-wrapper" wx:for="{{renderList}}" wx:for-item="it">
        <view class="product-item padding-sm solid-bottom">单号：{{it.orderno}}</view>
        <view class="product-item padding-sm solid-bottom">
            <view class="leftv">
                <view class="hyname">来自{{it.bmmc}}的收款</view>
                <view class="ctime">创建时间 {{it.createtime}}</view>
            </view>
            <view class="totalprice">￥{{it.price/100}}</view>
        </view>
        <view class="count">
            <view class="status" wx:if="{{it.isCancel==0&&it.isPay==0}}">待支付</view>
            <view class="status" wx:if="{{it.isCancel==1}}">已取消</view>
            <view class="status" wx:if="{{it.isPay==1}}">已购买</view>
            <view class="mingxi">共{{it.itemList.length}}件商品 合计：{{it.price/100}}</view>
        </view>
        <view class="count" wx:if="{{it.isPay==1}}">
            <view class="">退款情况： <block wx:if="{{it.isRefund==0}}">无</block>
                <block wx:if="{{it.isRefund==1}}">已退款</block>
                <block wx:if="{{it.isRefund==2}}">等待厅房一级审批</block>
                <block wx:if="{{it.isRefund==3}}">等待主管二级审批</block>
                <block wx:if="{{it.isRefund==4}}">二级审批完成还未到账</block>
            </view>
        </view>
        <view class="btn-wrapper solid-top">
            <button bindtap="refund" class="cg-btn" data-hyname="{{it.hyname}}" data-orderno="{{it.orderno}}" wx:if="{{it.isPay==1&&it.isRefund==0}}">申请退款</button>
            <button class="cg-btn" data-hyname="{{it.hyname}}" data-orderno="{{it.orderno}}" wx:if="{{it.isPay==1&&it.isRefund==2}}">审核中</button>
            <button class="cg-btn" data-hyname="{{it.hyname}}" data-orderno="{{it.orderno}}" wx:if="{{it.isPay==1&&it.isRefund==1}}">已退款</button>
            <button bindtap="detail" class="cg-btn" data-hyname="{{it.hyname}}" data-orderno="{{it.orderno}}">详情</button>
            <button bindtap="confirm" class="cg-btn" data-hyname="{{it.hyname}}" data-orderno="{{it.orderno}}" wx:if="{{it.isCancel==0&&it.isPay==0}}">支付</button>
        </view>
    </view>
    <view style="height:100rpx"></view>
</view>
<modal-pay bindpayevent="pay" disabled="{{disabled}}" isShow="{{isModalShow}}" price="{{payprice}}" saleName="{{saleName}}"></modal-pay>
