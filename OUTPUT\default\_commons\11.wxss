.icon__close-white {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_common_close-427f8ed891.svg) no-repeat 50%;
    background-size: contain
}

.icon__mute {
    background: url(https://res.wx.qq.com/a/fed_upload/7868426c-370b-4d60-b7c0-99e2f04ded6e/icon__mute__active.svg) no-repeat 50%/contain
}

.icon__mike,.icon__mute {
    display: inline-block;
    width: 24px;
    height: 24px
}

.icon__mike {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__voice-d8926acfb0.svg) no-repeat 50%;
    background-size: contain
}

.icon__code__rate {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__code__rate-d5a8774649.svg) no-repeat 50%;
    background-size: contain
}

.selected .icon__code__rate {
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath d='M9 0a9 9 0 110 18A9 9 0 019 0zm0 1.08a7.92 7.92 0 100 15.84A7.92 7.92 0 009 1.08z' id='b'/%3E%3Cpath d='M12.287 4.347L8.57 8.269a1.37 1.37 0 00-.252 1.508c.23.507.744.822 1.297.794a1.355 1.355 0 001.214-.919l1.85-5.057a.24.24 0 00-.094-.287.24.24 0 00-.298.039z' id='c'/%3E%3Cfilter x='-44.4%25' y='-44.4%25' width='188.9%25' height='188.9%25' filterUnits='objectBoundingBox' id='a'%3E%3CfeOffset dy='1' in='SourceAlpha' result='shadowOffsetOuter1'/%3E%3CfeGaussianBlur stdDeviation='1.5' in='shadowOffsetOuter1' result='shadowBlurOuter1'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0' in='shadowBlurOuter1' result='shadowMatrixOuter1'/%3E%3CfeMerge%3E%3CfeMergeNode in='shadowMatrixOuter1'/%3E%3CfeMergeNode in='SourceGraphic'/%3E%3C/feMerge%3E%3C/filter%3E%3C/defs%3E%3Cg filter='url(%23a)' transform='translate(3 3)' fill='none'%3E%3Cuse fill='%23FFF' xlink:href='%23b'/%3E%3Cuse fill-opacity='.5' fill='%23FF6146' xlink:href='%23b'/%3E%3Cg transform='rotate(12 10.445 7.423)'%3E%3Cuse fill='%23FFF' xlink:href='%23c'/%3E%3Cuse fill-opacity='.5' fill='%236467F0' xlink:href='%23c'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") no-repeat 50%
}

.icon__camera {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__camare-751c71f5d7.svg) no-repeat 50%;
    background-size: cover
}

.selected .icon__camera {
    background: url(https://res.wx.qq.com/a/fed_upload/7868426c-370b-4d60-b7c0-99e2f04ded6e/icon__carmer__active.svg) no-repeat 50%
}

.icon__info__watch {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_watching-8c26e4fd9d.svg) no-repeat 50%;
    background-size: contain
}

.icon__info__like,.icon__info__watch {
    display: inline-block;
    width: 14px;
    height: 14px
}

.icon__info__like {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_like-e53e04debd.svg) no-repeat 50%;
    background-size: contain
}

.icon__info__comment {
    display: inline-block;
    width: 14px;
    height: 14px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_info_comment-3e71d82c49.svg) no-repeat 50%;
    background-size: contain
}

.icon__profile {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_profile-ac36b39a06.svg) no-repeat 50%;
    background-size: contain
}

.icon__setting {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_setting-d8bc4c1811.svg) no-repeat 50%;
    background-size: contain
}

.icon__lottery {
    display: inline-block;
    width: 16px;
    height: 18px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='18' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.456 1.761c-.06 0-.12.006-.179.017a.998.998 0 00-.743.57L9.718 4.1h1.329l.834-.395c.301-.14.513-.418.567-.745a1.01 1.01 0 00-.28-.898 1.001 1.001 0 00-.712-.3zm-6.917.004a.999.999 0 00-.71.3 1.01 1.01 0 00-.283.896c.056.33.268.61.568.748l.836.394 1.329-.001-.817-1.75a.999.999 0 00-.74-.57 1.02 1.02 0 00-.183-.017zM1.47 9.451C.66 9.451 0 8.788 0 7.973V5.585c0-.816.658-1.48 1.469-1.48h.644a2.79 2.79 0 01-.291-.836A2.798 2.798 0 012.588.82 2.776 2.776 0 014.546.004c.546 0 1.081.164 1.546.474.409.272.747.678.978 1.172l.928 1.988.917-1.969c.277-.597.702-1.06 1.228-1.34a2.748 2.748 0 013.261.485 2.793 2.793 0 01.48 3.284h.644c.81 0 1.47.662 1.47 1.477L16 7.964a1.474 1.474 0 01-1.469 1.48L1.472 9.45h-.001zm1 8.547a1.476 1.476 0 01-1.469-1.478L1 11.145a.88.88 0 01.876-.882l4.748-.003a.88.88 0 01.877.881l.003 5.972a.88.88 0 01-.875.882l-4.155.003h-.001zM9.379 18a.88.88 0 01-.876-.881L8.5 11.147a.878.878 0 01.875-.882l4.75-.003a.88.88 0 01.876.881l.003 5.375a1.474 1.474 0 01-1.469 1.48L9.38 18z' fill='%23FFF' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%;
    background-size: contain
}

.icon__recommend-area__target {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__recommend-area__target-9ae473bfdb.svg) no-repeat 50%;
    background-size: contain
}

.icon_go_small {
    display: inline-block;
    width: 7px;
    height: 20px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_go_small-0d9a37167d.svg) no-repeat 50%;
    background-size: contain
}

.page__icon__loading {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-align-items: center;
    align-items: center;
    width: 44px;
    height: 44px;
    border-radius: 4px;
    padding: 0;
    background-color: rgba(0,0,0,.2)
}

.page__icon__loading:after {
    content: " ";
    display: block;
    width: 28px;
    height: 28px
}

.icon_loading,.page__icon__loading:after {
    background: url(https://res.wx.qq.com/wxaliveplayer/htdocs/loading4e79d0.gif) no-repeat 50%;
    background-size: contain
}

.icon_loading {
    display: inline-block;
    width: 32px;
    height: 32px
}

.icon__more {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5 10.25a1.75 1.75 0 110 3.5 1.75 1.75 0 010-3.5zm7 0a1.75 1.75 0 110 3.5 1.75 1.75 0 010-3.5zm7 0a1.75 1.75 0 110 3.5 1.75 1.75 0 010-3.5z' fill='%23FFF' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.icon__mark {
    display: inline-block;
    width: 20px;
    height: 25px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon_mark-b1428871db.svg) no-repeat 50%;
    background-size: contain
}

.icon__clearness {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__clearness-3c29f18679.svg) no-repeat 50%;
    background-size: contain
}

.icon__clearness,.icon__phone-setting {
    display: inline-block;
    width: 24px;
    height: 24px
}

.icon__phone-setting {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__phone-setting-baba21d8e6.svg) no-repeat 50%;
    background-size: contain
}

.icon__beauty {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/fairy-23d264390d.svg) no-repeat 50%;
    background-size: contain
}

.icon__beauty,.icon__white {
    display: inline-block;
    width: 24px;
    height: 24px
}

.icon__white {
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/face-e7d84e2794.svg) no-repeat 50%;
    background-size: contain
}

.icon__mirror {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url(https://res.wx.qq.com/a/wx_fed/wechat_search_common_assets/res/wxlive/icon__mirror-b93a42d137.svg) no-repeat 50%;
    background-size: contain
}

.selected .icon__mirror {
    background: url(https://res.wx.qq.com/a/fed_upload/7868426c-370b-4d60-b7c0-99e2f04ded6e/icon__mirror__active.svg) no-repeat 50%/contain
}

.setting-icon__mic {
    width: 20px;
    height: 18px;
    background: url(https://res.wx.qq.com/op_res/16z_e66OhhH9Km2BpxomkaTGDnHDbTtlo6boiR6yZF8E8G2L1KsJxxUgcWv7YpGD) no-repeat 50%/contain
}

.icon__setting__mic {
    width: 20px;
    height: 18px;
    background: url(https://res.wx.qq.com/a/fed_upload/8497b495-8ea8-435f-bb46-d82413222504/icons_outlined_connect.svg) no-repeat 50%/contain
}

.icon__setting__helper {
    width: 18px;
    height: 20px;
    background: url(https://res.wx.qq.com/a/fed_upload/bd2e80c2-4dbc-4de8-815c-b2357f1fa7b1/icons_outlined_subsidiary.svg) no-repeat 50%/contain
}

.icon__setting__forbid {
    width: 20px;
    height: 18px;
    background: url(https://res.wx.qq.com/op_res/WKwS0pAU2ThzOJ20d3HEYI30LUjJUQkVxjbrja0kVJc3gU5MS-dFMYVL4LtM_POk) no-repeat 50%/contain
}

.icon__setting__follow__push {
    width: 24px;
    height: 24px;
    background: url("data:image/svg+xml;charset=utf-8,%3Csvg width='24' height='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.187 20a1 1 0 01-.806-1.592C5.46 16.938 6 15.469 6 14v-4a6.003 6.003 0 014.027-5.668 2 2 0 113.945 0A6.003 6.003 0 0118 10v4c0 1.47.54 2.939 1.62 4.408A1 1 0 0118.812 20H13v.2a1 1 0 11-2 0V20H5.187zM12 3.2a.8.8 0 00-.789.934l.167.999-.956.332A4.802 4.802 0 007.2 10v4c0 1.63-.552 3.236-1.626 4.8h12.852C17.352 17.236 16.8 15.63 16.8 14v-4c0-2.06-1.309-3.87-3.222-4.535l-.956-.332.167-.999A.8.8 0 0012 3.2z' fill='%23FFF' fill-rule='evenodd'/%3E%3C/svg%3E") no-repeat 50%/contain
}

.icon__setting__advance {
    width: 24px;
    height: 24px;
    background: url(https://res.wx.qq.com/a/fed_upload/f4e41cbe-dbe3-4b4b-bf12-ed9bb584e340/videoPlay.svg) no-repeat 50%/contain
}

@-webkit-keyframes loading {
    0% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(7deg) translateZ(0);
        transform: rotate(7deg) translateZ(0)
    }

    to {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(1turn) translateZ(0);
        transform: rotate(1turn) translateZ(0)
    }
}

@keyframes loading {
    0% {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(7deg) translateZ(0);
        transform: rotate(7deg) translateZ(0)
    }

    to {
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: rotate(1turn) translateZ(0);
        transform: rotate(1turn) translateZ(0)
    }
}