<view bindtap="hideAllTip" class="chepai-con">
    <view class="chepai-input">
        <view class="chepai-img">
            <image src="/img/icon/qiche.png"></image>
        </view>
        <view catchtap="showJianpan" class="chapai-item {{isShow&&activeIndex==item?'chepai-active':''}} {{item==7&&!plateNo[item]?'xinnengyuan':''}}" data-xh="{{item}}" wx:for="{{8}}">
            <block wx:if="{{item==7}}">{{plateNo[item]?plateNo[item]:'新能源'}}</block>
            <block wx:else>{{plateNo[item]}}</block>
        </view>
    </view>
    <view class="gouxuan-wrapper">
        <block wx:if="{{isAuto==1}}">
            <view bindtap="chooseRelief" class="gouxuan">
                <view class="check-wz">自动会员减免与权益<view catchtap="tapTip1" class="img-ck">
                        <image src="/img/icon/zhuyi.png"></image>
                    </view>
                </view>
                <checkbox checked="{{isRelief}}" class="round sm zidy"></checkbox>
                <view class="popover-view one" wx:if="{{tipShow1}}">开通后车辆出场自动使用会员减免与权益</view>
            </view>
            <view bindtap="chooseJf" class="gouxuan">
                <view class="check-wz">自动抵扣积分</view>
                <checkbox checked="{{isJf}}" class="round sm zidy"></checkbox>
            </view>
        </block>
        <view bindtap="chooseDefault" class="gouxuan">
            <view class="check-wz">设置为默认车牌</view>
            <checkbox checked="{{isDefault}}" class="round sm zidy"></checkbox>
        </view>
    </view>
    <view class="weizhi padding flex flex-direction">
        <button bindtap="saveChepai" class="zdy lg">保存</button>
    </view>
    <view class="bottom-tip">提醒：若车辆非本人所有，可前往车牌管理解绑车牌</view>
</view>
<v-panel backgroundColor="white" binddelete="inputdelete" bindinputchange="inputChange" isShow="{{isShow}}" keyBoardType="{{keyBoardType}}" qty="{{plateNo.length}}"></v-panel>
