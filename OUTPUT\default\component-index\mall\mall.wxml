<view class="flex_row" wx:if="{{cs_id&&setting.dth_bg}}">
    <view bindtap="go_dth" class="dth" style="background:url('{{setting.dth_bg}}') no-repeat center;background-size: 100% 100%;" wx:if="{{setting.dth_bg}}">
        <view class="dth_text">大统华到家</view>
        <image class="dth_img" src="{{setting.dth_img}}"></image>
    </view>
    <view class="jxdiv" style="background: url('{{setting.jx_img}}') no-repeat center;background-size: 100% 100%;" wx:if="{{setting.jx_img}}">
        <view bindtap="go_jx">
            <view class="jx_text">会员商城</view>
        </view>
        <view class="jx_goods" wx:if="{{jxGoods.length>0}}">
            <view bindtap="go_detail" class="jx_dev" data-id="{{item.id}}" wx:for="{{jxGoods}}" wx:key="key">
                <image class="goods_item" src="{{item.cover_img}}"></image>
                <view class="jx_price">￥{{item.price}}</view>
            </view>
        </view>
    </view>
</view>
<view class="flex_row" wx:else>
    <view class="jxdiv1" style="background: url('{{setting.jx_img}}') no-repeat center;background-size: 100% 100%;" wx:if="{{setting.jx_img}}">
        <view bindtap="go_jx">
            <view class="jx_text">会员商城 </view>
        </view>
        <view class="jx_goods" wx:if="{{jxGoods.length>0}}">
            <view bindtap="go_detail" class="jx_dev" data-id="{{item.id}}" wx:for="{{jxGoods}}" wx:key="key">
                <image class="goods_item" src="{{item.cover_img}}"></image>
                <view class="jx_price">￥{{item.price}}</view>
            </view>
        </view>
    </view>
</view>
