@import "..\..\colorui.wxss";

page {
    background: #c8aa82
}

.container {
    padding-top: 42rpx
}

.main-wrapper {
    background: #fff;
    height: 760rpx;
    margin: 0 auto;
    position: relative;
    width: 672rpx
}

.hym {
    color: #c8aa82;
    font-size: 28rpx;
    left: 0;
    line-height: 38rpx;
    position: absolute;
    right: 0;
    text-align: center;
    top: 18rpx
}

.top-img {
    display: block;
    height: 58rpx;
    width: 672rpx
}

.canvas {
    height: 220px;
    margin: 76rpx auto 0;
    width: 220px
}

.tishi {
    margin-top: 40rpx;
    text-align: center
}

.screen-swiper {
    border-radius: 8rpx;
    height: 250rpx;
    margin: 34rpx auto 0;
    min-height: 250rpx;
    overflow: hidden;
    width: 710rpx
}

.screen-swiper wx-image {
    height: 100%;
    width: 100%
}

.window {
    display: flex;
    height: 400rpx;
    justify-content: space-between;
    margin: auto;
    width: 710rpx
}

.window-it {
    height: 300rpx;
    margin-top: 20rpx;
    width: 350rpx
}

.window-it:nth-child(2) {
    margin-left: 10rpx
}

.window-it>wx-image {
    height: 300rpx;
    width: 350rpx
}
