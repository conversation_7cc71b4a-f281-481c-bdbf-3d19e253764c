<view bindtap="toActivity" class="view-top" data-gameid="{{it.game_id}}" data-url="https://huadiu160467.ssl.minihaowan.com/web/game/game_id/{{it.game_id}}" wx:for="{{activitylist}}" wx:for-item="it">
    <view class="activityimage">
        <image src="{{it.imgurl}}"></image>
        <view class="activitytitle">{{it.title}}</view>
    </view>
    <view class="timeaddress">
        <view class="timeaddress-item">
            <text class="icon-timefill lg" style="color:#E2CEB3"></text>
            <text> 活动时间：{{it.start_time}} - {{it.end_time}}</text>
        </view>
        <view class="timeaddress-item">
            <text class="icon-locationfill lg" style="color:#E2CEB3"></text>
            <text> 游戏名称：{{it.name}}</text>
        </view>
    </view>
</view>
<view class="nodata" wx:if="{{activitylist.length==0}}">—————— 暂无数据 ——————</view>
