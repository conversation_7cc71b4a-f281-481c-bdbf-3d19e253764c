<view class="view-top">
    <view class="activityimage">
        <image src="{{activity.imgurl}}"></image>
        <view class="activitytitle">{{activity.title}}</view>
    </view>
    <view class="timeaddress">
        <view class="timeaddress-item">
            <text class="icon-timefill lg" style="color:#E2CEB3"></text>
            <text> 报名时间：{{activity.reportstarttime}} - {{activity.reportendtime}}</text>
        </view>
        <view class="timeaddress-item">
            <text class="icon-locationfill lg" style="color:#E2CEB3"></text>
            <text> 活动地点：{{activity.address}}</text>
        </view>
    </view>
</view>
<view class="view-center">
    <view class="content-title"> 活动详情 </view>
    <view class="content-body">
        <editor showImgResize showImgSize showImgToolbar id="editor" placeholder="abc" readOnly="true"></editor>
    </view>
</view>
<view class="view-center">
    <view class="content-title"> 活动规则 </view>
    <view> {{activity.rule}} </view>
</view>
<view class="blank-120"></view>
<view class="reportbar" wx:if="{{activityuser==null}}">
    <view class="reporttype">{{activity.paytype==1?activity.integralnum+'积分':'免费报名'}}</view>
    <view class="reportbtn" wx:if="{{isovertime}}">
        <button bindtap="report" class="bg-gold" disabled="true">报名已结束</button>
    </view>
    <view class="reportbtn" wx:else>
        <button bindtap="report" class="bg-gold" disabled="{{!canreport}}">{{!canreport?'报名名额已满':'我要报名'}}</button>
    </view>
</view>
<view class="reportbar" wx:else>
    <view class="reportbtn-c">
        <button bindtap="toticket" class="bg-gold ">查看报名信息</button>
    </view>
</view>
<view class="session-modal" wx:if="{{showModal==1}}">
    <view class="shadow"></view>
    <view class="modal">
        <view class="modeltitle">
            <view bindtap="cancel" class="left">取消</view>
            <view class="middle">请选择场次</view>
            <view bindtap="sure" class="right">确认</view>
        </view>
        <view class="modelcontent">
            <radio-group bindchange="radioChange">
                <view class="sessionitem" wx:for="{{activity.sessionList}}" wx:for-item="it">
                    <label>
                        <view class="item-name">
                            <view class="item-idx">场次{{it.idx+1}}：</view>
                            <view class="item-time">{{it.starttime}} - {{it.endtime}}</view>
                            <radio checked="" class="sm gold" disabled="{{it.limitnum-it.reportnum<=0}}" value="{{it.idx}}"></radio>
                        </view>
                    </label>
                    <view class="item-num">剩余名额：{{it.limitnum-it.reportnum}}名</view>
                </view>
            </radio-group>
        </view>
    </view>
</view>
<button bindtap="getPoster" class="swiper_fixed" data-type="{{hdlx}}" wx:if="{{isShowShare}}">
    <image class="goods_share" mode="widthFix" src="/yjh-config/images/goods_share.png"></image>
    <text class="goods_share_text">分享</text>
</button>
<view bindtap="tcodePop" class="mask" hidden="{{codePop}}"></view>
<view class="dialog" hidden="{{codePop}}">
    <view class="posi-rea">
        <image class="img" mode="widthFix" src="{{shareImg}}" style="max-width:100%;height:auto"></image>
    </view>
    <view class="dialog_bottom" hidden="{{codePop}}">
        <button class="share_btn" openType="share" style="display: inline">
            <image mode="widthFix" src="https://obs.springland.com.cn/obs-179f/huadi2/2020/10/1602490089777047.png"></image> 分享好友 </button>
        <button bindtap="save" class="share_btn" style="display: inline">
            <image mode="widthFix" src="/yjh-config/images/pyq.png"></image> 保存图片 </button>
    </view>
</view>
