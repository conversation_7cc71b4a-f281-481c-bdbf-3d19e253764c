<view bindtap="toActivity" class="view-top">
    <view class="activityimage">
        <image src="{{activity.imgurl}}"></image>
        <view class="activitytitle">{{activity.title}}</view>
    </view>
</view>
<view class="form">
    <view class="row" wx:if="{{activity.isreportmobile==1}}">
        <text decode="true" space="true">手机号&emsp;&emsp;</text>
        <text>{{activityuser.reportmobile}}</text>
    </view>
    <view class="row" wx:if="{{activity.isreportname==1}}">
        <text decode="true" space="true">姓&emsp;名&emsp;&emsp;</text>
        <text>{{activityuser.reportname}}</text>
    </view>
    <view class="row" wx:if="{{activity.isreportsex==1}}">
        <text decode="true" space="true">性&emsp;别&emsp;&emsp;</text>
        <text>{{activityuser.reportsex}}</text>
    </view>
    <view class="row" wx:if="{{activity.isreportage==1}}">
        <text decode="true" space="true">年&emsp;龄&emsp;&emsp;</text>
        <text>{{activityuser.reportage}}</text>
    </view>
    <view class="row" wx:if="{{activity.isreportnum==1}}">
        <text decode="true" space="true">参与人数&emsp;</text>
        <text>{{activityuser.reportnum}}</text>
    </view>
    <view class="row" wx:if="{{activityuser.ischeck==1}}">
        <text decode="true" space="true">验票时间&emsp;</text>
        <text>{{activityuser.checktime}}</text>
    </view>
</view>
<view class="qrcode" wx:if="{{activityuser.ischeck==null||activityuser.ischeck==0}}">
    <canvas bindlongtap="save" canvasId="canvas" class="canvas" style="width:{{width}}px;height:{{width}}px"></canvas>
</view>
