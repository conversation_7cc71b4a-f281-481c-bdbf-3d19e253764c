<view class="floor_center" hidden="{{!floor||floor.length==0}}">
    <view class="floor flex_col" hidden="{{item.is_show==0}}" wx:for="{{floor}}" wx:key="key">
        <navigator hoverClass="none" url="{{item.banner_link}}" wx:if="{{item.banner_link}}">
            <view>
                <image class="fimg" lazyLoad="true" mode="widthFix" src="{{item.banner}}"></image>
            </view>
        </navigator>
        <view wx:else>
            <image class="fimg" lazyLoad="true" mode="widthFix" src="{{item.banner}}"></image>
        </view>
        <view class="max202">
            <scroll-view>
                <view class="flex_row">
                    <view class="flex_col  f_goods_info" wx:for="{{item.goods_list}}" wx:for-item="goods" wx:key="key">
                        <navigator bindtap="we_click" class="we-prod-card" data-id="{{goods.detail_id}}" data-item="{{goods}}" data-scene="index_mall_api_item" hoverClass="none" url="/home/<USER>/goods-detail/index?goods_detail_id={{goods.detail_id}}">
                            <view class="f_goods_img flex_center">
                                <image lazyLoad="true" mode="widthFix" src="{{goods.cover_img}}"></image>
                                <image class="back_lt_img" lazyLoad="true" mode="widthFix" src="{{goods.back_img}}" wx:if="{{goods.back_img!=''}}"></image>
                            </view>
                            <view class="f_goods_name">
                                <text class="discount" wx:if="{{goods.zhekou}}">
                                    <text>{{goods.zhekou}}折</text>
                                </text> {{goods.name}}</view>
                            <view class="flex_row ">
                                <view class="f_price">￥{{goods.price}} <text style="font-size: 16rpx">到手价</text>
                                </view>
                                <view class="f_del_price">￥{{goods.market_price}}</view>
                            </view>
                        </navigator>
                    </view>
                    <navigator hoverClass="none" url="{{item.banner_link}}">
                        <view class="coupon_item flex_center">
                            <view class="coupon_item_1 flex_d" style="background: #f8f0e4;">
                                <view class="flex_center" style="width: 100%;">
                                    <view style="font-size: 26rpx;color: #382102;font-weight: 600;padding-top: 80rpx;">
                                        <view style="margin: auto;width: 100%;text-align: center;">
                                            <image src="https://obs.springland.com.cn/mg-mp/image/right-arrow-2.png" style="width: 50rpx;height: 50rpx;"></image>
                                        </view>
                                        <view style="padding-top:20rpx;line-height: 30px;letter-spacing: 2px;width: 100%;color: #ce9f73"> 查看更多 </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </navigator>
                </view>
            </scroll-view>
        </view>
    </view>
</view>
