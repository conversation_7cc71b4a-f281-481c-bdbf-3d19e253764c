<image class="img-bbb" src="https://obs.springland.com.cn/profile/upload/2021/05/08/84fd5d8f40e86ec0b4c9d04fafbd1a61.png"></image>
<view class="btn-wrapper">
    <button bindgetphonenumber="getphonenumber" class="yanse aaa" disabled="{{risk>0}}" openType="getPhoneNumber" wx:if="{{isXieyi}}">
        <image src="https://obs.springland.com.cn/profile/upload/2021/05/08/84fd5d8f40e86ec0b4c9d04fafbd1a61.png"></image>
        <text style="margin-left:20rpx;">手机一键登录</text>
    </button>
    <button bindtap="wxyjdl" class="yanse aaa wxyjdl-disabled" wx:else>
        <image src="https://obs.springland.com.cn/profile/upload/2021/05/08/84fd5d8f40e86ec0b4c9d04fafbd1a61.png"></image>
        <text style="margin-left:20rpx;">手机一键登录</text>
    </button>
    <view bindtap="goPhonePage" class="wenzi-shouji margin-tb {{risk>0?'text-grey':''}}">手机号快捷登录</view>
    <view class="margin-lr-xl" wx:if="{{validateShow}}">
        <move-verify bind:result="verifyResult"></move-verify>
    </view>
</view>
<view bindtap="chooseXieyi" class="xieyi">
    <checkbox checked="{{isXieyi}}" class="round sm zidy"></checkbox>我同意<text catchtap="xieyi1" class="link">《会员服务协议》</text>与<text catchtap="xieyi2" class="link">《隐私协议》</text>
</view>
<modal-box class="{{infoShow?'show':''}}">
    <dialog>
        <view class="modal-wrapper">
            <view class="tishi">提示</view>
            <view class="miaoshu solid-bottom">八佰伴智慧购申请获取您的昵称/头像/地区及性别</view>
            <button bindtap="getUserProfile" class="btn-bb">确认</button>
        </view>
    </dialog>
</modal-box>
<modal-box class="{{validateShow?'':''}}">
    <dialog>
        <view class="validate-wrapper">
            <view class="tishi">提示</view>
            <view class="miaoshu solid-bottom">请拖动滑块验证</view>
            <move-verify bind:result="verifyResult"></move-verify>
        </view>
    </dialog>
</modal-box>
<gift id="gift-id"></gift>
<movable-area class="movable-1" wx:if="{{testNetBtn}}">
    <movable-view bindchange="onChange" class="movable-2" damping="10000" direction="all" x="{{x}}" y="{{y}}">
        <button bindtap="testNet" style="width: 160rpx;height: 60rpx;padding: 0;background-color: #09da59!important;color: #fff;">测试网络</button>
    </movable-view>
</movable-area>
<view style="background-color: rgba(0, 0, 0, 0.815);width: 90%;position: fixed;top: 100rpx;left: 5%;" wx:if="{{testNet}}">
    <textarea style="width: 100%;height: 90%;color: #fff;padding: 10rpx 10rpx 10rpx 10rpx;"> {{testContent}} </textarea>
    <view bindtap="closeNetTap" style="line-height: 50rpx;width: 100%;height: 50rpx;text-align: center;color: #fff;">关闭</view>
</view>
<view bindtap="showTestBtn" style="width: 10rpx;height: 10rpx;position: fixed;right: 100rpx;top: 100rpx;"></view>
