/**
 * 测试MD5模式的Authorization生成
 * 发现：hyid的MD5前8位 = 1fb508f5 (匹配Authorization前8位)
 */

const crypto = require('crypto');

const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
const hyid = "820536796";
const mdid = "6021";

function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

function sha1(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

console.log("=== MD5模式Authorization验证 ===");
console.log("目标Authorization:", targetAuth);
console.log("HYID:", hyid);
console.log("MDID:", mdid);
console.log();

// 验证发现
console.log("--- 关键发现验证 ---");
const hyidMd5 = md5(hyid);
console.log(`HYID MD5: ${hyidMd5}`);
console.log(`前8位: ${hyidMd5.substring(0, 8)}`);
console.log(`目标前8位: ${targetAuth.substring(0, 8)}`);
console.log(`前8位匹配: ${hyidMd5.substring(0, 8) === targetAuth.substring(0, 8)}`);
console.log();

// 现在尝试生成后32位
console.log("--- 尝试生成后32位 ---");
const targetSuffix = targetAuth.substring(8); // a4e6557df6cb447aa314544f308a7a5d
console.log("目标后32位:", targetSuffix);
console.log();

// 测试各种组合来生成后32位
const testCombinations = [
    mdid,
    hyid + mdid,
    mdid + hyid,
    hyid + "_" + mdid,
    mdid + "_" + hyid,
    originalToken,
    originalToken + hyid,
    originalToken + mdid,
    originalToken + hyid + mdid,
    hyid + originalToken,
    mdid + originalToken,
    hyid + mdid + originalToken,
    // 尝试token的后32位
    originalToken.substring(8),
    originalToken.substring(8) + hyid,
    originalToken.substring(8) + mdid,
    originalToken.substring(8) + hyid + mdid,
    // 尝试其他组合
    "SPRINGLAND" + hyid + mdid,
    hyid + mdid + "springland*&^0627@",
    "SPRINGLAND" + hyid + mdid + "springland*&^0627@",
];

console.log("测试各种组合的MD5和SHA1:");
testCombinations.forEach((combo, i) => {
    const md5Result = md5(combo);
    const sha1Result = sha1(combo);
    
    // 检查MD5的前32位是否匹配
    const md5First32 = md5Result.substring(0, 32);
    const sha1First32 = sha1Result.substring(0, 32);
    
    if (md5First32 === targetSuffix) {
        console.log(`✓ MD5匹配! 组合${i+1}: "${combo}"`);
        console.log(`  完整Authorization: ${hyidMd5.substring(0, 8)}${md5First32}`);
    }
    
    if (sha1First32 === targetSuffix) {
        console.log(`✓ SHA1匹配! 组合${i+1}: "${combo}"`);
        console.log(`  完整Authorization: ${hyidMd5.substring(0, 8)}${sha1First32}`);
    }
    
    // 也检查完整的32位
    if (md5Result === targetSuffix) {
        console.log(`✓ MD5完整匹配! 组合${i+1}: "${combo}"`);
    }
    
    if (sha1Result === targetSuffix) {
        console.log(`✓ SHA1完整匹配! 组合${i+1}: "${combo}"`);
    }
});

console.log();

// 尝试更复杂的模式
console.log("--- 尝试复杂模式 ---");

// 模式1: 前8位(hyid的MD5) + 后32位(某种组合的MD5/SHA1)
console.log("模式: 前8位(hyid的MD5) + 后32位(组合的加密)");

// 尝试时间戳
const possibleTimestamps = [
    Math.floor(Date.now() / 1000), // 当前时间戳
    1722470400, // 2024-08-01
    1735689600, // 2025-01-01
];

possibleTimestamps.forEach(ts => {
    const timestampCombos = [
        ts.toString(),
        hyid + ts,
        mdid + ts,
        hyid + mdid + ts,
        ts + hyid + mdid,
    ];
    
    timestampCombos.forEach(combo => {
        const md5Result = md5(combo);
        const sha1Result = sha1(combo);
        
        if (md5Result.substring(0, 32) === targetSuffix) {
            console.log(`✓ 时间戳MD5匹配! 时间戳: ${ts}, 组合: "${combo}"`);
        }
        
        if (sha1Result.substring(0, 32) === targetSuffix) {
            console.log(`✓ 时间戳SHA1匹配! 时间戳: ${ts}, 组合: "${combo}"`);
        }
    });
});

console.log();
console.log("=== 总结 ===");
console.log("1. Authorization前8位 = MD5(hyid)的前8位");
console.log("2. 需要找到生成后32位的方法");
console.log("3. 后32位可能是某个参数组合的MD5或SHA1的前32位");
