# 八佰伴智慧购签到脚本

## 功能说明

本脚本用于自动执行八佰伴智慧购小程序的签到功能，支持多账号批量签到。

## 环境要求

- Node.js 12.0 或更高版本
- npm 包管理器

## 安装依赖

```bash
npm install
```

## 配置说明

### 环境变量配置

设置环境变量 `BBBZHG`，值为你的 hyid：

**单账号：**
```bash
export BBBZHG="820536796"
```

**多账号（用&分隔）：**
```bash
export BBBZHG="820536796&123456789&987654321"
```

### 参数说明

- `hyid`: 用户ID，从小程序抓包或登录接口获取
- `mdid`: 固定值 6021
- `Authorization`: 签名token，需要从登录接口获取

## 使用方法

### 方法一：直接运行
```bash
node 八佰伴智慧购.js
```

### 方法二：使用npm脚本
```bash
npm start
```

### 方法三：定时任务
可以配置crontab定时执行：
```bash
# 每天早上8点执行签到
0 8 * * * cd /path/to/script && export BBBZHG="你的hyid" && node 八佰伴智慧购.js
```

## 签名算法说明

通过分析签到.txt文件发现：

1. **登录接口返回的原始token**: `1fb508f5c099c83322274fe5a8ad1f46c7f5882a`
2. **签到请求的Authorization**: `1fb508f5a4e6557df6cb447aa314544f308a7a5d`

**Authorization是对原始token进行加密/签名处理的结果**，脚本提供了两种生成方式：

1. **基于Token的签名方式**：
   - 使用登录接口获取的原始token
   - 结合hyid、mdid等参数进行SHA1加密

2. **基于小程序源码的签名方式**：
   - 使用常量：`SPRINGLAND` 和 `springland*&^0627@`
   - 签名格式：`SHA1(SPRINGLAND + URL + 排序参数 + springland*&^0627@)`

## 接口信息

### 签到接口
- **URL**: `https://mp-gp.springland.com.cn/sign/sign`
- **方法**: POST
- **参数**: 
  - `hyid`: 用户ID
  - `mdid`: 固定值6021
- **响应格式**:
  ```json
  {
    "errCode": 0,
    "errMsg": "success", 
    "result": "5"
  }
  ```

### 登录接口
- **URL**: `https://mp-gp.springland.com.cn/login/getOpenidByCode/{code}/{appid}`
- **方法**: POST
- **用途**: 获取用户token和基本信息

## 注意事项

1. **Authorization生成**:
   - Authorization是对登录token进行签名加密的结果
   - 脚本会尝试多种签名算法来生成正确的Authorization
   - 如果签到失败，可能需要更新签名算法

2. **Token获取**:
   - 原始token从登录接口获取：`1fb508f5c099c83322274fe5a8ad1f46c7f5882a`
   - Authorization是签名结果：`1fb508f5a4e6557df6cb447aa314544f308a7a5d`
   - 建议通过抓包获取最新的token和Authorization对应关系

3. **请求频率**:
   - 脚本已内置1秒延迟，避免请求过快
   - 建议不要频繁执行，每天签到一次即可

4. **错误处理**:
   - 脚本包含完整的错误处理机制
   - 失败时会显示具体错误信息

5. **安全性**:
   - 请妥善保管你的hyid和token信息
   - 不要在公共场所分享包含敏感信息的日志

## 输出示例

```
🚀 八佰伴智慧购签到脚本启动...
📝 共找到 1 个账号

--- 处理第 1 个账号 ---
开始为账号 820536796 执行签到...
请求数据: {"hyid":"820536796","mdid":"6021"}
Authorization: 1fb508f5a4e6557df6cb447aa314544f308a7a5d
账号 820536796 签到响应: { errCode: 0, errMsg: 'success', result: '5' }
✅ 账号 820536796 签到成功！获得积分: 5

📊 签到结果汇总:
==================================================
账号1 (820536796): ✅ 成功 签到成功 (+5分)
==================================================
✅ 成功: 1/1
🎯 总积分: 5
🎉 签到完成！
```

## 故障排除

1. **环境变量未设置**:
   ```
   ❌ 请设置环境变量 BBBZHG
   ```
   解决：按照上述方法设置环境变量

2. **网络请求失败**:
   - 检查网络连接
   - 确认接口地址是否正确

3. **签到失败**:
   - 检查hyid是否正确
   - 确认Authorization token是否有效
   - 查看具体错误信息

## 更新日志

- v1.0.0: 初始版本，支持基本签到功能和多账号管理
