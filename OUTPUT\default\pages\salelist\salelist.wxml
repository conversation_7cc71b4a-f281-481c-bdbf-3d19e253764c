<view class="sale-list">
    <picker bindcancel="dateCancel" bindchange="dateChange" end="{{now}}" fields="month" mode="date" start="2000-01" value="{{date}}">
        <view class="picker padding-top">
            <text class="margin-left">选择月份：</text>
            <tag class="round">{{date}} <text class="icon-triangledownfill lg"></text>
            </tag>
        </view>
    </picker>
    <block wx:if="{{hasLogin}}">
        <view class="sale-item" wx:for="{{salelist}}" wx:for-item="it">
            <view bindtap="showSaleDetail" class="sale-content" data-mdid="{{it.mdid}}" data-xfjlid="{{it.xfjlid}}">
                <view class="sale-head">
                    <view class="sale-type">{{it.type}}</view>
                    <view class="sale-shop">{{it.mdmc}}</view>
                </view>
                <view class="sale-date"> 交易日期：{{it.jysj}} </view>
                <view class="sale-foot">
                    <view class="sale-price"> 总金额：{{it.zje}} </view>
                    <view class="sale-integral"> 本单积分：{{it.bdjf}} </view>
                </view>
            </view>
            <view class="sale-detail" wx:if="{{it.showDetail==1}}">
                <view class="detail-title">
                    <view class="l-cell title-name">商品</view>
                    <view class="m-cell title-num">数量</view>
                    <view class="r-cell title-price">金额</view>
                </view>
                <view class="detail-goods">
                    <view class="goods-item" wx:for="{{it.details}}" wx:for-item="goods">
                        <view class="l-cell body-name">{{goods.spmc}}</view>
                        <view class="m-cell body-num">{{goods.sl}}</view>
                        <view class="r-cell body-price">{{goods.je}}</view>
                    </view>
                </view>
            </view>
        </view>
        <view class="bottom">- 我是有底线的 -</view>
    </block>
    <login-com wx:else></login-com>
</view>
