<block wx:if="{{isShow==1}}">
    <view class="shadow"></view>
    <view class="modal">
        <image bindtap="cancel" src="/img/icon/<EMAIL>"></image>
        <view>
            <view class="title"> 确认支付 </view>
            <view class="price"> ￥{{sum}} </view>
            <view class="salename">
                <text>商品</text>
                <text>{{saleName}}</text>
            </view>
            <view class="zhanwei"></view>
            <view class="stepper">
                <text bindtap="bindMinus" class="{{num>1?'normal':'disable'}}" type="number">-</text>
                <input bindinput="bindManual" disabled="true" value="{{num}}"></input>
                <text bindtap="bindPlus" class="{{num<10&&num<canBuy?'normal':'disable'}}">+</text>
            </view>
            <button bindtap="onTap" class="submit" disabled="disabled" wx:if="{{disabled}}">立即支付</button>
            <user-info-btn hasPhone="{{hasPhone}}" wx:else>
                <button bindtap="onTap" class="submit">立即支付</button>
            </user-info-btn>
        </view>
    </view>
</block>
