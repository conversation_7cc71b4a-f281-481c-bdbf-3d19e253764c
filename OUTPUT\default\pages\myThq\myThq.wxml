<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{index==TabCur?'text-blue cur cust-color':''}}" data-id="{{index}}" wx:for="{{3}}"> {{titleList[index]}} </item>
    </view>
</scroll-view>
<view class="margin-cust" wx:if="{{hasLogin}}">
    <block wx:if="{{TabCur==0}}">
        <block wx:if="{{thqList&&thqList.length>0}}">
            <view bindtap="fireEvent" class="coupon-item" data-index="{{index}}" wx:for="{{thqList}}">
                <view class="xian">
                    <view class="left-wrapper">提货券</view>
                    <view class="right-wrapper">
                        <view class="coupon-title">《{{item.dept_name}}》提货券</view>
                        <view class="coupon-desc">小票编号：{{item.sktno}}{{item.jlbh}}</view>
                        <view class="coupon-range">线下提货专用券，请到对应厅房使用</view>
                        <view class="tap-kuoda">
                            <view class="shiyong">{{btnTextArray[TabCur]}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </block>
        <view class="no-data" wx:else>暂无记录</view>
    </block>
    <block wx:elif="{{TabCur==1}}">
        <block wx:if="{{yhxList&&yhxList.length>0}}">
            <view class="coupon-item" wx:for="{{yhxList}}">
                <view class="bg-white padding-sm">
                    <view class="aaa-title">{{item.name}}</view>
                    <view class="aaa-info">
                        <view>欠货数量</view>
                        <view>{{item.hxsl}}</view>
                    </view>
                    <view class="aaa-info">
                        <view>提货时间</view>
                        <view>{{item.hxsj}}</view>
                    </view>
                </view>
            </view>
        </block>
        <view class="no-data" wx:else>暂无记录</view>
    </block>
    <block wx:else>
        <block wx:if="{{yzfList&&yzfList.length>0}}">
            <view class="coupon-item" wx:for="{{yzfList}}">
                <view class="bg-white padding-sm">
                    <view class="aaa-title">{{item.name}}</view>
                    <view class="aaa-info">
                        <view>欠货数量</view>
                        <view>{{item.hxsl}}</view>
                    </view>
                    <view class="aaa-info">
                        <view>提货时间</view>
                        <view>{{item.hxsj}}</view>
                    </view>
                </view>
            </view>
        </block>
        <view class="no-data" wx:else>暂无记录</view>
    </block>
</view>
<login-com wx:else></login-com>
