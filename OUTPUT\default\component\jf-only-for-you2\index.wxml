<view class="other_content" style="margin-bottom:{{margin_bottom}};" wx:if="{{goods_list.length!=0}}">
    <view class="only_title">
        <image class="only_bg" mode="widthFix" src="{{banner_pic}}"></image>
    </view>
    <view class="mall_content">
        <view class="mall_con clearfix">
            <view class="mall_list" wx:for="{{goods_list}}" wx:key="key">
                <navigator bindtap="we_click" class="we-prod-card" data-id="{{item.id}}" data-item="{{item}}" data-scene="jf_recommend_api_item" url="/jf/pages/mallGoodsDetails/index?goods_detail_id={{item.id}}&base_id={{item.base_id}}&from_type={{item.from_type}}">
                    <view class="goods_img">
                        <image class="mall_img" src="{{item.cover_img}}"></image>
                        <image class="mall_img back_img" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                    </view>
                    <view class="mall_title">{{item.name}}</view>
                    <view class="mall_point">{{item.rule}}</view>
                    <view class="mall_num">已兑{{item.jf_sales}}件</view>
                </navigator>
            </view>
        </view>
    </view>
</view>
