<view class="top-block {{minbar==1?'minbar-color':''}}" style="width: 750rpx;height: {{startHeight}}px; {{minbar==1?'':''}}"></view>
<view style="width: 750rpx; height: {{startHeight}}px; " wx:if="{{minbar==1}}"></view>
<view style="min-height: 710rpx;">
    <view class="banner" wx:if="{{banner.length>0}}">
        <swiper autoplay="{{true}}" circular="true" class="banner-swiper" duration="{{1000}}" indicatorDots="{{true}}" interval="{{2000}}">
            <swiper-item class="banner-swiper-item" wx:for="{{banner}}" wx:key="key">
                <image bindtap="navPicClick" class="slide-image" data-id="{{item.id}}" data-is_article="{{item.is_article}}" data-is_outapp="{{item.is_outapp}}" data-out_appid="{{item.out_appid}}" data-out_link="{{item.out_link}}" data-url="{{item.url}}" lazyLoad="true" src="{{item.img_url}}"></image>
            </swiper-item>
        </swiper>
    </view>
</view>
<view class="mallandsearch {{minbar==1?'minbar-color':''}}" style="top:{{startHeight}}px;{{minbar==1?'position: fixed;':''}}">
    <view bindtap="goMallList" class="mall_select">
        <image class="big_new_icon" mode="widthFix" src="{{minbar==1?'/yjh-config/images/choose_icon.png':'/yjh-config/images/index_log.png'}}"></image>
        <view class="mall_name" style="{{minbar==1?'color:#505050;':''}}">{{mall_name}}</view>
    </view>
    <view class="search_bar">
        <navigator hoverClass="none" url="/home/<USER>/search/index">
            <input class="search_input"></input>
            <image class="new_seach_icon" mode="widthFix" src="/yjh-config/images/search.png"></image>
        </navigator>
    </view>
</view>
<scroll-view class="scroll-typelist" enhanced="{{true}}" scrollX="{{true}}" showScrollbar="{{false}}" style="top:{{startHeight+50}}px;" wx:if="{{goodsTypeOne.length>0}}">
    <view class="typelist">
        <view bindtap="tabCate" class="type" data-index="{{index}}" wx:for="{{goodsTypeOne}}" wx:key="key">{{item.name}}</view>
    </view>
</scroll-view>
<view class="alltype" style="top:{{startHeight+54}}px;">
    <image bindtap="tabCate" data-index="0" src="/img/indexnew/san.png"></image>
</view>
<view class="top-menu" style="top:{{startHeight+80}}px;" wx:if="{{navigations_top.length>0}}">
    <block wx:for="{{navigations_top}}">
        <view catchtap="nav_link_url" class="top-menu-item" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url+'?mdid='+mdid}}" wx:if="{{item.name==='更多分类'||item.name==='积分商城'}}">
            <image lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
            <view>{{item.name}}</view>
        </view>
        <view catchtap="nav_link_url" class="top-menu-item cate_list" data-is_gzh="{{item.is_gzh}}" data-item="{{item}}" data-out_link="{{item.out_link}}" data-url="{{item.link_url}}" wx:else>
            <image lazyLoad="true" mode="widthFix" src="{{item.img_url}}"></image>
            <view>{{item.name}}</view>
        </view>
    </block>
</view>
