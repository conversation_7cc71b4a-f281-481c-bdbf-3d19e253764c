page {
    background: #f1f1f1;
    color: #333;
    font-family: Helvetica Neue,Helvetica,sans-serif;
    font-size: 28rpx;
    line-height: 1
}

wx-bar,wx-button,wx-capsule,wx-chat,wx-custom,wx-form,wx-form-group,wx-image,wx-info,wx-input,wx-item,wx-label,wx-list,wx-navigator,wx-progress-bar,wx-scroll-view,wx-swiper,wx-tag,wx-text,wx-textarea,wx-timeline,wx-view {
    box-sizing: border-box
}

.flex {
    display: flex
}

.basis-xs {
    flex-basis: 20%
}

.basis-sm {
    flex-basis: 40%
}

.basis-df {
    flex-basis: 50%
}

.basis-lg {
    flex-basis: 60%
}

.basis-xl {
    flex-basis: 80%
}

.flex-sub {
    flex: 1
}

.flex-twice {
    flex: 2
}

.flex-treble {
    flex: 3
}

.flex-direction {
    flex-direction: column
}

.flex-wrap {
    flex-wrap: wrap
}

.align-start {
    align-items: flex-start
}

.align-end {
    align-items: flex-end
}

.align-center {
    align-items: center
}

.self-start {
    align-self: flex-start
}

.self-center {
    align-self: flex-center
}

.self-end {
    align-self: flex-end
}

.self-stretch {
    align-self: stretch
}

.align-stretch {
    align-items: stretch
}

.justify-start {
    justify-content: flex-start
}

.justify-end {
    justify-content: flex-end
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.justify-around {
    justify-content: space-around
}

.grid {
    display: flex;
    flex-wrap: wrap
}

.grid.grid-square {
    margin-bottom: -20rpx;
    overflow: hidden
}

.grid.grid-square wx-tag {
    border-bottom-left-radius: 6rpx;
    position: absolute;
    right: 0;
    top: 0
}

.grid.grid-square wx-item> wx-text[class*="icon"],.grid.grid-square wx-view> wx-text[class*="icon"] {
    align-items: center;
    bottom: 0;
    color: #aaa;
    display: flex;
    flex-direction: column;
    font-size: 52rpx;
    justify-content: center;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0
}

.grid.grid-square wx-item,.grid.grid-square wx-view {
    border-radius: 6rpx;
    margin-bottom: 20rpx;
    margin-right: 20rpx;
    overflow: hidden;
    position: relative
}

.grid.col-1.grid-square wx-item,.grid.col-1.grid-square wx-view {
    height: 0;
    margin-right: 0;
    padding-bottom: 100%
}

.grid.col-2.grid-square wx-item,.grid.col-2.grid-square wx-view {
    height: 0;
    padding-bottom: calc((100% - 20rpx)/2);
    width: calc((100% - 20rpx)/2)
}

.grid.col-2.grid-square wx-item:nth-child(2n),.grid.col-2.grid-square wx-view:nth-child(2n) {
    margin-right: 0
}

.grid.col-3.grid-square wx-item,.grid.col-3.grid-square wx-view {
    height: 0;
    padding-bottom: calc((100% - 40rpx)/3);
    width: calc((100% - 40rpx)/3)
}

.grid.col-3.grid-square wx-item:nth-child(3n),.grid.col-3.grid-square wx-view:nth-child(3n) {
    margin-right: 0
}

.grid.col-4.grid-square wx-item,.grid.col-4.grid-square wx-view {
    height: 0;
    padding-bottom: calc((100% - 60rpx)/4);
    width: calc((100% - 60rpx)/4)
}

.grid.col-4.grid-square wx-item:nth-child(4n),.grid.col-4.grid-square wx-view:nth-child(4n) {
    margin-right: 0
}

.grid.col-5.grid-square wx-item,.grid.col-5.grid-square wx-view {
    height: 0;
    padding-bottom: calc((100% - 80rpx)/5);
    width: calc((100% - 80rpx)/5)
}

.grid.col-1>wx-item,.grid.col-1>wx-view {
    width: 100%
}

.grid.col-2>wx-item,.grid.col-2>wx-view {
    width: 50%
}

.grid.col-3>wx-item,.grid.col-3>wx-view {
    width: 33.33%
}

.grid.col-4>wx-item,.grid.col-4>wx-view {
    width: 25%
}

.grid.col-5>wx-item,.grid.col-5>wx-view {
    width: 20%
}

.margin-0 {
    margin: 0!important
}

.margin-xs {
    margin: 10rpx
}

.margin-sm {
    margin: 20rpx
}

.margin {
    margin: 30rpx
}

.margin-lg {
    margin: 40rpx
}

.margin-xl {
    margin: 50rpx
}

.margin-top-xs {
    margin-top: 10rpx
}

.margin-top-sm {
    margin-top: 20rpx
}

.margin-top {
    margin-top: 30rpx
}

.margin-top-lg {
    margin-top: 40rpx
}

.margin-top-xl {
    margin-top: 50rpx
}

.margin-right-xs {
    margin-right: 10rpx
}

.margin-right-sm {
    margin-right: 20rpx
}

.margin-right {
    margin-right: 30rpx
}

.margin-right-lg {
    margin-right: 40rpx
}

.margin-right-xl {
    margin-right: 50rpx
}

.margin-bottom-xs {
    margin-bottom: 10rpx
}

.margin-bottom-sm {
    margin-bottom: 20rpx
}

.margin-bottom {
    margin-bottom: 30rpx
}

.margin-bottom-lg {
    margin-bottom: 40rpx
}

.margin-bottom-xl {
    margin-bottom: 50rpx
}

.margin-left-xs {
    margin-left: 10rpx
}

.margin-left-sm {
    margin-left: 20rpx
}

.margin-left {
    margin-left: 30rpx
}

.margin-left-lg {
    margin-left: 40rpx
}

.margin-left-xl {
    margin-left: 50rpx
}

.margin-lr-xs {
    margin-left: 10rpx;
    margin-right: 10rpx
}

.margin-lr-sm {
    margin-left: 20rpx;
    margin-right: 20rpx
}

.margin-lr {
    margin-left: 30rpx;
    margin-right: 30rpx
}

.margin-lr-lg {
    margin-left: 40rpx;
    margin-right: 40rpx
}

.margin-lr-xl {
    margin-left: 50rpx;
    margin-right: 50rpx
}

.margin-tb-xs {
    margin-bottom: 10rpx;
    margin-top: 10rpx
}

.margin-tb-sm {
    margin-bottom: 20rpx;
    margin-top: 20rpx
}

.margin-tb {
    margin-bottom: 30rpx;
    margin-top: 30rpx
}

.margin-tb-lg {
    margin-bottom: 40rpx;
    margin-top: 40rpx
}

.margin-tb-xl {
    margin-bottom: 50rpx;
    margin-top: 50rpx
}

.padding-0 {
    padding: 0!important
}

.padding-xs {
    padding: 10rpx
}

.padding-sm {
    padding: 20rpx
}

.padding {
    padding: 30rpx
}

.padding-lg {
    padding: 40rpx
}

.padding-xl {
    padding: 50rpx
}

.padding-top-xs {
    padding-top: 10rpx
}

.padding-top-sm {
    padding-top: 20rpx
}

.padding-top {
    padding-top: 30rpx
}

.padding-top-lg {
    padding-top: 40rpx
}

.padding-top-xl {
    padding-top: 50rpx
}

.padding-right-xs {
    padding-right: 10rpx
}

.padding-right-sm {
    padding-right: 20rpx
}

.padding-right {
    padding-right: 30rpx
}

.padding-right-lg {
    padding-right: 40rpx
}

.padding-right-xl {
    padding-right: 50rpx
}

.padding-bottom-xs {
    padding-bottom: 10rpx
}

.padding-bottom-sm {
    padding-bottom: 20rpx
}

.padding-bottom {
    padding-bottom: 30rpx
}

.padding-bottom-lg {
    padding-bottom: 40rpx
}

.padding-bottom-xl {
    padding-bottom: 50rpx
}

.padding-left-xs {
    padding-left: 10rpx
}

.padding-left-sm {
    padding-left: 20rpx
}

.padding-left {
    padding-left: 30rpx
}

.padding-left-lg {
    padding-left: 40rpx
}

.padding-left-xl {
    padding-left: 50rpx
}

.padding-lr-xs {
    padding-left: 10rpx;
    padding-right: 10rpx
}

.padding-lr-sm {
    padding-left: 20rpx;
    padding-right: 20rpx
}

.padding-lr {
    padding-left: 30rpx;
    padding-right: 30rpx
}

.padding-lr-lg {
    padding-left: 40rpx;
    padding-right: 40rpx
}

.padding-lr-xl {
    padding-left: 50rpx;
    padding-right: 50rpx
}

.padding-tb-xs {
    padding-bottom: 10rpx;
    padding-top: 10rpx
}

.padding-tb-sm {
    padding-bottom: 20rpx;
    padding-top: 20rpx
}

.padding-tb {
    padding-bottom: 30rpx;
    padding-top: 30rpx
}

.padding-tb-lg {
    padding-bottom: 40rpx;
    padding-top: 40rpx
}

.padding-tb-xl {
    padding-bottom: 50rpx;
    padding-top: 50rpx
}

.cf::after,.cf::before {
    content: " ";
    display: table
}

.cf::after {
    clear: both
}

.fl {
    float: left
}

.fr {
    float: right
}

wx-image {
    display: inline-block;
    max-width: 100%;
    position: relative;
    z-index: 0
}

wx-image.loading::before {
    background: #f5f5f5;
    content: "";
    display: block;
    height: 100%;
    position: absolute;
    width: 100%;
    z-index: -2
}

wx-image.loading::after {
    -webkit-animation: icon-spin 2s linear infinite;
    animation: icon-spin 2s linear infinite;
    bottom: 0;
    color: #ccc;
    content: "\e7f1";
    display: block;
    font-family: iconfont;
    font-size: 32rpx;
    height: 32rpx;
    left: 0;
    line-height: 32rpx;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
    width: 32rpx;
    z-index: -1
}

wx-image.response {
    width: 100%
}

wx-checkbox,wx-radio,wx-switch {
    position: relative
}

wx-switch::after,wx-switch::before {
    color: #fff;
    content: "\e645";
    font-family: iconfont!important;
    font-size: 32rpx;
    left: 0rpx;
    line-height: 64rpx;
    pointer-events: none;
    position: absolute;
    text-align: center;
    top: 0;
    transform: scale(0,0);
    transition: all .3s ease-in-out 0s;
    width: 64rpx;
    z-index: 9
}

wx-switch::before {
    content: "\e646";
    left: auto;
    right: 0
}

wx-switch::before,wx-switch[checked]::after {
    transform: scale(1,1)
}

wx-checkbox::before,wx-radio::before {
    color: #fff;
    content: "\e645";
    font-family: iconfont!important;
    font-size: 32rpx;
    line-height: 32rpx;
    margin-top: -16rpx;
    pointer-events: none;
    position: absolute;
    right: 10rpx;
    top: 50%;
    transform: scale(1,1);
    transition: all .3s ease-in-out 0s;
    z-index: 9
}

wx-switch[checked]::before {
    transform: scale(0,0)
}

wx-switch .wx-switch-input {
    background: #aaa!important;
    border: none;
    border-radius: 100rpx;
    height: 64rpx;
    margin: 0;
    padding: 0 60rpx
}

wx-switch .wx-switch-input::after {
    border-radius: 100rpx;
    bottom: 0rpx!important;
    height: 64rpx!important;
    left: 0rpx!important;
    margin: auto!important;
    position: absolute;
    top: 0rpx!important;
    transform: scale(.9,.9)!important;
    transition: all .1s ease-in-out 0s;
    width: 64rpx!important
}

wx-switch .wx-switch-input-checked::after {
    box-shadow: none!important;
    left: 57rpx!important;
    margin: auto!important
}

wx-radio-group {
    display: inline-block
}

wx-checkbox .wx-checkbox-input,wx-radio .wx-radio-input {
    height: 48rpx;
    margin: 0;
    width: 48rpx
}

wx-checkbox.round .wx-checkbox-input {
    border-radius: 100rpx
}

wx-switch.radius .wx-switch-input,wx-switch.radius .wx-switch-input::after,wx-switch.radius .wx-switch-input::before {
    border-radius: 10rpx
}

wx-checkbox .wx-checkbox-input::before,wx-radio .wx-radio-input::before,wx-radio.radio::before,wx-switch .wx-switch-input::before {
    display: none
}

wx-radio.radio[checked]::after {
    background: transparent;
    border: 16rpx solid #fff;
    border-radius: 200rpx;
    bottom: 0;
    content: "";
    display: block;
    height: 16rpx;
    left: 0rpx;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0rpx;
    width: 16rpx;
    z-index: 999
}

wx-checkbox.sm,wx-radio.sm,wx-switch.sm {
    transform: scale(.8)
}

.switch-sex::after {
    content: "\e71c"
}

.switch-sex::before {
    content: "\e71a"
}

.switch-sex .wx-switch-input {
    background: #e54d42!important;
    border-color: #e54d42
}

.switch-sex[checked] .wx-switch-input {
    background: #0081ff!important;
    border-color: #0081ff!important
}

.line-red::after,.lines-red::after,wx-checkbox.red[checked] .wx-checkbox-input,wx-radio.red[checked] .wx-radio-input,wx-switch.red[checked] .wx-switch-input {
    border-color: #e54d42!important
}

.line-orange::after,.lines-orange::after,wx-checkbox.orange[checked] .wx-checkbox-input,wx-radio.orange[checked] .wx-radio-input,wx-switch.orange[checked] .wx-switch-input {
    border-color: #f37b1d!important
}

.line-yellow::after,.lines-yellow::after,wx-checkbox.yellow[checked] .wx-checkbox-input,wx-radio.yellow[checked] .wx-radio-input,wx-switch.yellow[checked] .wx-switch-input {
    border-color: #fbbd08!important
}

.line-olive::after,.lines-olive::after,wx-checkbox.olive[checked] .wx-checkbox-input,wx-radio.olive[checked] .wx-radio-input,wx-switch.olive[checked] .wx-switch-input {
    border-color: #8dc63f!important
}

.line-green::after,.lines-green::after,wx-checkbox.green[checked] .wx-checkbox-input,wx-checkbox[checked] .wx-checkbox-input,wx-radio.green[checked] .wx-radio-input,wx-switch.green[checked] .wx-switch-input {
    border-color: #39b54a!important
}

.line-cyan::after,.lines-cyan::after,wx-checkbox.cyan[checked] .wx-checkbox-input,wx-radio.cyan[checked] .wx-radio-input,wx-switch.cyan[checked] .wx-switch-input {
    border-color: #1cbbb4!important
}

.line-blue::after,.lines-blue::after,wx-checkbox.blue[checked] .wx-checkbox-input,wx-radio.blue[checked] .wx-radio-input,wx-switch.blue[checked] .wx-switch-input {
    border-color: #0081ff!important
}

.line-purple::after,.lines-purple::after,wx-checkbox.purple[checked] .wx-checkbox-input,wx-radio.purple[checked] .wx-radio-input,wx-switch.purple[checked] .wx-switch-input {
    border-color: #6739b6!important
}

.line-mauve::after,.lines-mauve::after,wx-checkbox.mauve[checked] .wx-checkbox-input,wx-radio.mauve[checked] .wx-radio-input,wx-switch.mauve[checked] .wx-switch-input {
    border-color: #9c26b0!important
}

.line-pink::after,.lines-pink::after,wx-checkbox.pink[checked] .wx-checkbox-input,wx-radio.pink[checked] .wx-radio-input,wx-switch.pink[checked] .wx-switch-input {
    border-color: #e03997!important
}

.line-brown::after,.lines-brown::after,wx-checkbox.brown[checked] .wx-checkbox-input,wx-radio.brown[checked] .wx-radio-input,wx-switch.brown[checked] .wx-switch-input {
    border-color: #a5673f!important
}

.line-grey::after,.lines-grey::after,wx-checkbox.grey[checked] .wx-checkbox-input,wx-radio.grey[checked] .wx-radio-input,wx-switch.grey[checked] .wx-switch-input {
    border-color: #8799a3!important
}

.line-gray::after,.lines-gray::after,wx-checkbox.gray[checked] .wx-checkbox-input,wx-radio.gray[checked] .wx-radio-input,wx-switch.gray[checked] .wx-switch-input {
    border-color: #aaa!important
}

.line-black::after,.lines-black::after,wx-checkbox.black[checked] .wx-checkbox-input,wx-radio.black[checked] .wx-radio-input,wx-switch.black[checked] .wx-switch-input {
    border-color: #333!important
}

.line-white::after,.lines-white::after,wx-checkbox.white[checked] .wx-checkbox-input,wx-radio.white[checked] .wx-radio-input,wx-switch.white[checked] .wx-switch-input {
    border-color: #fff!important
}

.line-gold::after,.lines-gold::after,wx-checkbox.gold[checked] .wx-checkbox-input,wx-radio.gold[checked] .wx-radio-input,wx-switch.gold[checked] .wx-switch-input {
    border-color: #c8aa82!important
}

.bg-red,wx-checkbox.red[checked] .wx-checkbox-input,wx-radio.red[checked] .wx-radio-input,wx-switch.red[checked] .wx-switch-input {
    background-color: #e54d42!important;
    color: #fff!important
}

.bg-orange,wx-checkbox.orange[checked] .wx-checkbox-input,wx-radio.orange[checked] .wx-radio-input,wx-switch.orange[checked] .wx-switch-input {
    background-color: #f37b1d!important;
    color: #fff!important
}

.bg-yellow,wx-checkbox.yellow[checked] .wx-checkbox-input,wx-radio.yellow[checked] .wx-radio-input,wx-switch.yellow[checked] .wx-switch-input {
    background-color: #fbbd08!important;
    color: #333!important
}

.bg-olive,wx-checkbox.olive[checked] .wx-checkbox-input,wx-radio.olive[checked] .wx-radio-input,wx-switch.olive[checked] .wx-switch-input {
    background-color: #8dc63f!important;
    color: #fff!important
}

.bg-green,wx-checkbox.green[checked] .wx-checkbox-input,wx-checkbox[checked] .wx-checkbox-input,wx-radio.green[checked] .wx-radio-input,wx-radio[checked] .wx-radio-input,wx-switch.green[checked] .wx-switch-input,wx-switch[checked] .wx-switch-input {
    background-color: #39b54a!important;
    color: #fff!important
}

.bg-cyan,wx-checkbox.cyan[checked] .wx-checkbox-input,wx-radio.cyan[checked] .wx-radio-input,wx-switch.cyan[checked] .wx-switch-input {
    background-color: #1cbbb4!important;
    color: #fff!important
}

.bg-blue,wx-checkbox.blue[checked] .wx-checkbox-input,wx-radio.blue[checked] .wx-radio-input,wx-switch.blue[checked] .wx-switch-input {
    background-color: #0081ff!important;
    color: #fff!important
}

.bg-purple,wx-checkbox.purple[checked] .wx-checkbox-input,wx-radio.purple[checked] .wx-radio-input,wx-switch.purple[checked] .wx-switch-input {
    background-color: #6739b6!important;
    color: #fff!important
}

.bg-mauve,wx-checkbox.mauve[checked] .wx-checkbox-input,wx-radio.mauve[checked] .wx-radio-input,wx-switch.mauve[checked] .wx-switch-input {
    background-color: #9c26b0!important;
    color: #fff!important
}

.bg-pink,wx-checkbox.pink[checked] .wx-checkbox-input,wx-radio.pink[checked] .wx-radio-input,wx-switch.pink[checked] .wx-switch-input {
    background-color: #e03997!important;
    color: #fff!important
}

.bg-brown,wx-checkbox.brown[checked] .wx-checkbox-input,wx-radio.brown[checked] .wx-radio-input,wx-switch.brown[checked] .wx-switch-input {
    background-color: #a5673f!important;
    color: #fff!important
}

.bg-gold,wx-checkbox.gold[checked] .wx-checkbox-input,wx-radio.gold[checked] .wx-radio-input,wx-switch.gold[checked] .wx-switch-input {
    background-color: #c8aa82!important;
    color: #fff!important
}

.bg-grey,wx-checkbox.grey[checked] .wx-checkbox-input,wx-radio.grey[checked] .wx-radio-input,wx-switch.grey[checked] .wx-switch-input {
    background-color: #8799a3!important;
    color: #fff!important
}

.bg-gray,wx-checkbox.gray[checked] .wx-checkbox-input,wx-radio.gray[checked] .wx-radio-input,wx-switch.gray[checked] .wx-switch-input {
    background-color: #f0f0f0!important;
    color: #666!important
}

.bg-black,wx-checkbox.black[checked] .wx-checkbox-input,wx-radio.black[checked] .wx-radio-input,wx-switch.black[checked] .wx-switch-input {
    background-color: #333!important;
    color: #fff!important
}

.bg-white,wx-checkbox.white[checked] .wx-checkbox-input,wx-radio.white[checked] .wx-radio-input,wx-switch.white[checked] .wx-switch-input {
    background-color: #fff!important;
    color: #666
}

.bg-shadeTop {
    background-image: linear-gradient(#000,rgba(0,0,0,.01));
    color: #fff
}

.bg-shadeBottom {
    background-image: linear-gradient(rgba(0,0,0,.01),#000);
    color: #fff
}

.line-white,.lines-white,.none-bg {
    background-color: initial!important
}

.bg-red.light {
    background: #fadbd9!important;
    color: #e54d42!important
}

.bg-orange.light {
    background: #fde6d2!important;
    color: #f37b1d!important
}

.bg-yellow.light {
    background: #fef2ce!important;
    color: #fbbd08!important
}

.bg-olive.light {
    background: #e8f4d9!important;
    color: #8dc63f!important
}

.bg-green.light {
    background: #d7f0db!important;
    color: #39b54a!important
}

.bg-cyan.light {
    background: #d2f1f0!important;
    color: #1cbbb4!important
}

.bg-blue.light {
    background: #cce6ff!important;
    color: #0081ff!important
}

.bg-purple.light {
    background: #e1d7f0!important;
    color: #6739b6!important
}

.bg-mauve.light {
    background: #ebd4ef!important;
    color: #9c26b0!important
}

.bg-pink.light {
    background: #f9d7ea!important;
    color: #e03997!important
}

.bg-brown.light {
    background: #ede1d9!important;
    color: #a5673f!important
}

.bg-grey.light {
    background: #e7ebed!important;
    color: #8799a3!important
}

.bg-gray.light {
    background: #fadbd9!important;
    background: #f1f1f1!important;
    color: #666!important;
    color: #888!important
}

.gradual-red {
    background-image: linear-gradient(45deg,#f43f3b,#ec008c)!important;
    color: #fff!important
}

.gradual-orange {
    background-image: linear-gradient(45deg,#ff9700,#ed1c24)!important;
    color: #fff!important
}

.gradual-green {
    background-image: linear-gradient(45deg,#39b54a,#8dc63f)!important;
    color: #fff!important
}

.gradual-purple {
    background-image: linear-gradient(45deg,#9000ff,#5e00ff)!important;
    color: #fff!important
}

.gradual-pink {
    background-image: linear-gradient(45deg,#ec008c,#6739b6)!important;
    color: #fff!important
}

.gradual-blue {
    background-image: linear-gradient(45deg,#0081ff,#1cbbb4)!important;
    color: #fff!important
}

wx-button.shadow[class*="-red"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(204,69,59,.2)!important
}

wx-button.shadow[class*="-orange"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(217,109,26,.2)!important
}

wx-button.shadow[class*="-yellow"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(224,170,7,.2)!important
}

wx-button.shadow[class*="-olive"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(124,173,55,.2)!important
}

wx-button.shadow[class*="-green"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(48,156,63,.2)!important
}

wx-button.shadow[class*="-cyan"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(28,187,180,.2)!important
}

wx-button.shadow[class*="-blue"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(0,102,204,.2)!important
}

wx-button.shadow[class*="-purple"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(88,48,156,.2)!important
}

wx-button.shadow[class*="-mauve"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(133,33,150,.2)!important
}

wx-button.shadow[class*="-pink"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(199,50,134,.2)!important
}

wx-button.shadow[class*="-brown"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(140,88,53,.2)!important
}

wx-button.shadow[class*="-gray"],wx-button.shadow[class*="-grey"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(114,130,138,.2)!important
}

wx-button.shadow[class*="-black"] {
    box-shadow: 6rpx 6rpx 8rpx rgba(26,26,26,.2)!important
}

.bg-img {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover
}

.bg-mask {
    background-color: #333;
    position: relative
}

.bg-mask::after {
    background-color: rgba(0,0,0,.5);
    border-radius: inherit;
    bottom: 0;
    content: "";
    display: block;
    height: 100%;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%
}

.bg-mask wx-cover-view,.bg-mask wx-view {
    position: relative;
    z-index: 5
}

.bg-mask>wx-cover-view {
    background-color: rgba(0,0,0,.5)
}

.bg-video {
    position: relative
}

.bg-video wx-video {
    display: block;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    pointer-events: none;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: 0
}

.text-xs {
    font-size: 20rpx
}

.text-sm {
    font-size: 24rpx
}

.text-df {
    font-size: 28rpx
}

.text-lg {
    font-size: 32rpx
}

.text-xl {
    font-size: 36rpx
}

.text-xxl {
    font-size: 44rpx
}

.text-sl {
    font-size: 80rpx
}

.text-xsl {
    font-size: 120rpx
}

.text-Abc {
    text-transform: Capitalize
}

.text-ABC {
    text-transform: Uppercase
}

.text-abc {
    text-transform: Lowercase
}

.text-price::before {
    content: "¥";
    font-size: 80%;
    margin-right: 4rpx
}

.text-cut {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-bold {
    font-weight: 700
}

.text-center {
    text-align: center
}

.text-content {
    line-height: 1.6
}

.text-left {
    text-align: left
}

.text-right {
    text-align: right
}

.line-red,.lines-red,.text-red {
    color: #e54d42!important
}

.line-orange,.lines-orange,.text-orange {
    color: #f37b1d!important
}

.line-yellow,.lines-yellow,.text-yellow {
    color: #fbbd08!important
}

.line-olive,.lines-olive,.text-olive {
    color: #8dc63f!important
}

.line-green,.lines-green,.text-green {
    color: #39b54a!important
}

.line-cyan,.lines-cyan,.text-cyan {
    color: #1cbbb4!important
}

.line-blue,.lines-blue,.text-blue {
    color: #0081ff!important
}

.line-purple,.lines-purple,.text-purple {
    color: #6739b6!important
}

.line-mauve,.lines-mauve,.text-mauve {
    color: #9c26b0!important
}

.line-pink,.lines-pink,.text-pink {
    color: #e03997!important
}

.line-brown,.lines-brown,.text-brown {
    color: #a5673f!important
}

.line-grey,.lines-grey,.text-grey {
    color: #8799a3!important
}

.line-gray,.lines-gray,.text-gray {
    color: #aaa!important
}

.line-black,.lines-black,.text-black {
    color: #333!important
}

.line-white,.lines-white,.text-white {
    color: #fff!important
}

.line-gold,.lines-gold,.text-gold {
    color: #c8aa82!important
}

.dashed,.dashed-bottom,.dashed-left,.dashed-right,.dashed-top,.solid,.solid-bottom,.solid-left,.solid-right,.solid-top,.solids,.solids-bottom,.solids-left,.solids-right,.solids-top {
    position: relative
}

.dashed-bottom::after,.dashed-left::after,.dashed-right::after,.dashed-top::after,.dashed::after,.solid-bottom::after,.solid-left::after,.solid-right::after,.solid-top::after,.solid::after,.solids-bottom::after,.solids-left::after,.solids-right::after,.solids-top::after,.solids::after {
    border-radius: inherit;
    box-sizing: border-box;
    content: " ";
    height: 200%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transform: scale(.5);
    transform-origin: 0 0;
    width: 200%
}

.solid::after {
    border: 1rpx solid rgba(0,0,0,.1)
}

.solid-top::after {
    border-top: 1rpx solid rgba(0,0,0,.1)
}

.solid-right::after {
    border-right: 1rpx solid rgba(0,0,0,.1)
}

.solid-bottom::after {
    border-bottom: 1rpx solid rgba(0,0,0,.1)
}

.solid-left::after {
    border-left: 1rpx solid rgba(0,0,0,.1)
}

.solids::after {
    border: 4rpx solid #eee
}

.solids-top::after {
    border-top: 4rpx solid #eee
}

.solids-right::after {
    border-right: 4rpx solid #eee
}

.solids-bottom::after {
    border-bottom: 4rpx solid #eee
}

.solids-left::after {
    border-left: 4rpx solid #eee
}

.dashed::after {
    border: 1rpx dashed #ddd
}

.dashed-top::after {
    border-top: 1rpx dashed #ddd
}

.dashed-right::after {
    border-right: 1rpx dashed #ddd
}

.dashed-bottom::after {
    border-bottom: 1rpx dashed #ddd
}

.dashed-left::after {
    border-left: 1rpx dashed #ddd
}

.shadow {
    box-shadow: 0 1rpx 6rpx rgba(0,0,0,.1)
}

.shadow-lg {
    box-shadow: 0rpx 40rpx 100rpx 0rpx rgba(0,0,0,.07)
}

.shadow-warp {
    box-shadow: 0 0 10rpx rgba(0,0,0,.1);
    position: relative
}

.shadow-warp:after,.shadow-warp:before {
    bottom: 30rpx;
    box-shadow: 0 30rpx 20rpx rgba(0,0,0,.2);
    content: "";
    left: 20rpx;
    position: absolute;
    top: 20rpx;
    transform: rotate(-3deg);
    width: 50%;
    z-index: -1
}

.shadow-warp:after {
    left: auto;
    right: 20rpx;
    transform: rotate(3deg)
}

.shadow-blur {
    position: relative
}

.shadow-blur::before {
    background: inherit;
    border-radius: inherit;
    content: "";
    display: block;
    filter: blur(10rpx);
    height: 100%;
    left: 10rpx;
    opacity: .4;
    position: absolute;
    top: 10rpx;
    transform: scale(1,1);
    transform-origin: 0 0;
    width: 100%;
    z-index: -1
}

.round,wx-button.text {
    border-radius: 5000rpx!important
}

.radius {
    border-radius: 6rpx!important
}

wx-button {
    align-items: center;
    background-color: #fff!important;
    border-radius: 6rpx;
    box-sizing: border-box;
    color: #666;
    display: inline-flex;
    font-size: 28rpx;
    justify-content: center;
    line-height: 1;
    margin-left: 0;
    margin-right: 0;
    overflow: visible;
    padding: 20rpx 30rpx 16rpx;
    position: relative;
    text-align: center;
    text-decoration: none;
    transform: translate(0rpx,0rpx)
}

wx-button::after,wx-tag[class*="line-"]::after {
    border: 1rpx solid rgba(0,0,0,.2);
    border-radius: inherit;
    box-sizing: border-box;
    content: " ";
    height: 200%;
    left: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    transform: scale(.5);
    transform-origin: 0 0;
    width: 200%;
    z-index: 1
}

wx-button[class*="line"]::after,wx-tag.radius[class*="line"]::after {
    border-radius: 12rpx
}

wx-button.round[class*="line"]::after,wx-tag.round[class*="line"]::after {
    border-radius: 1000rpx
}

wx-button[class*="lines"]::after {
    border: 6rpx solid rgba(0,0,0,.2)
}

wx-button[class*="bg-"]::after {
    display: none
}

wx-button.sm {
    font-size: 24rpx;
    padding: 14rpx 20rpx 10rpx
}

wx-button.lg {
    font-size: 32rpx;
    padding: 32rpx 40rpx 28rpx
}

wx-button.icon.sm {
    height: 56rpx;
    width: 56rpx
}

wx-button.text {
    height: 70rpx;
    padding: 0;
    width: 70rpx
}

wx-button.icon.lg {
    height: 80rpx;
    width: 80rpx
}

wx-button.shadow-blur::before {
    filter: blur(6rpx);
    left: 4rpx;
    opacity: .6;
    top: 4rpx
}

wx-button.button-hover {
    transform: translate(1rpx,1rpx)
}

.block {
    display: block
}

wx-button.block {
    display: flex
}

wx-button[disabled] {
    color: #fff;
    opacity: .6
}

wx-tag {
    align-items: stretch;
    background: #fff;
    box-sizing: border-box;
    color: #666;
    display: inline-flex;
    font-family: Helvetica Neue,Helvetica,sans-serif;
    font-size: 24rpx;
    justify-content: center;
    line-height: 1;
    padding: 12rpx 14rpx 10rpx;
    position: relative;
    vertical-align: middle
}

wx-tag[class*="line-"]::after {
    border-radius: 0
}

wx-tag+wx-tag {
    margin-left: 10rpx
}

wx-tag.sm {
    font-size: 20rpx;
    padding: 10rpx 12rpx 6rpx
}

wx-capsule {
    display: inline-flex;
    vertical-align: middle
}

wx-capsule + wx-capsule {
    margin-left: 10rpx
}

wx-capsule wx-tag {
    margin: 0
}

wx-capsule wx-tag[class*="line-"]:last-child::after {
    border-left: 0rpx solid transparent!important
}

wx-capsule wx-tag[class*="line-"]:first-child::after {
    border-right: 0rpx solid transparent!important
}

wx-capsule.radius wx-tag:first-child {
    border-bottom-left-radius: 6rpx;
    border-top-left-radius: 6rpx
}

wx-capsule.radius wx-tag:last-child::after,wx-capsule.radius wx-tag[class*="line-"] {
    border-bottom-right-radius: 12rpx;
    border-top-right-radius: 12rpx
}

wx-capsule.round wx-tag:first-child {
    border-bottom-left-radius: 200rpx;
    border-top-left-radius: 200rpx;
    text-indent: 4rpx
}

wx-capsule.round wx-tag:last-child,wx-capsule.round wx-tag:last-child::after {
    border-bottom-right-radius: 200rpx;
    border-top-right-radius: 200rpx;
    text-indent: -4rpx
}

wx-tag.badge {
    background: #dd514c;
    border-radius: 200rpx;
    color: #fff;
    font-size: 20rpx;
    padding: 6rpx 10rpx 4rpx;
    position: absolute;
    right: -10rpx;
    top: -10rpx
}

wx-tag:empty {
    padding: 8rpx;
    right: -4rpx;
    top: -4rpx
}

wx-avatar {
    background: #ccc;
    background-position: 50%;
    background-size: cover;
    color: #fff;
    display: inline-block;
    font-size: 0rpx;
    font-variant: small-caps;
    height: 64rpx;
    line-height: 64rpx;
    margin: 0;
    padding: 0;
    position: relative;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    width: 64rpx
}

wx-avatar wx-text::first-letter,wx-avatar::first-letter {
    font-size: 40rpx
}

wx-avatar wx-text {
    display: inline-block;
    font-size: inherit;
    left: 50%;
    position: absolute;
    transform: scale(1) translateX(-50%);
    transform-origin: 0 center
}

wx-avatar.sm {
    height: 48rpx;
    line-height: 48rpx;
    width: 48rpx
}

wx-avatar.sm wx-text::first-letter,wx-avatar.sm::first-letter {
    font-size: 30rpx
}

wx-avatar > wx-text[class*="icon"] {
    bottom: 0;
    left: 50%;
    margin: auto;
    position: absolute;
    top: 0;
    transform: scale(1.2) translateX(-50%);
    transform-origin: 0 center
}

wx-avatar.sm > wx-text[class*="icon"] {
    transform: scale(.75) translateX(-50%)
}

wx-avatar.lg > wx-text[class*="icon"] {
    transform: scale(1.75) translateX(-50%)
}

wx-avatar.xl > wx-text[class*="icon"] {
    transform: scale(2.2) translateX(-50%)
}

wx-avatar.lg {
    font-size: 22rpx;
    height: 90rpx;
    line-height: 90rpx;
    width: 90rpx
}

wx-avatar.lg wx-text::first-letter,wx-avatar.lg::first-letter {
    font-size: 36rpx
}

wx-avatar.xl {
    font-size: 24rpx;
    height: 128rpx;
    line-height: 128rpx;
    width: 128rpx
}

wx-avatar.xl wx-text::first-letter,wx-avatar.xl::first-letter {
    font-size: 40rpx
}

wx-avatar-group {
    direction: rtl;
    display: inline-block;
    padding: 0 10rpx 0 40rpx;
    unicode-bidi: bidi-override
}

wx-avatar-group wx-avatar {
    border: 4rpx solid #f1f1f1;
    margin-left: -30rpx;
    vertical-align: middle
}

wx-avatar-group wx-avatar.sm {
    border: 1rpx solid #f1f1f1;
    margin-left: -20rpx
}

wx-progress-bar {
    align-items: center;
    background-color: #ebeef5;
    display: inline-flex;
    height: 28rpx;
    overflow: hidden;
    width: 100%
}

wx-progress-bar+wx-text,wx-progress-bar+wx-view {
    line-height: 1
}

wx-progress-bar.xs {
    height: 10rpx
}

wx-progress-bar.sm {
    height: 20rpx
}

wx-progress-bar wx-view {
    background: #0081ff;
    color: #fff;
    height: 100%;
    justify-content: space-around;
    justify-items: flex-end;
    transition: width .6s ease;
    width: 0
}

wx-progress-bar wx-text,wx-progress-bar wx-view {
    align-items: center;
    display: flex;
    font-size: 20rpx
}

wx-progress-bar wx-text {
    color: #666;
    text-indent: 10rpx
}

wx-progress-bar.text-progress {
    padding-right: 60rpx
}

wx-progress-bar.striped wx-view {
    background-image: linear-gradient(45deg,hsla(0,0%,100%,.15) 25%,transparent 0,transparent 50%,hsla(0,0%,100%,.15) 0,hsla(0,0%,100%,.15) 75%,transparent 0,transparent);
    background-size: 72rpx 72rpx
}

wx-progress-bar.active wx-view {
    animation: progress-bar-stripes 2s linear infinite
}

@keyframes progress-bar-stripes {
    from {
        background-position: 72rpx 0
    }

    to {
        background-position: 0 0
    }
}

wx-load {
    display: block;
    line-height: 3em;
    text-align: center
}

wx-load::before {
    display: inline-block;
    font-family: iconfont!important;
    margin-right: 6rpx
}

wx-load.loading::before {
    animation: icon-spin 2s linear infinite;
    content: "\e67a"
}

wx-load.loading::after {
    content: "加载中..."
}

wx-load.over::before {
    content: "\e64a"
}

wx-load.over::after {
    content: "没有更多了"
}

wx-load.erro::before {
    content: "\e658"
}

wx-load.erro::after {
    content: "加载失败"
}

wx-load.load-icon::before {
    font-size: 32rpx
}

wx-load.load-icon.over,wx-load.load-icon::after {
    display: none
}

wx-load.load-modal {
    align-items: center;
    background: #fff;
    border-radius: 10rpx;
    bottom: 140rpx;
    box-shadow: 0 0 0rpx 2000rpx rgba(0,0,0,.5);
    display: flex;
    flex-direction: column;
    font-size: 28rpx;
    height: 260rpx;
    justify-content: center;
    left: 0;
    line-height: 2.4em;
    margin: auto;
    position: fixed;
    right: 0;
    top: 0;
    width: 260rpx;
    z-index: 9999
}

wx-load.load-modal [class*="icon"] {
    font-size: 60rpx
}

wx-load.load-modal wx-image {
    height: 70rpx;
    width: 70rpx
}

wx-load.load-modal::after {
    animation: icon-spin 1s linear infinite;
    background: #fff;
    border: 6rpx solid rgba(0,0,0,.05);
    border-left-color: #f37b1d;
    border-radius: 50%;
    content: "";
    font-size: 10px;
    height: 200rpx;
    position: absolute;
    width: 200rpx;
    z-index: -1
}

.load-progress {
    left: 0;
    pointer-events: none;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 2000
}

.load-progress.hide {
    display: none
}

.load-progress .load-progress-bar {
    height: 4rpx;
    overflow: hidden;
    position: relative;
    transition: all .2s ease 0s;
    width: 100%
}

.load-progress .load-progress-spinner {
    display: block;
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    z-index: 2000
}

.load-progress .load-progress-spinner::after {
    -webkit-animation: load-progress-spinner .4s linear infinite;
    animation: load-progress-spinner .4s linear infinite;
    border: 4rpx solid transparent;
    border-left-color: inherit;
    border-radius: 50%;
    border-top-color: inherit;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    content: "";
    display: block;
    height: 24rpx;
    width: 24rpx
}

@-webkit-keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes load-progress-spinner {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.grayscale {
    filter: grayscale(1)
}

wx-list.menu {
    background: #fff;
    display: block;
    padding: 0 30rpx
}

wx-list.menu.no-padding {
    padding: 0
}

wx-list+wx-list,wx-list.menu+wx-list.menu {
    margin-top: 30rpx
}

wx-list.menu>wx-item {
    align-items: center;
    border-bottom: 1rpx solid #eee;
    display: flex;
    justify-content: space-between;
    min-height: 100rpx;
    position: relative
}

wx-list.menu>wx-item.cur {
    background-color: #fcf7e9
}

wx-list.menu>wx-item:last-child {
    border: none
}

wx-list.menu.no-padding>wx-item {
    padding: 30rpx
}

wx-list.menu-avatar.no-padding>wx-item {
    padding-left: 140rpx
}

wx-list.menu-avatar.no-padding>wx-item wx-avatar {
    left: 30rpx
}

wx-list.menu.no-padding>wx-item.arrow {
    padding-right: 66rpx
}

wx-list.menu>wx-item .content {
    flex: 1;
    font-size: 30rpx;
    line-height: 1.6em
}

wx-list.menu>wx-item wx-button.content {
    justify-content: flex-start;
    padding: 0
}

wx-list.menu>wx-item wx-button.content::after {
    display: none
}

wx-list.menu>wx-item .content>wx-text[class*="icon"] {
    display: inline-block;
    margin-right: 10rpx;
    text-align: center;
    width: 1.6em
}

wx-list.menu>wx-item .content>wx-image {
    display: inline-block;
    height: 1.6em;
    margin-right: 10rpx;
    vertical-align: middle;
    width: 1.6em
}

wx-list.menu>wx-item .action {
    text-align: right
}

wx-list>wx-item.grayscale {
    background-color: #f5f5f5
}

wx-list.menu>wx-item .action wx-tag:empty {
    right: 10rpx
}

wx-list.menu>wx-item.arrow {
    padding-right: 36rpx
}

wx-list.menu>wx-item.arrow::after {
    bottom: 0;
    color: #aaa;
    content: "\e6a3";
    display: block;
    font-family: iconfont!important;
    font-size: 34rpx;
    height: 30rpx;
    line-height: 30rpx;
    margin: auto;
    position: absolute;
    right: 0;
    text-align: center;
    top: 1rpx;
    width: 30rpx
}

wx-list.menu.no-padding>wx-item.arrow::after {
    right: 30rpx
}

wx-list.menu>wx-item wx-avatar-group wx-avatar {
    border-color: #fff
}

wx-list.card-menu {
    border-radius: 20rpx;
    margin-left: 30rpx;
    margin-right: 30rpx;
    overflow: hidden
}

wx-list.menu-avatar>wx-item>wx-avatar {
    left: 0;
    position: absolute
}

wx-list.menu-avatar>wx-item {
    height: 140rpx;
    padding-left: 110rpx
}

wx-list.menu>wx-item .content wx-tag.sm {
    font-size: 16rpx;
    line-height: 80%;
    margin-top: -6rpx;
    padding: 8rpx 6rpx 4rpx
}

wx-list.grid {
    background: #fff;
    text-align: center
}

wx-list.grid>wx-item {
    border-bottom: 1rpx solid #eee;
    border-right: 1rpx solid #eee;
    display: flex;
    flex-direction: column;
    padding: 20rpx
}

wx-list.grid>wx-item wx-text[class*="icon"] {
    display: block;
    font-size: 48rpx;
    margin-top: 20rpx;
    position: relative;
    width: 100%
}

wx-list.grid>wx-item wx-text {
    color: #888;
    display: block;
    font-size: 26rpx;
    line-height: 40rpx;
    margin-top: 10rpx
}

wx-list.grid>wx-item wx-tag {
    left: 50%;
    margin-left: 20rpx;
    right: auto
}

wx-list.grid.col-3>wx-item:nth-child(3n),wx-list.grid.col-4>wx-item:nth-child(4n),wx-list.grid.col-5>wx-item:nth-child(5n) {
    border-right: 0rpx
}

wx-list.grid.no-border {
    padding: 20rpx 10rpx
}

wx-list.grid.no-border>wx-item {
    border: none!important;
    padding-bottom: 10rpx;
    padding-top: 10rpx
}

wx-list.menu-avatar.comment > wx-item {
    height: auto;
    padding-bottom: 30rpx;
    padding-left: 90rpx;
    padding-top: 30rpx
}

wx-list.menu-avatar.comment wx-avatar {
    align-self: flex-start
}

wx-bar {
    background: #fff;
    height: 100rpx;
    justify-content: space-between;
    position: relative
}

wx-bar,wx-bar .action {
    align-items: center;
    display: flex
}

wx-bar .action {
    height: 100%;
    justify-content: center;
    max-width: 100%
}

wx-bar .action:first-child {
    font-size: 30rpx;
    margin-left: 30rpx
}

wx-bar .action wx-text.text-cut {
    text-align: left;
    width: 100%
}

wx-bar wx-avatar:first-child {
    margin-left: 20rpx
}

wx-bar .action:first-child >wx-text[class*="icon"] {
    margin-left: -.3em;
    margin-right: .3em
}

wx-bar .action:last-child {
    margin-right: 30rpx
}

wx-bar .action>wx-text[class*="icon"] {
    font-size: 36rpx
}

wx-bar .action>wx-text[class*="icon"]::before {
    vertical-align: .1em
}

wx-bar .action>wx-text[class*="icon"]+wx-text[class*="icon"] {
    margin-left: .5em
}

wx-bar .content {
    bottom: 16rpx;
    cursor: none;
    font-size: 36rpx;
    height: 60rpx;
    left: 0;
    line-height: 60rpx;
    margin: auto;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    right: 0;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 400rpx
}

wx-bar.btn-group {
    justify-content: space-around
}

wx-bar.btn-group wx-button {
    flex: 1;
    margin: 0 20rpx;
    max-width: 50%;
    padding: 20rpx 32rpx
}

wx-bar .search-form {
    align-items: center;
    background: #f5f5f5;
    color: #666;
    display: flex;
    flex: 1;
    font-size: 24rpx;
    height: 64rpx;
    line-height: 64rpx;
    margin: 0 20rpx
}

wx-bar .search-form +.action {
    margin-right: 20rpx
}

wx-bar .search-form wx-input {
    flex: 1;
    font-size: 26rpx;
    height: 128rpx;
    line-height: 128rpx;
    padding-right: 20rpx
}

wx-bar .search-form [class*="icon"] {
    margin: 0 .5em
}

wx-bar .search-form.round [class*="icon"] {
    margin-left: .5em
}

wx-bar .search-form [class*="icon"]::before {
    top: 0rpx
}

.nav.fixed,wx-bar.fixed {
    box-shadow: 0 1rpx 6rpx rgba(0,0,0,.1);
    top: 0
}

.nav.fixed,wx-bar.fixed,wx-bar.foot {
    position: fixed;
    width: 100%;
    z-index: 1024
}

wx-bar.foot {
    bottom: 0;
    box-shadow: 0 -1rpx 6rpx rgba(0,0,0,.1)
}

wx-bar.shop {
    padding: 0
}

wx-bar.shop .action {
    display: block;
    flex: 1;
    font-size: 24rpx;
    height: auto!important;
    line-height: 1;
    margin: 0!important;
    padding: 0 20rpx;
    position: relative;
    text-align: center
}

wx-bar.shop wx-button.action::after {
    border: 0
}

wx-bar.shop [class*="icon"] {
    display: block;
    height: auto!important;
    margin: 0 auto 10rpx!important;
    position: relative;
    text-align: center;
    width: 100rpx!important
}

wx-bar.shop .submit {
    align-items: center;
    display: flex;
    flex: 2;
    height: 100%;
    justify-content: center;
    position: relative;
    text-align: center
}

wx-bar.shop .submit:last-child {
    flex: 2.6
}

wx-bar.shop .submit+.submit {
    flex: 2
}

wx-bar.shop .submit wx-button {
    margin-left: 20rpx
}

wx-bar.shop .submit:last-child wx-button {
    margin-left: 0rpx
}

wx-bar.shop .submit+.submit wx-button {
    margin-left: 0rpx;
    margin-right: 20rpx
}

wx-bar.shop .action::after {
    border-right: 1rpx solid rgba(0,0,0,.1)!important;
    content: " ";
    height: 200%;
    left: 0;
    position: absolute;
    top: 0;
    transform: scale(.5);
    transform-origin: 0 0;
    width: 200%
}

wx-bar.input {
    padding-right: 20rpx
}

wx-bar.input wx-input {
    flex: 1;
    font-size: 30rpx;
    height: 64rpx;
    line-height: 64rpx;
    margin: 0 20rpx;
    min-height: 64rpx;
    overflow: initial
}

wx-bar.input .action {
    margin-left: 20rpx
}

wx-bar.input .action [class*="icon"] {
    font-size: 48rpx
}

wx-bar.input wx-input+.action {
    margin-left: 0rpx;
    margin-right: 20rpx
}

wx-bar.input .action:first-child [class*="icon"] {
    margin-left: 0rpx
}

wx-custom {
    display: block;
    position: relative
}

wx-custom wx-bar {
    box-shadow: 0rpx 0rpx 0rpx!important;
    padding-right: 220rpx
}

.nav {
    white-space: nowrap
}

::-webkit-scrollbar {
    display: none
}

.nav wx-item {
    display: inline-block;
    height: 90rpx;
    line-height: 90rpx;
    margin: 0 10rpx;
    padding: 0 20rpx
}

.nav wx-item.cur {
    border-bottom: 4rpx solid
}

wx-timeline {
    background: #fff;
    display: block
}

wx-timeline wx-time {
    color: #888;
    display: block;
    font-size: 26rpx;
    padding: 20rpx 0;
    text-align: center;
    width: 120rpx
}

wx-timeline>wx-item {
    color: #ccc;
    display: block;
    padding: 30rpx 30rpx 30rpx 120rpx;
    position: relative;
    z-index: 0
}

wx-timeline>wx-item::after {
    background: #ddd;
    content: "";
    display: block;
    height: 100%;
    left: 60rpx;
    position: absolute;
    top: 0;
    width: 1rpx;
    z-index: 8
}

wx-timeline>wx-item::before {
    content: "\e763";
    display: block;
    font-family: iconfont;
    position: absolute;
    top: 36rpx;
    z-index: 9
}

wx-timeline>wx-item::before,wx-timeline>wx-item[class*="icon"]::before {
    background: #fff;
    border: none;
    height: 50rpx;
    left: 36rpx;
    line-height: 50rpx;
    text-align: center;
    width: 50rpx
}

wx-timeline>wx-item>.content {
    background: #f1f1f1;
    border-radius: 6rpx;
    color: #666;
    display: block;
    line-height: 1.6;
    padding: 30rpx
}

wx-timeline>wx-item>.content+.content {
    margin-top: 20rpx
}

wx-chat {
    display: flex;
    flex-direction: column
}

wx-chat wx-item {
    display: flex;
    padding: 30rpx 30rpx 70rpx;
    position: relative
}

wx-chat wx-item>wx-avatar {
    height: 80rpx;
    width: 80rpx
}

wx-chat wx-item>.main {
    align-items: center;
    display: flex;
    margin: 0 40rpx;
    max-width: calc(100% - 260rpx)
}

wx-chat wx-item>wx-image {
    height: 320rpx
}

wx-chat wx-item>.main .content {
    align-items: center;
    background: #fff;
    border-radius: 6rpx;
    color: #666;
    display: inline-flex;
    font-size: 30rpx;
    line-height: 40rpx;
    max-width: 100%;
    min-height: 80rpx;
    padding: 20rpx;
    position: relative;
    text-align: left
}

wx-chat wx-item .date {
    bottom: 20rpx;
    color: #aaa;
    font-size: 24rpx;
    left: 160rpx;
    position: absolute;
    width: calc(100% - 320rpx)
}

wx-chat wx-item .action {
    align-items: center;
    display: flex;
    padding: 0 30rpx
}

wx-chat wx-item>.main .content::after {
    border-color: transparent currentcolor transparent #fff;
    border-style: solid dotted solid solid;
    border-width: 16rpx 0 16rpx 16rpx;
    content: "";
    display: inline-block;
    height: 0;
    left: -14rpx;
    overflow: hidden;
    position: absolute;
    right: auto;
    top: 24rpx;
    transform: rotate(180deg);
    vertical-align: middle;
    width: 0;
    z-index: 100
}

wx-chat wx-item.self {
    justify-content: flex-end;
    text-align: right
}

wx-chat wx-item.self>.main .content::after {
    border-left: 0 dotted;
    border-right: 16rpx solid #fff;
    left: auto;
    right: -14rpx
}

wx-chat wx-item.self>.main .bg-green.content::after {
    border-right-color: #39b50a
}

wx-chat wx-info {
    background-color: rgba(0,0,0,.2);
    border-radius: 6rpx;
    color: #fff;
    display: inline-block;
    font-size: 24rpx;
    line-height: 1.4;
    margin: 20rpx auto;
    max-width: 400rpx;
    padding: 8rpx 12rpx
}

wx-card,wx-card > wx-item {
    display: block;
    overflow: hidden
}

wx-card > wx-item {
    background: #fff;
    border-radius: 10rpx;
    margin: 30rpx
}

wx-card > wx-item.shadow-blur {
    overflow: initial
}

wx-card.no-card > wx-item {
    border-radius: 0rpx;
    margin: 0rpx
}

wx-card.case .image {
    position: relative
}

wx-card.case .image wx-image {
    width: 100%
}

wx-card.case .image wx-tag {
    position: absolute;
    right: 0;
    top: 0
}

wx-card.case .image wx-bar {
    word-wrap: normal;
    background-color: initial;
    bottom: 0;
    padding: 0rpx 30rpx;
    position: absolute;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

wx-card.case.no-card .image {
    border-radius: 10rpx;
    margin: 30rpx 30rpx 0;
    overflow: hidden
}

wx-card.dynamic {
    display: block
}

wx-card.dynamic>wx-item {
    background-color: #fff;
    display: block;
    overflow: hidden
}

wx-card.dynamic>wx-item > .text-content {
    font-size: 30rpx;
    margin-bottom: 20rpx;
    max-height: 6.4em;
    overflow: hidden;
    padding: 0 30rpx
}

wx-card.dynamic>wx-item .square-img {
    border-radius: 6rpx;
    height: 200rpx;
    width: 100%
}

wx-card.dynamic>wx-item .only-img {
    border-radius: 6rpx;
    height: 320rpx;
    width: 100%
}

wx-card.article {
    display: block
}

wx-card.article>wx-item {
    padding-bottom: 30rpx
}

wx-card.article>wx-item .title {
    word-wrap: normal;
    color: #333;
    font-size: 30rpx;
    font-weight: 900;
    line-height: 100rpx;
    padding: 0 30rpx;
    text-overflow: ellipsis;
    white-space: nowrap
}

wx-card.article>wx-item .content {
    display: flex;
    padding: 0 30rpx
}

wx-card.article>wx-item .content > wx-image {
    border-radius: 6rpx;
    height: 6.4em;
    margin-right: 20rpx;
    width: 240rpx
}

wx-card.article>wx-item .content .desc {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between
}

wx-card.article>wx-item .content .text-content {
    color: #888;
    font-size: 28rpx;
    height: 4.8em;
    overflow: hidden
}

wx-form-group {
    align-items: center;
    background: #fff;
    display: flex;
    justify-content: space-between;
    min-height: 100rpx;
    padding: 0 30rpx
}

wx-form-group + wx-form-group {
    border-top: 1rpx solid #eee
}

wx-form-group .title {
    font-size: 30rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding-right: 30rpx;
    position: relative;
    text-align: justify
}

wx-form-group.top {
    align-items: baseline
}

wx-form-group wx-input {
    color: #555;
    flex: 1;
    font-size: 30rpx;
    padding-right: 20rpx
}

wx-form-group > wx-text[class*="icon"] {
    box-sizing: border-box;
    font-size: 36rpx;
    padding: 0
}

wx-form-group wx-textarea,wx-form-group wx-textarea wx-textarea {
    box-sizing: initial;
    display: inline-block;
    flex: 1;
    font-size: 28rpx;
    height: 4.8em;
    line-height: 1.2em;
    margin: 32rpx 0 30rpx;
    padding: 0;
    vertical-align: top;
    width: 100%
}

wx-form-group wx-textarea::after {
    content: "测试文字";
    opacity: 0
}

wx-form-group .grid-square {
    margin: 30rpx 0 0!important
}

wx-form-group wx-picker {
    flex: 1;
    overflow: hidden;
    padding-right: 40rpx;
    position: relative
}

wx-form-group wx-picker .picker {
    font-size: 28rpx;
    line-height: 100rpx;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

wx-form-group wx-picker::after {
    bottom: 0;
    color: #aaa;
    content: "\e6a3";
    display: block;
    font-family: iconfont!important;
    font-size: 34rpx;
    line-height: 100rpx;
    margin: auto;
    position: absolute;
    right: -20rpx;
    text-align: center;
    top: 0;
    width: 60rpx
}

wx-form-group wx-textarea[disabled],wx-form-group wx-textarea[disabled] .placeholder {
    color: transparent
}

wx-modal-box {
    backface-visibility: hidden;
    background: rgba(0,0,0,.6);
    bottom: 0;
    left: 0;
    opacity: 0;
    outline: 0;
    perspective: 2000rpx;
    pointer-events: none;
    position: fixed;
    right: 0;
    text-align: center;
    top: 0;
    -ms-transform: scale(1.185);
    transform: scale(1.185);
    transition: all .6s ease-in-out 0;
    z-index: 1110
}

wx-modal-box::before {
    content: "\200B";
    display: inline-block;
    height: 100%;
    vertical-align: middle
}

wx-modal-box.show {
    opacity: 1;
    overflow-x: hidden;
    overflow-y: auto;
    pointer-events: auto;
    -ms-transform: scale(1);
    transform: scale(1);
    transition-duration: .3s
}

wx-dialog {
    background: #f8f8f8;
    border-radius: 10rpx;
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
    max-width: 100%;
    overflow: hidden;
    position: relative;
    vertical-align: middle;
    width: 680rpx
}

wx-modal-box.bottom-modal::before {
    vertical-align: bottom
}

wx-modal-box.bottom-modal wx-dialog {
    border-radius: 0;
    width: 100%
}

wx-modal-box.bottom-modal {
    margin-bottom: -1000rpx
}

wx-modal-box.bottom-modal.show {
    margin-bottom: 0
}

wx-swiper.square-dot .wx-swiper-dot {
    background: #fff;
    border-radius: 20rpx!important;
    height: 10rpx!important;
    opacity: .4;
    transition: all .3s ease-in-out 0s!important;
    width: 10rpx!important
}

wx-swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active {
    opacity: 1;
    width: 30rpx!important
}

wx-swiper.round-dot .wx-swiper-dot {
    height: 10rpx!important;
    position: relative;
    top: -4rpx!important;
    transition: all .3s ease-in-out 0s!important;
    width: 10rpx!important
}

wx-swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after {
    background: #fff;
    border-radius: 20rpx;
    bottom: 0;
    content: "";
    height: 10rpx;
    left: 0rpx;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0rpx;
    width: 10rpx
}

wx-swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active {
    height: 18rpx!important;
    top: 0rpx!important;
    width: 18rpx!important
}

.screen-swiper {
    min-height: 375rpx
}

.screen-swiper wx-image {
    display: block;
    height: 100%;
    margin: 0;
    width: 100%
}

.card-swiper {
    height: 420rpx
}

.card-swiper wx-swiper-item {
    box-sizing: border-box;
    left: 70rpx!important;
    overflow: initial!important;
    padding: 40rpx 0rpx 70rpx;
    width: 610rpx!important
}

.card-swiper wx-swiper-item .bg-img {
    border-radius: 10rpx;
    display: block;
    height: 100%;
    transform: scale(.9);
    transition: all .2s ease-in 0s;
    width: 100%
}

.card-swiper wx-swiper-item.cur .bg-img {
    transform: none;
    transition: all .2s ease-in 0s
}

.tower-swiper {
    height: 420rpx;
    position: relative
}

.tower-swiper .tower-item {
    bottom: 0;
    height: 380rpx;
    left: 50%;
    margin: auto;
    opacity: 1;
    position: absolute;
    top: 0;
    transition: all .3s ease-in 0s;
    width: 300rpx
}

.tower-swiper .tower-item.none {
    opacity: 0
}

.tower-swiper .tower-item .bg-img {
    border-radius: 6rpx;
    height: 100%;
    width: 100%
}

.kuoda::after {
    bottom: -20rpx;
    content: "";
    left: -20rpx;
    position: absolute;
    right: -20rpx;
    top: -20rpx
}

.sp-desption {
    word-wrap: break-word;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    color: #999;
    display: -webkit-box;
    font-size: 22rpx;
    line-height: 30rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal!important
}

@import "hide-btn.wxss";

@import "icon.wxss";