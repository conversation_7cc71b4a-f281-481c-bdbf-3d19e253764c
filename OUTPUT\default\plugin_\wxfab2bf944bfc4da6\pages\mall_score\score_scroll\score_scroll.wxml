<view class="score-scroll-box">
    <view class="score-scroll-box-num" wx:if="{{score>=0}}">
        <view class="score-item-box score-item-{{scoreSplitArr[scoreItem]}}" style="transform: translateY(-{{scoreSplitArr[scoreItem]*28}}px); transition-delay: {{scoreItem*50}}ms;" wx:for="{{scoreSplitArr.length}}" wx:for-index="scoreIndex" wx:for-item="scoreItem" wx:key="scoreIndex">
            <text class="score-num-text score-num-{{scoreNum}}" wx:for="{{10}}" wx:for-item="scoreNum" wx:key="scoreNum">{{scoreNum}}</text>
        </view>
    </view>
    <view wx:else>-</view>
</view>
