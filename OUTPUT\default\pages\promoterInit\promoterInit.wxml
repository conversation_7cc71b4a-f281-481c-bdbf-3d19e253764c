<form>
    <form-group>
        <view class="title">多列选择</view>
        <picker bindchange="MultiChange" bindcolumnchange="MultiColumnChange" mode="multiSelector" range="{{multiArray}}" rangeKey="bmmc" value="{{multiIndex}}">
            <view class="picker"> {{multiArray[0][ multiIndex[0] ].bmmc}}，{{multiArray[1][ multiIndex[1] ].bmmc}} <block wx:if="{{multiArray[2].length>0}}"> ，{{multiArray[2][ multiIndex[2] ].bmmc}} </block>
            </view>
        </picker>
    </form-group>
    <view class="padding flex flex-direction">
        <button bindtap="tapSure" class="bg-green lg" disabled="{{btnDisabled}}">确定</button>
    </view>
</form>
