<view class="top">
    <view bindtap="taptype" class="top-item {{type=='calendar'?'status-active':''}}" data-type="calendar">活动日历</view>
    <view bindtap="taptype" class="top-item {{type=='list'?'status-active':''}}" data-type="list">活动列表</view>
</view>
<calendar bind:checkall="checkall" bind:clear="clear" bind:getdate="getdate" bind:select="cmfclick" id="calendar" isOpen="{{true}}" lockDay="{{lockday}}" selected="{{selectedDays}}" wx:if="{{type=='calendar'}}"></calendar>
<view class="activity-status" wx:if="{{type=='list'}}">
    <view bindtap="tapstatus" class="status-item {{status==0?'status-active':''}}" data-status="0">全部</view>
    <view bindtap="tapstatus" class="status-item {{status==1?'status-active':''}}" data-status="1">报名中</view>
    <view bindtap="tapstatus" class="status-item {{status==2?'status-active':''}}" data-status="2">已结束</view>
</view>
<activitylist datestr="{{datestr}}" mdid="{{mdid}}" status="{{status}}" type="{{type}}"></activitylist>
<view style="height:100rpx;"></view>
<view bindtap="taprecord" class="record">报名记录</view>
