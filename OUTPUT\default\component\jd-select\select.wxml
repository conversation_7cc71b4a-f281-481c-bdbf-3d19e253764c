<view class="jd_box" wx:if="{{current.show}}">
    <view class="jd_select grey" wx:if="{{hadregion==true}}">
        <button bindtap="addrAdd" class="jd_add_btn">选择其他地址</button>
        <view class="jd_add_show">
            <scroll-view class="jd_scroll-c" scrollY="true">
                <view bindtap="pick" class="jd_add_list {{pickId==item.id?'jd_pick':''}}" data-id="{{item.id}}" wx:for="{{addrlist}}" wx:key="key">
                    <view class="jd_add_title" data-id="{{item.id}}">{{item.consignee}}<text>{{item.phone}}</text>
                    </view>
                    <view class="jd_add_subtitle" data-id="{{item.id}}">
                        <text wx:if="{{item.is_default==1}}">默认</text> {{item.province_name}} {{item.city_name}} {{item.district_name}} {{item.town_name==null?'':item.town_name}} {{item.address}} </view>
                </view>
            </scroll-view>
        </view>
    </view>
    <view class="jd_select" wx:else>
        <view class="jd_title">{{publicObj.title}}<image bindtap="close" src="/img/icon/<EMAIL>"></image>
        </view>
        <view class="jd_choice">
            <view bindtap="choice" class="{{publicObj.page==index?'active':''}}" data-index="{{index}}" data-item="{{item.select}}" wx:for="{{publicObj.selected}}" wx:key="unique">{{item.select}}</view>
            <view class="active" wx:if="{{publicObj.multipleChoice}}">{{select}}</view>
        </view>
        <scroll-view class="jd_list" scrollY="true" style="height: 700rpx;">
            <view bindtap="select" class="jd_select-list {{publicObj.selected[publicObj.page].key==index?'listActive':''}}" data-id="{{item.c}}" data-index="{{index}}" data-item="{{item.n||item}}" wx:for="{{publicObj.pageList}}" wx:key="unique"> {{item.n||item}} <image src="/img/icon/dui.png" wx:if="{{publicObj.selected[publicObj.page].key==index?'listActive':''}}"></image>
            </view>
        </scroll-view>
    </view>
    <view bindtap="close" class="jd_shade"></view>
</view>
