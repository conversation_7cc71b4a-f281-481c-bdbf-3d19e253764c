<view class="head">
    <select-mall bind:fireReSearch="fireReSearch" shopId="{{shopId}}"></select-mall>
    <view class="head_search_con">
        <input bindtap="clickSearchLocation" class="head_seach" disabled="true" placeholder="请输入商品名称"></input>
        <image class="head_seach_icon" mode="widthFix" src="../../img/smicon/index_search.png"></image>
    </view>
</view>
<view class="head-topa">
    <official-account binderror="gzhfail" bindload="gzhok"></official-account>
    <new-comer bind:fireAddCart="fireAddCartRec" newcomerCouponsList="{{newcomerCouponsList}}" newcomerGoodsList="{{newcomerGoodsList}}" wx:if="{{newcomerCouponsList&&newcomerCouponsList.length>0||newcomerGoodsList&&newcomerGoodsList.length>0}}"></new-comer>
    <sm-index-con actList="{{actList}}" ballList="{{ballList}}" bannerList="{{bannerList}}" basicSetting="{{basicSetting}}" bind:fireAddCart="fireAddCartRec" couponList="{{couponList}}" dragonGame="{{dragonGame}}" duotuList="{{duotuList}}" flashSale="{{flashSale}}" flashSaleList="{{flashSaleList}}" groupBuyingList="{{groupBuyingList}}" mdid="{{shopId}}" menuList="{{menuList}}" menuLists="{{menuLists}}" preSaleList="{{preSaleList}}" timeDjs="{{timeDjs}}" zhongtongList="{{zhongtongList}}" zhuanquList="{{zhuanquList}}"></sm-index-con>
</view>
<view class="scroll-group-zhan">
    <view class="scroll-group-wrapper {{groupIdTop<96?'scroll-group-wrapper-fixed':''}}">
        <scroll-view scrollX class="scroll-group-top">
            <view bindtap="tapChooseGroup" class="group-it {{index==groupChooseIndex?'group-it-choosed':''}}" data-index="{{index}}" wx:for="{{goodsGroupList}}" wx:key="id">
                <view class="group-title">{{item.name}}</view>
                <view class="group-subtitle">{{item.subtitle}}</view>
            </view>
        </scroll-view>
    </view>
</view>
<view class="sp-list" id="scroll-group-id">
    <sp-list bind:fireAddCart="fireAddCartRec" groupId="{{goodsGroupList[groupChooseIndex].id}}" groupImgurl="{{goodsGroupList[groupChooseIndex].erectImg}}" list="{{showList}}"></sp-list>
</view>
<on-loading isShow="{{pageNum>1&&hasMore}}"></on-loading>
<no-more isShow="{{pageNum>1&&!hasMore}}"></no-more>
<cs-screen screenData="{{screenData}}"></cs-screen>
<improve-image bind:closeSrl="hideImprove" improveUrl="{{improveUrl}}" wx:if="{{improveShow}}"></improve-image>
<buoy-advert id="buoy" pageType="homePage"></buoy-advert>
<to-cart cartNum="{{cartNum}}"></to-cart>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>