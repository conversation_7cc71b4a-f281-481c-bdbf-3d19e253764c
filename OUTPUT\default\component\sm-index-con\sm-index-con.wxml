<view class="head2">
    <view class="second-module-allicon">
        <view class="jiben-tishi">
            <view class="tishi-item" wx:if="{{basicSetting}}">
                <image src="{{filter.addImgPath(basicSetting.deliverIcon)}}"></image>
                <text>{{basicSetting.deliverDesc}}</text>
            </view>
            <view class="tishi-item">
                <image src="../../img/smicon/songda.png"></image>
                <text>最快45分钟送达</text>
            </view>
            <view class="tishi-item">
                <image src="../../img/smicon/anxin.png"></image>
                <text>安心退</text>
            </view>
        </view>
        <scroll-view scrollX bindscroll="scroll" class="nav-bar">
            <view class="nav-bar-wrap">
                <view bindtap="toMenu" class="nav-bar-item" data-it="{{item}}" data-url="{{item.url}}" wx:for="{{menuList}}" wx:key="id">
                    <image src="{{filter.addImgPath140x140(item.url)}}"></image>
                    <text>{{item.name}}</text>
                </view>
            </view>
        </scroll-view>
        <scroll-view scrollX bindscroll="scroll" class="nav-bar" wx:if="{{ballList&&ballList.length>0}}">
            <view class="nav-bar-wrap1">
                <view bindtap="toMenu" class="nav-bar-item1" data-it="{{item}}" data-url="{{item.url}}" wx:for="{{ballList}}" wx:key="id">
                    <image src="{{filter.addImgPath140x140(item.url)}}"></image>
                    <text>{{item.name}}</text>
                </view>
            </view>
        </scroll-view>
        <view class="slider">
            <view class="slider-inside .slider-inside-location" style="left:{{left}}"></view>
        </view>
    </view>
</view>
<view class="rukou-wrapper" wx:if="{{zhongtongList&&zhongtongList.length>0}}">
    <view class="no-tuangou" wx:for="{{zhongtongList}}" wx:key="id">
        <image bindtap="tapAdvert" data-linkurl="{{item.linkUrl}}" mode="widthFix" src="{{filter.addImgPath(item.imgurl)}}"></image>
    </view>
</view>
<view class="li-zhongtong-wrapper" wx:if="{{duotuList&&duotuList.length>0}}">
    <scroll-view>
        <view class="li-zhongtong-wrap">
            <view bindtap="tapAdvert" class="li-zhongtong-item" data-linkurl="{{item.linkUrl}}" wx:for="{{duotuList}}" wx:key="id">
                <image src="{{filter.addImgPath(item.imgurl)}}"></image>
            </view>
        </view>
    </scroll-view>
</view>
<view bindtap="goBbys" class="bq-left" wx:if="{{preSaleList&&preSaleList.length>0}}">
    <view class="header-container">
        <view>
            <text class="main-text">爆品预售</text>
            <text class="sub-text">  · 口碑推荐 直击底价</text>
        </view>
        <view class="view-all">查看全部</view>
    </view>
    <view class="bq-list">
        <view class="bq-item" wx:for="{{preSaleList}}" wx:key="id">
            <view class="bq-img">
                <view class="bq-img-wra">
                    <image lazyLoad class="bq-image" src="{{filter.addImgPath350x350(item.picture)}}"></image>
                    <image class="xsqg-freeze" mode="heightFix" src="{{filter.addImgPath(item.freezeUrl)}}" wx:if="{{item.freezeUrl}}"></image>
                    <image class="xsqg-sale" mode="heightFix" src="{{filter.addImgPath(item.saleUrl)}}" wx:if="{{item.saleUrl}}"></image>
                </view>
            </view>
            <view class="bq-xj">
                <text>￥{{filter.priceToFixed2(item.preSalePrice)}}</text>
            </view>
            <view class="bq-yj">￥{{filter.priceToFixed2(item.originalPrice)}}</view>
        </view>
    </view>
</view>
<view class="container">
    <view class="row">
        <block wx:if="{{actList&&actList.length>0}}" wx:for="{{actList}}" wx:key="id">
            <view class="col" wx:if="{{item.type==6&&item.actType==3}}">
                <view bindtap="goXsqg" class="box">
                    <text class="title bq-top">每日必抢
          <block wx:if="{{flashSale.isSelling}}">
                            <text class="text">{{timeDjs.hours}}</text>
                            <text class="zhongjian"> : </text>
                            <text class="text">{{timeDjs.minutes}}</text>
                            <text class="zhongjian"> : </text>
                            <text class="text">{{timeDjs.seconds}}</text>
                        </block>
                        <text class="first-text zhongjian" wx:else>即将开始</text>
                    </text>
                    <block wx:if="{{flashSaleList&&flashSaleList.length>0}}">
                        <image lazyLoad class="img" src="{{filter.addImgPath350x350(flashSaleList[0].url)}}"></image>
                        <text class="name">{{flashSaleList[0].goodsName}}</text>
                        <view class="price">
                            <view class="current">¥{{filter.priceToFixed2(flashSaleList[0].promotionPrice)}} <text class="original">¥{{filter.priceToFixed2(flashSaleList[0].markingPrice)}}</text>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
            <view class="col" wx:if="{{item.type==6&&item.actType==2}}">
                <view bindtap="goXspt" class="box">
                    <text class="title bq-top">限时拼团
        <text class="subtext">组团拼单 更实惠</text>
                    </text>
                    <block wx:if="{{groupBuyingList&&groupBuyingList.length>0}}">
                        <image lazyLoad class="img" src="{{filter.addImgPath350x350(groupBuyingList[0].picture)}}"></image>
                        <text class="name">{{groupBuyingList[0].goodsName}}</text>
                        <view class="price">
                            <view class="current">¥{{filter.priceToFixed2(groupBuyingList[0].groupBuyingPrice)}}<text class="original">¥{{filter.priceToFixed2(groupBuyingList[0].markingPrice)}} </text>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
            <view class="col" wx:if="{{item.type==6&&item.actType==1}}">
                <view bindtap="goDragon" class="box">
                    <text class="title bq-top">接龙
        <text class="subtext">组团更实惠</text>
                    </text>
                    <block wx:if="{{dragonGame}}">
                        <image lazyLoad class="img" src="{{filter.addImgPath350x350( dragonGame.products[0].picList[0] )}}"></image>
                        <text class="name">{{dragonGame.products[0].goodsName}}</text>
                        <view class="price">
                            <view class="current">¥{{filter.priceToFixed2(dragonGame.products[0].preSaleInfo.preSalePrice)}}<text class="original">¥{{filter.priceToFixed2(dragonGame.products[0].markingPrice)}} </text>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
        </block>
        <view bindtap="tapAdvert" class="col coupon-col" data-linkurl="/supermarket/pages/coupon-list/coupon-list?tab=2&isCommon=1">
            <image lazyLoad class="couponimg" src="https://obs.springland.com.cn/mg-mp/image/ui/coupon-center.png"></image>
        </view>
    </view>
</view>
<goods-spec bind:changeCartNum="changeCartNum" bind:chooseGuige="chooseGuige" bind:hideSpec="hideSpec" goodsInfo="{{goodsInfo}}" paramData="{{paramData}}" specShow="{{specShow}}"></goods-spec>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>