<view class="plugin-mall-list exposure-report-dom" data-exposure-data="{{({event_id:100000})}}" id="auth-circle-list">
    <view class="mall-list-title">当前会员卡的支付即积分服务所覆盖的商圈如下：</view>
    <view class="padding-bottom">
        <block wx:for="{{authCircleNameList}}" wx:key="name">
            <text class="mall-brand-name">{{item.name}}</text>
            <text class="mall-brand-name" wx:if="{{authCircleNameList.length-1===index&&!loadMoreAuthCircleStatus}}">。</text>
            <text class="mall-brand-name" wx:elif="{{authCircleNameList.length-1!==index}}">、</text>
        </block>
        <text bind:tap="addMoreMallAuthBrand" class="load-more-text" wx:if="{{loadMoreAuthCircleStatus}}">  加载更多</text>
    </view>
</view>
