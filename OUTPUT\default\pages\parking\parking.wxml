<view bindtap="hideJianpan" class="container">
    <view class="top-wrapper">
        <view class="yuan-padding">
            <view class="yuan-wrapper">
                <view class="wenzi-left">P</view>
                <view class="juti-rigth">
                    <view bindtap="goMallList" class="select-mall">
                        <view class="mallname">{{mall_name}}停车场</view>
                        <view class="xiao-tishi">本车场只支持线上缴费</view>
                        <view class="xiao-tb">
                            <image src="/img/icon/jian-xia.png"></image>
                        </view>
                    </view>
                    <view bindtap="showDesc" class="cc-desc">
                        <view class="ccms">{{parkingSet.sfsm}}</view>
                        <view class="ms-icon">
                            <image src="/img/icon/che-tishi.png"></image>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="lc-thishi">
            <image src="/img/icon/hui.png"></image>
            <text>提前缴费，{{parkingSet.lcsj}}分钟内出场，避免排队等待</text>
        </view>
    </view>
    <view class="chepai-wrapper">
        <view class="chepai-con">
            <view class="qiehuan-wra">
                <view class="qiehuan">
                    <view bindtap="tapQiehuan" class="qiehuan-item {{activeTab==1?'qiehuan-active':''}}" data-tab="1">车牌绑定</view>
                    <view bindtap="tapQiehuan" class="qiehuan-item {{activeTab==2?'qiehuan-active':''}}" data-tab="2">手动缴费</view>
                </view>
            </view>
            <block wx:if="{{activeTab==1}}">
                <block wx:if="{{plateNoList&&plateNoList.length>0}}">
                    <picker bindchange="choosePlateNo" range="{{plateNoList}}" rangeKey="plateNo" value="{{plateNoIndex}}">
                        <view class="chepai-bd">
                            <view class="chepai-bd-no">{{plateNoList[plateNoIndex].plateNo}}</view>
                            <image class="chepai-xiala" src="/img/icon/<EMAIL>"></image>
                            <image catchtap="addChepai" class="chepai-add-tj" src="/img/icon/add-chepai.png"></image>
                        </view>
                    </picker>
                    <view class="btn-aaaa">
                        <view bindtap="bindQuery" class="query">查询</view>
                    </view>
                </block>
                <view class="chepai-bd" wx:else>
                    <view class="chepai-bd-ts">绑定车牌，缴费离场方便快捷！</view>
                    <view bindtap="addChepai" class="chepai-bd-xz">
                        <image src="/img/icon/add-no.png"></image>
                        <view class="chepai-bd-bdcp">+ 绑定车牌</view>
                    </view>
                </view>
            </block>
            <block wx:else>
                <view class="chepai-input">
                    <view class="chepai-img">
                        <image src="/img/icon/qiche.png"></image>
                    </view>
                    <view catchtap="showJianpan" class="chapai-item {{isShow&&activeIndex==item?'chepai-active':''}} {{item==7&&!plateNo[item]?'xinnengyuan':''}}" data-xh="{{item}}" wx:for="{{8}}">
                        <block wx:if="{{item==7}}">{{plateNo[item]?plateNo[item]:'新能源'}}</block>
                        <block wx:else>{{plateNo[item]}}</block>
                    </view>
                </view>
                <view class="btn-aaaa">
                    <view bindtap="bindQuery" class="query">查询</view>
                </view>
                <view class="zuijin-chepai" wx:if="{{plateNoRecentList.length>0}}">
                    <view class="chepai-item" wx:for="{{plateNoRecentList}}" wx:key="plateNo">
                        <view catchtap="chooseZuijinChepai" class="che-text kuoda" data-item="{{item}}">{{item.plateNo}}</view>
                        <image catchtap="deleteZuijinChepai" class="kuoda" data-item="{{item}}" src="/img/icon/shanchu-x.png"></image>
                    </view>
                </view>
            </block>
        </view>
    </view>
    <view class="caozuo-wrapper">
        <view bindtap="goRecords" class="caozuo">
            <image src="../../img/icon/tc-record.png"></image>
            <text class="wenzi">停车记录</text>
        </view>
        <view bindtap="goLqtcq" class="caozuo">
            <image src="../../img/icon/lqtcq.png"></image>
            <text class="wenzi">领取停车券</text>
        </view>
        <view bindtap="goWdtcq" class="caozuo">
            <image src="../../img/icon/wdtcq.png"></image>
            <text class="wenzi">我的停车券</text>
        </view>
        <view bindtap="goChangyong" class="caozuo" wx:if="{{hasLogin}}">
            <image src="../../img/icon/cpgl.png"></image>
            <text class="wenzi">车牌管理</text>
        </view>
        <view bindtap="goInvoice" class="caozuo" wx:if="{{(mdid==6002||mdid==6003||mdid==6004||mdid==6005||mdid==6006||mdid==6007||mdid==6009||mdid==6010||mdid==6011||mdid==6013||mdid==6016||mdid==6018||mdid==6019||mdid==6021||mdid==6022||mdid==6023||mdid==6028||mdid==6030||mdid==6031||mdid==6036||mdid==6037)&&hasLogin}}">
            <image src="../../img/icon/sqkp.png"></image>
            <text class="wenzi">申请开票</text>
        </view>
        <view bindtap="goFxxc" class="caozuo" wx:if="{{mdid==6030||mdid==6031||mdid==6018||mdid==6004||mdid==6019||mdid==6010}}">
            <image src="../../img/icon/cpgl.png"></image>
            <text class="wenzi">寻车</text>
        </view>
        <view bindtap="goYycw" class="caozuo" wx:if="{{mdid==6006}}">
            <image src="../../img/icon/cpgl.png"></image>
            <text class="wenzi">车位</text>
        </view>
        <view bindtap="goZfgl" class="caozuo" wx:if="{{hasLogin}}">
            <image src="../../img/icon/zfgl.png"></image>
            <text class="wenzi">支付管理</text>
        </view>
    </view>
    <view class="top-margin"></view>
    <swiper autoplay="true" circular="true" class="screen-swiper square-dot" current="{{currentTap}}" duration="500" indicatorDots="true" interval="5000" wx:if="{{bannerList.length>0}}">
        <swiper-item bindtap="navPicClick" data-jumptype="{{it.jumpType}}" data-url="{{it.linkUrl}}" wx:for="{{bannerList}}" wx:for-item="it" wx:key="{{it.imgUrl}}">
            <image mode="widthFix" src="{{it.imgUrl}}"></image>
        </swiper-item>
    </swiper>
    <onlyforyou banner_pic="https://obs-179f.obs.cidc-rp-12.joint.cmecloud.cn/obs-179f/huadi2/2020/06/1591955279899851.png" class="only_bg" id="onlyforyou" mode="widthFix"></onlyforyou>
</view>
<modal-box class="{{descModalShow?'show':''}}">
    <view bindtap="hodeDesc" class="beijing"></view>
    <dialog class="ccxinxi bg-white">
        <view class="xinxi-wraee">
            <view class="chechang-title">车场信息</view>
            <view class="chechang-desc">
                <text>{{parkingSet.sfsm}}</text>
            </view>
            <view bindtap="hodeDesc" class="i-known">我知道了</view>
        </view>
    </dialog>
</modal-box>
<back-home homeShow="{{homeShow}}"></back-home>
<v-panel backgroundColor="white" binddelete="inputdelete" bindinputchange="inputChange" isShow="{{isShow}}" keyBoardType="{{keyBoardType}}" qty="{{plateNo.length}}"></v-panel>
<view class="mallcircle" wx:if="{{isUnCommit==1}}">
    <view class="mallcircle-shadow"></view>
    <view class="mallcircle-box">
        <image class="mallcircle-img" src="https://obs.springland.com.cn/mg-mp/image/20230621145512.png"></image>
        <view bindtap="unCommitClose" class="mallcircle-qx">取消</view>
        <view bindtap="goUnCommit" class="mallcircle-kt">立即领取</view>
    </view>
</view>
