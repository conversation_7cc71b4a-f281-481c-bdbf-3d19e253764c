<view class="^coupon_img ^pa-1" wx:if="{{is_coupon==1}}">
    <view class="flex_center cimg">
        <image catchtap="linq" class="coupon_top" src="https://obs.springland.com.cn/mg-mp/image/20240220144953.png"></image>
    </view>
    <view class="coupon_bottom">
        <view class="coupon_item flex_center" wx:for="{{coupon_list}}" wx:key="key">
            <view bindtap="couponDetail" class="coupon_item_1 flex_d" data-index="{{index}}">
                <view style="padding-bottom: 30rpx;">
                    <view class="money" wx:if="{{item.mz!=undefined&&item.mz!=null}}">¥{{item.mz}}</view>
                    <view class="money" wx:elif="{{item.mzje!=undefined&&item.mzje!=null}}">¥{{item.mzje}}</view>
                    <view class="money" wx:elif="{{item.discount_money!=undefined&&item.discount_money!=null}}">¥{{item.discount_money}}</view>
                    <view class="money" wx:else>¥{{item.amount||item.gmje||item.je}}</view>
                    <view class="inline"></view>
                    <view class="f_coupon_name">{{item.name||item.bt||item.qmc||item.title}}</view>
                </view>
                <view class="butt-in flex_center">
                    <view catchtap="couponSubSureNew" class="text" data-index="{{index}}" wx:if="{{item.qzslx==1}}">
                        <text wx:if="{{item.canJoin==1&&item.hdlx==3}}">立即购买</text>
                        <text wx:elif="{{item.hdlx==2}}">立即兑换</text>
                        <text wx:elif="{{item.canJoin!=1&&item.reason!=null}}">{{item.reason}}</text>
                        <text wx:else>立即领取</text>
                    </view>
                    <view catchtap="fireCouponEventCs" class="text" data-index="{{index}}" data-item="{{item}}" wx:elif="{{item.qzslx==3}}">
                        <text wx:if="{{item.qbh}}">去使用</text>
                        <text wx:else>
                            <block wx:if="{{item.canJoin==1}}">
                                <block wx:if="{{item.type==1}}">立即领取</block>
                                <block wx:elif="{{item.type==2}}">立即兑换</block>
                                <block wx:else>立即购买</block>
                            </block>
                            <block wx:else>{{item.reason}}</block>
                        </text>
                    </view>
                    <block wx:else>
                        <view catchtap="goGoodsList" class="text" data-app_id="{{item.app_id}}" data-id="{{item.id}}" data-prime="{{item.prime}}" data-store="{{item.store_id}}" wx:if="{{item.kl==0||item.jd>=100&&item.kl==0}}"> 去使用 </view>
                        <view wx:elif="{{item.jd>=100&&item.kl==1}}"> 已抢光 </view>
                        <view catchtap="couponSubSure" class="text" data-id="{{item.id}}" data-prime="{{item.prime}}" wx:else> 立即领取 </view>
                    </block>
                </view>
            </view>
        </view>
        <view class="coupon_item flex_center">
            <view class="coupon_item_1 flex_d" style="background: #f8f0e4;">
                <view class="flex_center" style="width: 100%;">
                    <view catchtap="linq" style="font-size: 26rpx;color: #382102;font-weight: 600;padding-top: 50rpx;">
                        <view style="margin: auto;width: 100%;text-align: center;">
                            <image src="https://obs.springland.com.cn/mg-mp/image/right-arrow-2.png" style="width: 50rpx;height: 50rpx;"></image>
                        </view>
                        <view style="line-height: 30px;letter-spacing: 2px;width: 100%;color: #ce9f73"> 查看更多 </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>
<modal-pay-coupon bindnumMinus="numMinus" bindnumPlus="numPlus" bindpayevent="pay" canBuy="{{canBuy}}" disabled="{{disabled}}" isShow="{{isModalShow==1}}" num="{{couponList[selectIndex].qty}}" price="{{couponList[selectIndex].gmje}}" saleName="{{couponList[selectIndex].bt}}"></modal-pay-coupon>
<modal-pay-coupon bindnumMinus="numMinusCs" bindnumPlus="numPlusCs" bindpayevent="payCs" canBuy="{{canBuy}}" disabled="{{disabled}}" isShow="{{isModalShow==2}}" num="{{couponList[selectIndex].gmsl}}" price="{{couponList[selectIndex].gmje}}" saleName="{{couponList[selectIndex].title}}"></modal-pay-coupon>
<modal-box class="{{couponMcShow?'show':''}}"> -->
 <dialog class="dialogaa">
        <bar class="justify-end">
            <view class="contentFu">领取成功，将为您同步加入到微信卡包</view>
        </bar>
        <view class="padding-xl bg-white">
            <send-coupon bindcustomevent="getcoupon" send_coupon_merchant="{{couponMcInfo.send_coupon_merchant}}" send_coupon_params="{{couponMcInfo.send_coupon_params}}" sign="{{couponMcInfo.sign}}">
                <button class="bg-green btn-sjqlq">确认</button>
            </send-coupon>
        </view>
    </dialog>
</modal-box>
