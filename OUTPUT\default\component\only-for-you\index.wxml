<view class="content" style="margin-bottom:{{margin_bottom}};" wx:if="{{goods_list.length!=0}}">
    <view class="only_title">
        <image class="only_bg" lazyLoad="true" mode="widthFix" src="{{banner_pic}}"></image>
    </view>
    <view class="clearfix">
        <view class="goods" wx:for="{{goods_list}}" wx:key="key">
            <navigator bindtap="we_click" class="we-prod-card" data-id="{{item.id}}" data-item="{{item}}" data-scene="home_recommend_api_item" hoverClass="none" url="/home/<USER>/goods-detail/index?goods_detail_id={{item.id}}">
                <view class="goods_img">
                    <image class="goods_image" lazyLoad="true" mode="widthFix" src="{{item.cover_img}}?x-image-process=style/style-350x350"></image>
                </view>
                <view class="goods_title">
                    <view class="scroll_zhekou" wx:if="{{item.zhekou}}">{{item.zhekou}}折</view> {{item.name}}</view>
                <view class="goods_label" wx:if="{{item.discount}}">
                    <text>{{item.discount}}折</text>
                </view>
                <view class="goods_label" wx:else></view>
                <view class="goods_price">
                    <text>¥ {{item.price}}</text>
                    <text class="best_price">到手价</text>
                    <text hidden="{{item.market_price<=0}}">¥ {{item.market_price}}</text>
                </view>
            </navigator>
        </view>
    </view>
</view>
