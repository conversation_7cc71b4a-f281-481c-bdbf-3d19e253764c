/**
 * 专门测试Authorization后32位的生成方法
 */

const crypto = require('crypto');

const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
const targetSuffix = "a4e6557df6cb447aa314544f308a7a5d"; // 后32位
const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
const hyid = "820536796";
const mdid = "6021";

function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

function sha1(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

console.log("=== 专门测试后32位生成 ===");
console.log("目标后32位:", targetSuffix);
console.log();

// 从原始token中提取可能的参数
const tokenSuffix = originalToken.substring(8); // c099c83322274fe5a8ad1f46c7f5882a

console.log("--- 测试1: 基于原始token ---");
console.log("原始token后32位:", tokenSuffix);

// 测试token相关的组合
const tokenCombos = [
    tokenSuffix,
    tokenSuffix + hyid,
    tokenSuffix + mdid,
    tokenSuffix + hyid + mdid,
    hyid + tokenSuffix,
    mdid + tokenSuffix,
    hyid + mdid + tokenSuffix,
    tokenSuffix + mdid + hyid,
];

tokenCombos.forEach((combo, i) => {
    const md5Result = md5(combo);
    const sha1Result = sha1(combo);
    
    console.log(`${i+1}. "${combo.substring(0, 50)}${combo.length > 50 ? '...' : ''}"`);
    
    if (md5Result === targetSuffix) {
        console.log(`   ✓ MD5完整匹配!`);
    } else if (md5Result.substring(0, 32) === targetSuffix) {
        console.log(`   ✓ MD5前32位匹配!`);
    }
    
    if (sha1Result === targetSuffix) {
        console.log(`   ✓ SHA1完整匹配!`);
    } else if (sha1Result.substring(0, 32) === targetSuffix) {
        console.log(`   ✓ SHA1前32位匹配!`);
    }
});

console.log();

// 测试2: 尝试不同的参数顺序和分隔符
console.log("--- 测试2: 参数组合 ---");
const paramCombos = [
    hyid + mdid,
    mdid + hyid,
    hyid + ":" + mdid,
    mdid + ":" + hyid,
    hyid + "|" + mdid,
    mdid + "|" + hyid,
    hyid + "&" + mdid,
    mdid + "&" + hyid,
    `hyid=${hyid}&mdid=${mdid}`,
    `mdid=${mdid}&hyid=${hyid}`,
    `hyid=${hyid}mdid=${mdid}`,
    `mdid=${mdid}hyid=${hyid}`,
];

paramCombos.forEach((combo, i) => {
    const md5Result = md5(combo);
    const sha1Result = sha1(combo);
    
    if (md5Result === targetSuffix || sha1Result === targetSuffix) {
        console.log(`✓ 找到匹配! 组合${i+1}: "${combo}"`);
        if (md5Result === targetSuffix) console.log(`  MD5匹配`);
        if (sha1Result === targetSuffix) console.log(`  SHA1匹配`);
    }
});

console.log();

// 测试3: 尝试包含常量的组合
console.log("--- 测试3: 包含常量 ---");
const REQUEST_KEY = "SPRINGLAND";
const REQUEST_SECRET = "springland*&^0627@";

const constantCombos = [
    REQUEST_KEY + hyid + mdid,
    hyid + mdid + REQUEST_SECRET,
    REQUEST_KEY + hyid + mdid + REQUEST_SECRET,
    REQUEST_SECRET + hyid + mdid,
    hyid + REQUEST_KEY + mdid,
    hyid + mdid + REQUEST_KEY,
    mdid + REQUEST_KEY + hyid,
    REQUEST_KEY + mdid + hyid + REQUEST_SECRET,
];

constantCombos.forEach((combo, i) => {
    const md5Result = md5(combo);
    const sha1Result = sha1(combo);
    
    if (md5Result === targetSuffix || sha1Result === targetSuffix) {
        console.log(`✓ 找到匹配! 组合${i+1}: "${combo}"`);
        if (md5Result === targetSuffix) console.log(`  MD5匹配`);
        if (sha1Result === targetSuffix) console.log(`  SHA1匹配`);
    }
});

console.log();

// 测试4: 尝试URL相关
console.log("--- 测试4: URL相关 ---");
const urls = [
    "/sign/sign",
    "sign/sign",
    "https://mp-gp.springland.com.cn/sign/sign",
    "mp-gp.springland.com.cn/sign/sign",
];

urls.forEach(url => {
    const urlCombos = [
        url + hyid + mdid,
        hyid + mdid + url,
        hyid + url + mdid,
        mdid + url + hyid,
        url + hyid,
        url + mdid,
        hyid + url,
        mdid + url,
    ];
    
    urlCombos.forEach(combo => {
        const md5Result = md5(combo);
        const sha1Result = sha1(combo);
        
        if (md5Result === targetSuffix || sha1Result === targetSuffix) {
            console.log(`✓ 找到匹配! URL: "${url}", 组合: "${combo}"`);
            if (md5Result === targetSuffix) console.log(`  MD5匹配`);
            if (sha1Result === targetSuffix) console.log(`  SHA1匹配`);
        }
    });
});

console.log();

// 测试5: 尝试时间相关（可能的固定时间戳）
console.log("--- 测试5: 可能的时间戳 ---");
// 尝试一些可能的时间戳（从抓包时间推测）
const possibleTimes = [
    "1722470400", // 2024-08-01
    "1735689600", // 2025-01-01
    "1704067200", // 2024-01-01
    "20240801",   // 日期格式
    "20250101",
    "20240729",   // 可能的抓包日期
];

possibleTimes.forEach(time => {
    const timeCombos = [
        hyid + mdid + time,
        time + hyid + mdid,
        hyid + time + mdid,
        mdid + time + hyid,
        time + hyid,
        time + mdid,
    ];
    
    timeCombos.forEach(combo => {
        const md5Result = md5(combo);
        const sha1Result = sha1(combo);
        
        if (md5Result === targetSuffix || sha1Result === targetSuffix) {
            console.log(`✓ 找到匹配! 时间: "${time}", 组合: "${combo}"`);
            if (md5Result === targetSuffix) console.log(`  MD5匹配`);
            if (sha1Result === targetSuffix) console.log(`  SHA1匹配`);
        }
    });
});

console.log();
console.log("=== 如果没有找到匹配 ===");
console.log("可能的原因:");
console.log("1. 包含了动态参数（如当前时间戳、随机数）");
console.log("2. 使用了其他加密算法");
console.log("3. 是服务器端生成的固定会话token");
console.log("4. 包含了我们不知道的隐藏参数");
