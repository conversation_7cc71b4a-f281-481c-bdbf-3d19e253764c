<view class="weui-navigation-bar {{extClass}} {{screenType==='horizontal'?'weui-navigation-bar__horizontal':''}} {{'weui-navigation-bar__'+liveStatus}} {{isSupportBounding?'weui-navigation-bar__support-bounding':''}} {{isNoCenter?'weui-navigation-bar__no-center':''}}">
    <view class="weui-navigation-bar__placeholder {{ios?'ios':'android'}}" style="padding-top: {{statusBarHeight}}px;visibility: hidden;"></view>
    <view class="weui-navigation-bar__body {{ios?'ios':'android'}}" style="padding-top: {{statusBarHeight}}px; color: {{color}};background: {{background}};{{displayStyle}};{{innerPaddingRight}};{{innerWidth}};">
        <view class="weui-navigation-bar__left">
            <block wx:if="{{back}}">
                <view class="weui-navigation-bar__buttons">
                    <view bindtap="back" class="weui-navigation-bar__button weui-navigation-bar__btn_goback" wx:if="{{!isExitMiniprogram}}"></view>
                    <navigator bindtap="exit" openType="exit" target="miniProgram" wx:else>
                        <view class="weui-navigation-bar__button weui-navigation-bar__btn_goback"></view>
                    </navigator>
                </view>
                <slot name="left"></slot>
            </block>
            <slot name="left" wx:else></slot>
        </view>
        <view class="weui-navigation-bar__center" wx:if="{{!isNoCenter}}">
            <view class="weui-navigation-bar__loading" wx:if="{{loading}}">
                <view class="weui-loading" style="width:{{size.width}}rpx;height:{{size.height}}rpx;"></view>
            </view>
            <view wx:if="{{title}}">{{title}}</view>
            <slot name="center" wx:else></slot>
        </view>
        <view class="weui-navigation-bar__right">
            <slot name="right"></slot>
        </view>
    </view>
</view>
