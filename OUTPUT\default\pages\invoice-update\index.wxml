<view class="content">
    <view class="list">订单编号<text>{{order_code}}</text>
    </view>
    <view class="list">开票号<text>{{invoice_code}}</text>
    </view>
    <view class="list">金额<text>{{invoice_price}}元</text>
    </view>
</view>
<view class="content">
    <view class="list">发票类型<view class="list_cho">
            <picker bindchange="invoiceType" class="picker" range="{{invoice_array}}" value="{{invoice_type}}">
                <text>{{invoice_default}}</text>
            </picker>
            <image src="/image/after_ems02.png"></image>
        </view>
    </view>
    <view class="list">抬头类型<view class="list_cho">
            <picker bindchange="headType" class="picker" range="{{head_array}}" value="{{head_type}}">
                <text>{{head_default}}</text>
            </picker>
            <image src="/image/after_ems02.png"></image>
        </view>
    </view>
    <view class="list">发票抬头<view bindtap="headDetail" class="list_cho spe">{{title}} <image src="/image/after_ems02.png"></image>
        </view>
    </view>
    <view class="list" wx:if="{{head_type==1}}">单位税号 <view class="list_cho">
            <input bindinput="taxNumberDetail" placeholder="请输入单位税号" value="{{taxNumber}}"></input>
        </view>
    </view>
</view>
<view class="content" wx:if="{{head_type==1}}">
    <view class="list">单位地址 <view class="list_cho">
            <input bindinput="addressDetail" placeholder="单位地址信息" value="{{bank_address}}"></input>
        </view>
    </view>
    <view class="list">单位电话 <view class="list_cho">
            <input bindinput="bankPhoneDetail" placeholder="单位电话号码" value="{{bank_phone}}"></input>
        </view>
    </view>
    <view class="list">开户银行 <view class="list_cho">
            <input bindinput="bankNameDetail" placeholder="开户银行名称" value="{{bank_name}}"></input>
        </view>
    </view>
    <view class="list">银行账号 <view class="list_cho">
            <input bindinput="bankDetail" placeholder="银行账号号码" value="{{bankAccount}}"></input>
        </view>
    </view>
</view>
<block wx:if="{{invoice_type!=9}}">
    <view class="content" wx:if="{{invoice_type==0&&goods_type!=2}}">
        <view class="list">手机号码 <view class="list_cho">
                <input bindinput="phoneDetail" placeholder="{{phone}}"></input>
            </view>
        </view>
        <view class="list">电子邮箱 <view class="list_cho">
                <input bindinput="emailDetail" placeholder="用来接收电子邮件(必填)" value="{{email}}"></input>
            </view>
        </view>
    </view>
    <view class="content" wx:else>
        <view class="list">手机号码 <view class="list_cho">
                <input bindinput="phoneDetail" placeholder="{{phone}}"></input>
            </view>
        </view>
        <view class="list">收件人 <view class="list_cho">
                <input bindinput="nameDetail" placeholder="{{name}}"></input>
            </view>
        </view>
        <view class="list">收件地址 <view class="list_cho">
                <input bindblur="blurAddress" bindfocus="getLocation" value="{{address}}"></input>
            </view>
        </view>
        <view class="list" wx:if="{{goods_type==2}}">电子邮箱 <view class="list_cho">
                <input bindinput="emailDetail" placeholder="用来接收电子邮件(必填)" value="{{email}}"></input>
            </view>
        </view>
        <view class="list">备注 <view class="list_cho">
                <input bindinput="remarkDetail" placeholder="选填"></input>
            </view>
        </view>
    </view>
</block>
<view class="list_last"></view>
