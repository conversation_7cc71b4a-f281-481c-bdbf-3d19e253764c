/**
 * 简单测试Authorization生成方式
 * 尝试更多可能的组合
 */

const crypto = require('crypto');

// 从抓包数据中提取的信息
const targetAuth = "1fb508f5a4e6557df6cb447aa314544f308a7a5d";
const originalToken = "1fb508f5c099c83322274fe5a8ad1f46c7f5882a";
const hyid = "820536796";
const mdid = "6021";

// 常量
const REQUEST_KEY = "SPRINGLAND";
const REQUEST_SECRET = "springland*&^0627@";

function sha1(str) {
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex');
}

function md5(str) {
    return crypto.createHash('md5').update(str, 'utf8').digest('hex');
}

console.log("=== 简单Authorization验证 ===");
console.log("目标:", targetAuth);
console.log("Token:", originalToken);
console.log();

// 测试1: 直接使用各种简单组合
console.log("--- 测试1: 简单字符串组合 ---");
const simpleTests = [
    hyid + mdid,
    mdid + hyid,
    hyid,
    mdid,
    hyid + "_" + mdid,
    mdid + "_" + hyid,
    REQUEST_KEY + hyid + mdid,
    hyid + mdid + REQUEST_SECRET,
    REQUEST_KEY + hyid + mdid + REQUEST_SECRET,
];

simpleTests.forEach((test, i) => {
    const sha1Result = sha1(test);
    const md5Result = md5(test);
    console.log(`${i+1}. "${test}"`);
    console.log(`   SHA1: ${sha1Result} ${sha1Result === targetAuth ? '✓' : ''}`);
    console.log(`   MD5:  ${md5Result} ${md5Result === targetAuth ? '✓' : ''}`);
});

console.log();

// 测试2: 基于token的简单变换
console.log("--- 测试2: Token变换 ---");
const tokenTests = [
    originalToken.substring(0, 40), // 取前40位
    originalToken.substring(8),     // 去掉前8位
    originalToken.split('').reverse().join(''), // 反转
];

tokenTests.forEach((test, i) => {
    console.log(`${i+1}. ${test} ${test === targetAuth ? '✓' : ''}`);
});

console.log();

// 测试3: 尝试时间戳相关
console.log("--- 测试3: 时间戳相关 ---");
// 从2024年开始的一些可能时间戳
const timestamps = [
    1704067200, // 2024-01-01
    1735689600, // 2025-01-01
    1722470400, // 2024-08-01
    1753920000, // 2025-07-29 (大概的日期)
];

timestamps.forEach(ts => {
    const tests = [
        hyid + mdid + ts,
        ts + hyid + mdid,
        hyid + ts + mdid,
    ];
    
    tests.forEach(test => {
        const result = sha1(test);
        if (result === targetAuth) {
            console.log(`✓ 找到匹配! 时间戳: ${ts}, 组合: ${test}`);
        }
    });
});

// 测试4: 尝试URL相关
console.log("--- 测试4: URL相关 ---");
const urls = [
    "/sign/sign",
    "https://mp-gp.springland.com.cn/sign/sign",
    "mp-gp.springland.com.cn/sign/sign",
];

urls.forEach(url => {
    const tests = [
        url + hyid + mdid,
        hyid + mdid + url,
        REQUEST_KEY + url + hyid + mdid + REQUEST_SECRET,
    ];
    
    tests.forEach(test => {
        const result = sha1(test);
        if (result === targetAuth) {
            console.log(`✓ 找到匹配! URL: ${url}, 组合: ${test}`);
        }
    });
});

console.log();

// 测试5: 尝试固定值
console.log("--- 测试5: 检查是否为固定值 ---");
console.log("Authorization可能是一个固定的会话token，不是通过算法生成的");

// 测试6: 尝试更复杂的组合
console.log("--- 测试6: 复杂组合 ---");
const complexTests = [
    `${REQUEST_KEY}${hyid}${mdid}${REQUEST_SECRET}`,
    `${hyid}${REQUEST_KEY}${mdid}${REQUEST_SECRET}`,
    `${REQUEST_SECRET}${hyid}${mdid}${REQUEST_KEY}`,
];

complexTests.forEach((test, i) => {
    const result = sha1(test);
    console.log(`${i+1}. ${test}`);
    console.log(`   结果: ${result} ${result === targetAuth ? '✓ 匹配!' : ''}`);
});

console.log("\n=== 结论 ===");
console.log("如果没有找到匹配，Authorization可能是:");
console.log("1. 服务器端生成的会话token");
console.log("2. 包含了我们不知道的参数（如时间戳、随机数等）");
console.log("3. 使用了不同的加密算法");
console.log("4. 是登录后服务器返回的另一个token值");
