<view class="head {{klx}}-head">
    <image class="{{klx}}-img" src="http://obs.springland.com.cn/mg-mp/image/{{klx}}.png"></image>
    <image bindtap="menuTap" class="touxiang" data-menuname="个人信息" data-url="/pages/personal-info/personal-info" src="{{hyInfo.headimg?hyInfo.headimg:defaultAvatarUrl}}" type="userAvatarUrl"></image>
    <view class="hybq-wra">
        <view class="text-white my-name" wx:if="{{hyInfo.name}}">{{hyInfo.name}}</view>
        <open-data class="text-white my-name" type="userNickName" wx:else></open-data>
        <view class="tag-prime" wx:if="{{hyInfo.isPrime==1}}">prime</view>
        <image class="tlh-img" src="/img/icon/tlhx.png" wx:if="{{hyInfo.isTlh==1}}"></image>
        <image class="tlh-img" src="/img/icon/mlhx.png" wx:if="{{hyInfo.isMlh==1}}"></image>
    </view>
    <view class="text-white my-phone" wx:if="{{mobile}}">{{mobile}}</view>
    <view class="card {{klx}}-card">
        <view class="linecard {{klx}}-line">
            <view class="incard {{klx}}-card">
                <block wx:if="{{hyInfo!=null}}">
                    <view class="card-no {{klx}}-cardno">会员卡号：{{hyInfo.hykh}}</view>
                    <span bindtap="goQrCode" class="{{klx}}-button" style="margin-left: 54%;line-height: 80rpx;padding: 10rpx;border-radius: 30rpx;font-size: 23rpx;">查看会员卡</span>
                    <span bindtap="goMyBrand" class="{{klx}}-button" style="margin-left: 10rpx;line-height: 80rpx;padding: 10rpx;border-radius: 30rpx;font-size: 23rpx;">查看联名会员</span>
                </block>
                <button bindtap="goLogin" class="{{klx}}-button" wx:else>登录/注册</button>
            </view>
        </view>
    </view>
    <view bindtap="menuTap" class="xxtx" data-menuname="消息" data-nologin="1" data-url="/home/<USER>/news/index">
        <image src="/img/newicon/xxtx.png"></image>
        <view class="jiaobiao" wx:if="{{xxNum>0}}">{{xxNum}}</view>
    </view>
    <view bindtap="menuTap" class="sztx" data-menuname="设置" data-url="/pages/personal-info/personal-info">
        <image src="/img/newicon/sztx.png"></image>
    </view>
    <view class="tlh" wx:if="{{hyInfo.isZzjk==1}}">
        <image src="/img/icon/zzjkx.png"></image>
    </view>
</view>
<view class="content">
    <list class="grid no-border col-4" style="margin-top:20rpx; border-radius: 20rpx;">
        <item bindtap="menuTap" data-type="1" data-url="/subfield/pages/my-points/my-points">
            <text class="text-black">{{jfNum}}</text>
            <text class="text-gray">积分</text>
        </item>
        <item bindtap="menuTap" data-type="1" data-url="/subfield/pages/gbk/my-vip/my-vip">
            <text class="text-black">{{gbkAmount}}</text>
            <text class="text-gray">贵宾礼卡</text>
        </item>
        <item bindtap="menuTap" data-type="1" data-url="/subfield/pages/my-value/my-value">
            <text class="text-black">{{storeCardAmount}}</text>
            <text class="text-gray">储值卡</text>
        </item>
        <item bindtap="menuTap" data-type="1" data-url="/home/<USER>/my-coupon/index?mallId=babai">
            <text class="text-black">{{yjhqnum+deduct_num+scflNum+csqNum+mdqNum+hfqNum+eleNum+weComNum+jffdzqNum+tgqNum}}</text>
            <text class="text-gray">优惠券</text>
        </item>
    </list>
    <view class="grid-title">
        <label>我的订单</label>
        <label bindtap="menuTap" data-url="/home/<USER>/myOrder/index" style="float: right;margin-right: 10rpx;">
            <text class="text-black">查看全部></text>
        </label>
    </view>
    <list class="my-order grid no-border col-4">
        <item bindtap="menuTap" data-url="/home/<USER>/myOrder/index?status=1">
            <text class="blage-in" wx:if="{{daizhifu>0}}">{{daizhifu}}</text>
            <text style="width: 40rpx;height: 40rpx;" wx:else></text>
            <image src="https://obs.springland.com.cn/mg-mp/svg/20231008001.svg"></image>
            <text class="text-black">待付款</text>
        </item>
        <item bindtap="menuTap" data-url="/home/<USER>/myOrder/index?status=3">
            <text class="blage-in" wx:if="{{daishouhuo>0}}">{{daishouhuo}}</text>
            <text style="width: 40rpx;height: 40rpx;" wx:else></text>
            <image src="https://obs.springland.com.cn/mg-mp/svg/20231008002.svg"></image>
            <text class="text-black">待收货</text>
        </item>
        <item bindtap="menuTap" data-url="/home/<USER>/myOrder/index?status=7">
            <text class="blage-in" wx:if="{{comment>0}}">{{comment}}</text>
            <text style="width: 40rpx;height: 40rpx;" wx:else></text>
            <image src="https://obs.springland.com.cn/mg-mp/svg/20231008003.svg"></image>
            <text class="text-black">待评价</text>
        </item>
        <item bindtap="menuTap" data-url="/home/<USER>/my-after-sales/index">
            <text style="width: 40rpx;height: 40rpx;"></text>
            <image src="https://obs.springland.com.cn/mg-mp/svg/20231008004.svg"></image>
            <text class="text-black">售后/退货</text>
        </item>
    </list>
    <view class="grid-title">常用功能</view>
    <list class="grid no-border col-4">
        <item bindtap="menuTap" data-nologin="1" data-url="/pages/parking/parking">
            <image src="https://obs.springland.com.cn/mg-mp/image/park.svg" style="width: 80rpx;height: 80rpx;"></image>
            <text class="text-black">停车缴费</text>
        </item>
        <item bindtap="menuTap" data-menuname="门店电话" data-nologin="1" data-url="/home/<USER>/customer/index">
            <image src="/img/newicon/kf.png"></image>
            <text class="text-black">门店电话</text>
        </item>
        <item bindtap="menuTap" data-menuname="平台客服" data-nologin="1" data-url="/qiyuPlugin/pages/chat-plugin/chat">
            <image src="/img/newicon/tsjy.png"></image>
            <text class="text-black">平台客服</text>
        </item>
        <item bindtap="menuTap" data-menuname="我的权益" data-nologin="1" data-url="/subfield/pages/my-equity/my-equity">
            <image src="/img/newicon/wdlp.png"></image>
            <text class="text-black">礼品/权益</text>
        </item>
    </list>
    <swiper autoplay="true" circular="true" class="screen-swiper square-dot" current="{{currentTap}}" duration="500" indicatorDots="true" interval="5000" wx:if="{{bannerList.length>0}}">
        <swiper-item bindtap="navPicClick" data-jumptype="{{it.jumpType}}" data-url="{{it.linkUrl}}" wx:for="{{bannerList}}" wx:for-item="it" wx:key="{{it.imgUrl}}">
            <image mode="widthFix" src="{{it.imgUrl}}?x-image-process=style/style-720x240"></image>
        </swiper-item>
    </swiper>
    <view class="grid-title">我的服务</view>
    <list class="grid no-border col-4">
        <item bindtap="menuTap" data-menuname="地址管理" data-nologin="1" data-url="/home/<USER>/address-jd/index?form=my&source=def">
            <image src="/img/newicon/dzgl.png"></image>
            <text class="text-black">地址管理</text>
        </item>
        <item bindtap="menuTap" data-menuname="奖励金" data-url="{{isFx?'/home/<USER>/bonus/index':'/home/<USER>/hft-join/hft-join'}}">
            <image src="/img/newicon/jlj.png"></image>
            <text class="text-black">奖励金</text>
        </item>
        <item bindtap="menuTap" data-menuname="收藏" data-nologin="1" data-url="/home/<USER>/collectGoods/index">
            <image src="/img/newicon/spsc.png"></image>
            <text class="text-black">收藏</text>
        </item>
        <item bindtap="menuTap" data-menuname="充值券码" data-nologin="1" data-url="/pages/recharge-code/recharge-code">
            <image src="/img/newicon/czqm.png"></image>
            <text class="text-black">充值券码</text>
        </item>
        <item bindtap="menuTap" data-menuname="零星收费" data-nologin="1" data-url="/pages/piecemeal/list">
            <image src="/img/newicon/lxsf.png"></image>
            <text class="text-black">零星收费</text>
        </item>
        <item bindtap="goMallCircle">
            <image src="/img/newicon/jfsc.png"></image>
            <text class="text-black">微信无感积分</text>
        </item>
        <item bindtap="menuTap" data-menuname="联名会员卡" data-nologin="1" data-url="/pages/my-co-brand/index">
            <image src="/img/newicon/cobrand.png"></image>
            <text class="text-black">联名会员卡</text>
        </item>
        <item bindtap="menuTap" data-menuname="签到" data-url="/sign/pages/calendar/calendar">
            <image src="https://obs.springland.com.cn/mg-mp/svg/20231008005.svg" style="width: 68rpx;height: 68rpx;"></image>
            <text class="text-black">签到</text>
        </item>
    </list>
</view>
<view class="flex flex-direction" wx:if="{{hyInfo}}">
    <button bindtap="logout" class="margin-tb-sm lg">退出登录</button>
</view>
<onlyforyou banner_pic="https://obs.springland.com.cn/obs-179f/huadi2/2020/06/1591955279899851.png" id="onlyforyou"></onlyforyou>
<view class="mallcircle" wx:if="{{isUnCommit==1}}">
    <view class="mallcircle-shadow"></view>
    <view class="mallcircle-box">
        <image class="mallcircle-img" src="https://obs.springland.com.cn/mg-mp/image/20230621145512.png"></image>
        <view bindtap="unCommitClose" class="mallcircle-qx">取消</view>
        <view bindtap="goUnCommit" class="mallcircle-kt">立即领取</view>
    </view>
</view>
