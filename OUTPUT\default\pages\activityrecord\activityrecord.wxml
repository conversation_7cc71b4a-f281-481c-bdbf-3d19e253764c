<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{curStatus==it.status?'text-blue cur cust-color':''}} " data-status="{{it.status}}" wx:for="{{titleList}}" wx:for-item="it"> {{it.name}} </item>
    </view>
</scroll-view>
<view class="padding-cust">
    <view class="item-wrapper" wx:for="{{recordList}}" wx:for-item="it">
        <view bindtap="toTicket" class="product-item padding-sm solid-bottom" data-activityinfoid="{{it.activityinfoId}}">{{it.mdName}}</view>
        <view bindtap="toTicket" class="product-item padding-sm solid-bottom" data-activityinfoid="{{it.activityinfoId}}">
            <view class="leftv">
                <view class="hyname">{{it.title}}</view>
                <view class="ctime">场次{{it.idx}}</view>
                <view class="ctime">报名时间{{it.reporttime}}</view>
            </view>
            <view class="right-con">
                <view class="pointprice" wx:if="{{it.paytype==1}}">{{it.integralnum}}积分</view>
                <view class="totalprice" wx:if="{{it.paytype==0}}">免费</view>
            </view>
        </view>
        <view class="count">
            <view class="status" wx:if="{{it.status==1}}">已报名</view>
            <view class="status" wx:if="{{it.status==2}}">已验票</view>
            <view class="status" wx:if="{{it.status==3}}">已过期</view>
            <button bindtap="cancel" class="cg-btn" data-activityuserid="{{it.activityuserId}}" wx:if="{{it.ischeck==null}}">取消报名</button>
        </view>
    </view>
    <view style="height:100rpx"></view>
</view>
