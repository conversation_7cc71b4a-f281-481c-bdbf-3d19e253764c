<scroll-view scrollX class="bg-white nav top-tab">
    <view class="flex text-center">
        <item bindtap="tabSelect" class="flex-sub {{curStatus==it.status?'text-blue cur cust-color':''}} " data-status="{{it.status}}" wx:for="{{titleList}}" wx:for-item="it"> {{it.name}} </item>
    </view>
</scroll-view>
<view class="padding-cust">
    <block wx:if="{{renderList&&renderList.length>0}}">
        <view class="item-wrapper" wx:for="{{renderList}}" wx:for-item="it" wx:key="{{it.id}}">
            <view class="product-item padding-sm solid-bottom" wx:for="{{it.items}}" wx:for-item="it2" wx:key="{{it2.id}}">
                <image class="product-img" lazyLoad="true" src="{{it2.imgurl}}?w=150&h=150"></image>
                <view class="sddd">
                    <view class="left-wrapper">
                        <view class="product-info">
                            <view class="product-title">{{it2.name}}</view>
                            <view class="product-desc">{{it2.description||''}}</view>
                        </view>
                        <view class="right-wrapper">
                            <view class="price">
                                <block wx:if="{{it2.totalprice!=null&&it2.totalprice!=0}}"> ￥{{it2.price/100}} </block>
                            </view>
                            <view class="num">x{{it2.num}}</view>
                        </view>
                    </view>
                    <view class="btn-wrapper1">
                        <button bindtap="refund" class="cg-btn" data-paymentid="{{it.id}}" data-paymentitemid="{{it2.id}}" disabled="{{disabled}}" wx:if="{{(it.status==2||it.status==4)&&it2.isRefund==0}}">退款</button>
                    </view>
                </view>
            </view>
            <view class="count">共购买{{it.items.length}}件 合计： <text class="total">￥{{it.price/100}}</text>
            </view>
            <view class="btn-wrapper solid-top" wx:if="{{it.status==1}}">
                <button bindtap="cancel" class="cg-btn" data-paymentid="{{it.id}}">取消</button>
                <button bindtap="confirm" class="zf-btn" data-paymentid="{{it.id}}">支付</button>
            </view>
        </view>
    </block>
    <view class="tab-empty" wx:else>
        <view class="tab_title">暂无订单</view>
    </view>
    <view style="height:100rpx"></view>
    <modal-pay bindpayevent="pay" disabled="{{disabled}}" isShow="{{isModalShow}}" price="{{payprice}}" saleName="{{saleName}}"></modal-pay>
</view>
