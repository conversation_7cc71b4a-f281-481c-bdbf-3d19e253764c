@import "..\..\colorui.wxss";

.chepai-con {
    background: #fff;
    padding: 30rpx 0
}

.chepai-input {
    display: flex;
    margin: 0 34rpx;
    padding: 12rpx 0
}

.chepai-img {
    flex: 0 0 70rpx
}

.chepai-img wx-image {
    height: 32rpx;
    margin-top: 20rpx;
    width: 45rpx
}

.chapai-item {
    border: 2rpx solid rgba(48,48,48,.4);
    border-radius: 8rpx;
    font-size: 40rpx;
    height: 72rpx;
    line-height: 72rpx;
    margin-right: 16rpx;
    text-align: center;
    width: 62rpx
}

.chapai-item:last-child {
    margin-right: 0
}

.xinneng<PERSON> {
    color: rgba(48,48,48,.7);
    font-size: 18rpx;
    line-height: 72rpx
}

.chepai-active {
    border: 2rpx solid #79c0ff;
    color: #79c0ff
}

.gouxuan-wrapper {
    margin-top: 66rpx;
    padding: 0 30rpx
}

.gouxuan {
    border-bottom: 1rpx solid #eee;
    display: flex;
    position: relative
}

.check-wz {
    flex: 1;
    line-height: 100rpx
}

.img-ck {
    display: inline-block;
    padding: 30rpx 12rpx;
    vertical-align: top
}

.check-wz wx-image {
    display: block;
    height: 40rpx;
    width: 40rpx
}

.zidy {
    display: block;
    line-height: 100rpx
}

.weizhi {
    margin-top: 30rpx
}

.zdy {
    background: #2196ff!important;
    border-radius: 92rpx;
    color: #fff
}

.zdy::after {
    border: none
}

.popover-view {
    background-color: #fff;
    border-radius: 6rpx;
    box-shadow: 0 0 2px 2px #ddd;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20rpx;
    position: absolute;
    top: -80rpx
}

.popover-view::before {
    border-color: #fff #fff transparent transparent;
    border-style: solid;
    border-width: 6px;
    bottom: -6px;
    box-shadow: 2px -2px 2px #ddd;
    content: "";
    display: inline-block;
    height: 0;
    position: absolute;
    transform: rotate(135deg);
    width: 0
}

.popover-view.one::before {
    left: 135px
}

.popover-view.two::before {
    left: 64px
}

.bottom-tip {
    color: #999;
    font-size: 24rpx;
    padding: 12rpx 0 24rpx;
    text-align: center
}
