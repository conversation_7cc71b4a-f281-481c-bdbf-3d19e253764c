<view catchtap="clickSubscribe" class="{{isSubscribe?'live-player-subscribe__btn live-player-hasSubscribe':'live-player-subscribe__btn live-player-notSubscribe'}}" style="width: {{width}}px; height: {{height}}px; line-height: {{height}}px; font-size: {{fontSize}}px; color: {{color}}; background-color: {{backgroundColor}};" wx:if="{{showSubscribe&&stopPropagation}}">{{isSubscribe?'取消提醒':'开播提醒'}}</view>
<view bindtap="clickSubscribe" class="{{isSubscribe?'live-player-subscribe__btn live-player-hasSubscribe':'live-player-subscribe__btn live-player-notSubscribe'}}" style="width: {{width}}px; height: {{height}}px; line-height: {{height}}px; font-size: {{fontSize}}px; color: {{color}}; background-color: {{backgroundColor}};" wx:if="{{showSubscribe&&!stopPropagation}}">{{isSubscribe?'取消提醒':'开播提醒'}}</view>
