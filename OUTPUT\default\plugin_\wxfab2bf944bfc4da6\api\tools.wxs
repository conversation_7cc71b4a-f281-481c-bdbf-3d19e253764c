var formatDate = (function(timestamp) {
  var date = getDate(timestamp * 1000);
  var y = date.getFullYear();
  var m = date.getMonth() + 1;
  m = m < 10 ? ('0' + m) : m;
  var d = date.getDate();
  d = d < 10 ? ('0' + d) : d;
  var h = date.getHours();
  h = h < 10 ? ('0' + h) : h;
  var minute = date.getMinutes();
  var second = date.getSeconds();
  minute = minute < 10 ? ('0' + minute) : minute;
  second = second < 10 ? ('0' + second) : second;
  date = m + '月' + d + '日' + h + ':' + minute;
  return (date)
});
var formatPrice = (function(price) {
  price = price / 100;
  return (price.toFixed(2))
});
var charAtFirst = (function(v) {
  return (v.charAt(0))
});
module.exports = ({
  formatDate: formatDate,
  formatPrice: formatPrice,
  charAtFirst: charAtFirst,
});