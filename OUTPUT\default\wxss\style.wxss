page {
    background: #f4f4f4
}

.clearfix:after {
    clear: both;
    content: "";
    display: block
}

wx-sup {
    vertical-align: text-top
}

wx-sub,wx-sup {
    font-size: 20rpx
}

wx-sub {
    vertical-align: text-bottom
}

.dialog_content {
    background: #fff;
    border-radius: 12rpx;
    padding: 40rpx 48rpx;
    position: relative
}

.dialog_top {
    display: block;
    height: 148rpx;
    margin: 0 auto -30rpx;
    position: relative;
    width: 144rpx;
    z-index: 10
}

.dialog_title {
    color: #333;
    display: block;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
    margin-bottom: 22rpx;
    margin-top: 16rpx;
    text-align: center
}

.dialog_int {
    border-bottom: 1rpx solid #ececec;
    height: 56rpx;
    line-height: 56rpx;
    margin-bottom: 20rpx;
    margin-top: 56rpx;
    width: 100%
}

.dialog_text {
    color: red;
    height: 30rpx;
    line-height: 30rpx;
    margin-bottom: 28rpx
}

.dialog_btn {
    background: #fff;
    border: none;
    border-radius: 8rpx;
    font-size: 32rpx;
    height: 80rpx;
    line-height: 80rpx;
    margin: 0;
    padding: 0;
    width: 100%
}

.dialog_btn_no {
    background: #fff;
    color: #999
}

wx-button::after {
    border: none
}

.dialog_two {
    top: 25%
}

.dialog_subtitle {
    color: #999;
    display: block;
    line-height: 40rpx;
    margin-bottom: 38rpx;
    text-align: center
}

.dialog_bg {
    position: relative
}

.dialog_bg_img {
    display: block;
    margin: 0 auto;
    width: 440rpx
}

.dialog_bg_text {
    bottom: 70rpx;
    color: #fff;
    font-size: 32rpx;
    font-weight: 700;
    height: 44rpx;
    left: 0;
    line-height: 44rpx;
    position: absolute;
    text-align: center;
    width: 100%
}

.mask {
    background: rgba(0,0,0,.7);
    bottom: 0;
    right: 0;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    z-index: 1000
}

.mask,.preventTouchMove {
    left: 0;
    position: fixed;
    top: 0
}

.preventTouchMove {
    height: 100%;
    overflow: hidden;
    width: 100%;
    z-index: 0
}
