<block wx:if="{{plateNoList&&plateNoList.length>0}}">
    <button bindtap="noBindChepai" class="wxzff" wx:if="{{hasWx}}">
        <text class="icon-settings text-margin-left"></text> 微信支付分停车管理</button>
    <view class="list-item" wx:for="{{plateNoList}}">
        <view class="shang solid-bottom">{{item.plateNo}} <text wx:if="{{item.isDefault==1}}">默认车牌</text>
            <view bindtap="tapSetDefault" class="shang-szmr" data-index="{{index}}" wx:else>设置默认</view>
        </view>
        <view class="caozuo">
            <view class="caozuo-left">
                <block wx:if="{{isAuto==1}}">
                    <view class="caozuo-gouxuan" wx:if="{{item.isRelief==1}}">自动使用减免 <image src="/img/icon/gouxuan.png"></image>
                    </view>
                    <view bindtap="chooseRelief" class="caozuo-gou" data-index="{{index}}" wx:else>
                        <text>+ 自动使用减免</text>
                    </view>
                    <view class="caozuo-gouxuan" wx:if="{{item.isJf==1}}">积分自动抵扣 <image src="/img/icon/gouxuan.png"></image>
                    </view>
                    <view bindtap="chooseJf" class="caozuo-gou" data-index="{{index}}" wx:else>
                        <text>+ 积分自动抵扣</text>
                    </view>
                    <view class="caozuo-gouxuan" wx:if="{{item.isWx==1}}">微信无感支付 <image src="/img/icon/gouxuan.png"></image>
                    </view>
                </block>
            </view>
            <view bindtap="shanchu" class="ddd" data-index="{{index}}">
                <image src="/img/icon/shanchu.png"></image>
                <text class="jiebang"> 解绑</text>
            </view>
        </view>
    </view>
</block>
<view class="bottom" wx:else>- 您暂时还没有车牌呢 -</view>
<view class="weizhi padding flex flex-direction">
    <button bindtap="addChepai" class="zdy lg">添加车牌</button>
</view>
