<list class="menu menu-avatar no-padding">
    <item class="item-padding" wx:for="{{couponList}}">
        <view bindtap="tapDetail" class="item-wrapper" data-index="{{index}}" wx:if="{{item.hdlx==3}}">
            <view class="left-info">
                <view class="couponitem">
                    <view class="bg">
                        <image src="{{item.imgSrc}}"></image>
                    </view>
                    <view class="access" wx:if="{{item.needPrime==1}}">Prime会员专享</view>
                    <view class="noaccess" wx:else>普通会员</view>
                </view>
                <view class="main-info">
                    <view class="info-title">{{item.hdsm}}</view>
                    <view class="price">￥{{item.gmje}}<text class="txtaa">￥{{item.gmje+item.zqje}}</text>
                    </view>
                </view>
            </view>
            <view class="right-info">
                <view catchtap="couponSubSure" data-index="{{index}}">
                    <user-info-btn hasPhone="{{hasPhone}}">
                        <view class="shiji">
                            <button class="my-button {{!hasPhone||item.canJoin==1?'':'unuse'}}">{{!hasPhone||item.canJoin==1?'购买':item.reason}}</button>
                            <view class="info-content" wx:if="{{item.xskcs==1}}">剩余{{item.kcs}}份</view>
                        </view>
                    </user-info-btn>
                </view>
            </view>
        </view>
        <view bindtap="tapDetail" class="item-wrapper" data-index="{{index}}" wx:else>
            <view class="left-info">
                <view class="couponitem">
                    <view class="bg">
                        <image src="/img/icon/quan_bg.png"></image>
                    </view>
                    <view class="access" wx:if="{{item.needPrime==1}}">Prime会员专享</view>
                    <view class="noaccess" wx:else>普通会员</view>
                    <view class="type">{{item.yxflmc}}</view>
                    <view class="price" wx:if="{{isQb}}">￥{{item.ye}}</view>
                    <view class="price" wx:elif="{{item.hdlx==1}}">￥{{item.mz}}</view>
                    <view class="price" wx:elif="{{item.hdlx==2}}">￥{{item.je}}</view>
                </view>
                <view class="main-info">
                    <view class="info-title">{{item.sysm}}</view>
                    <view class="info-content" wx:if="{{item.xskcs==1}}">库存：{{item.kcs}}</view>
                </view>
            </view>
            <view class="right-info">
                <view catchtap="couponSubSure" data-index="{{index}}">
                    <user-info-btn hasPhone="{{hasPhone}}">
                        <view class="shiji">
                            <button class="my-button {{!hasPhone||isUse&&(item.canJoin||item.canJoin==undefined)?'':'unuse'}}">{{!item.hdlx?btnText:hasPhone&&item.canJoin!=1?item.reason:item.hdlx==1?'领取':item.hdlx==2?'积分兑换':item.hdlx==4?'领取':btnText}}</button>
                        </view>
                    </user-info-btn>
                </view>
            </view>
        </view>
    </item>
</list>
<modal-pay-coupon bindnumMinus="numMinus" bindnumPlus="numPlus" bindpayevent="pay" canBuy="{{canBuy}}" disabled="{{disabled}}" isShow="{{isModalShow}}" num="{{couponList[selectIndex].qty}}" price="{{couponList[selectIndex].gmje}}" saleName="{{couponList[selectIndex].bt}}"></modal-pay-coupon>
