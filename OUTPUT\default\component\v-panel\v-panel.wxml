<view class="vehicle-panel" style="height:430rpx;background-color:{{backgroundColor}}" wx:if="{{isShow}}">
    <block wx:if="{{keyBoardType===1}}">
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyVehicle1}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyVehicle2}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyVehicle3}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
        <view class="vehicle-panel-row-last">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button vehicle-panel-row-button-last" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyVehicle4}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
    </block>
    <block wx:else>
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button vehicle-panel-row-button-number" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyNumber}}" wx:for-index="idx" wx:key="item">{{item}}</view>
        </view>
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInput1}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
            <view bindtap="vehicleTapF" class="vehicle-panel-row-button {{qty==7||qty==6?'':'zhihui'}}" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInputF1}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
        <view class="vehicle-panel-row">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInput2}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
            <view bindtap="vehicleTapF" class="vehicle-panel-row-button {{qty==7||qty==6?'':'zhihui'}}" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInputF2}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
        </view>
        <view class="vehicle-panel-row-last">
            <view bindtap="vehicleTap" class="vehicle-panel-row-button vehicle-panel-row-button-last" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInput3}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
            <view bindtap="vehicleTapF" class="vehicle-panel-row-button vehicle-panel-row-button-last {{qty==7||qty==6?'':'zhihui'}}" data-value="{{item}}" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}" wx:for="{{keyEnInputF3}}" wx:for-index="idx" wx:key="idx">{{item}}</view>
            <view class="vehicle-panel-row-button" hoverClass="vehicle-hover" hoverStartTime="10" hoverStayTime="100" style="border:{{buttonBorder}}">
                <image bindtap="vehicleTap" class="vehicle-en-button-delete" data-value="delete" mode="aspectFit" src="../../img/icon/sc.png"></image>
            </view>
        </view>
    </block>
</view>
