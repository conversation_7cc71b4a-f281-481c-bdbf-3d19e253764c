.clearfix:after {
    clear: both;
    content: "";
    display: block
}

.mask {
    background: rgba(0,0,0,.7)
}

wx-sup {
    vertical-align: text-top
}

wx-sub,wx-sup {
    font-size: 20rpx
}

wx-sub {
    vertical-align: text-bottom
}

.mask {
    background: rgba(0,0,0,.6);
    bottom: 0;
    right: 0;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    z-index: 1000
}

.mask,.preventTouchMove {
    left: 0;
    position: fixed;
    top: 0
}

.preventTouchMove {
    height: 100%;
    overflow: hidden;
    width: 100%;
    z-index: 0
}

.dialog {
    background: #fff;
    border-radius: 20rpx;
    height: 542rpx;
    left: 50%;
    margin-left: -254rpx;
    position: fixed;
    top: 378rpx;
    width: 508rpx;
    z-index: 1001
}

.dialog_image {
    display: block;
    margin: -125rpx auto 36rpx;
    width: 256rpx
}

.dialog_cou {
    color: #282828;
    font-size: 36rpx;
    line-height: 50rpx;
    margin-bottom: 78rpx;
    text-align: center
}

.dialog_btn {
    background: linear-gradient(270deg,#f1debf,#d5a978);
    border-radius: 54rpx;
    color: #fff;
    height: 86rpx;
    line-height: 86rpx;
    margin: 0 auto;
    text-align: center;
    width: 412rpx
}

.mtop200 {
    margin-top: 200px
}

.disno {
    display: none
}
