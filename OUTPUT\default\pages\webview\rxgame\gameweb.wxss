@import "..\..\..\colorui.wxss";

page {
    background: #f8f8f8
}

.img-bbb {
    display: block;
    height: 400rpx;
    width: 750rpx
}

.btn-wrapper {
    margin-top: 100rpx;
    text-align: center
}

.yanse {
    background: #1aac19!important;
    border-radius: 10rpx;
    color: #fff;
    font-size: 30rpx;
    width: 600rpx
}

.yanse wx-image {
    height: 50rpx;
    width: 50rpx
}

wx-dialog {
    border-radius: 0;
    width: 604rpx
}

.modal-wrapper {
    background: #fff;
    padding-top: 22rpx;
    width: 604rpx
}

.tishi {
    font-size: 32rpx;
    line-height: 58rpx
}

.miaoshu {
    color: #aaa;
    font-size: 28rpx;
    line-height: 42rpx;
    padding: 18rpx 52rpx
}

.btn-bb {
    color: #3f882b;
    font-size: 32rpx;
    height: 80rpx;
    line-height: 80rpx;
    width: 100%
}

.we<PERSON><PERSON>-s<PERSON><PERSON> {
    color: #1aac19;
    font-size: 30rpx;
    line-height: 50rpx;
    margin-top: 32rpx
}

.xieyi {
    bottom: 10rpx;
    left: 10rpx;
    position: absolute
}

.link {
    color: #1e90ff
}
