<view class="mask"></view>
<view class="main-pwd-box main-bg-color" style="top:{{height}}">
    <view class="desc-title">{{title}}</view>
    <view bindtap="pwdInpt" class="desc-box">
        <view class="pwd-box">
            <view class="pwd-item" wx:for="{{6}}" wx:key="index"> {{!dotList[index]?'':showPwd?codeList[index]:dotList[index]}} </view>
        </view>
        <view class="input-box">
            <input adjustPosition="{{false}}" bindblur="bindblur" bindfocus="bindfocus" bindinput="bindKeyInput" class="weui-input" focus="{{focusInput}}" maxlength="6" type="password" value="{{inputCode}}"></input>
        </view>
    </view>
    <view class="btnBox">
        <button bindtap="onCancel" class="myBtn cancel" style="width: 40%;">取消</button>
        <button bindtap="onChecking" class="myBtn checking" style="width: 40%;">确认</button>
    </view>
</view>
