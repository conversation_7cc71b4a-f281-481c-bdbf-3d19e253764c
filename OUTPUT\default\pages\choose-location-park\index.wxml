<view class="hd">
    <view bindtap="cityList" class="add">
        <image class="hd_icon" src="/yjh-config/images/choose_icon.png"></image>
        <text>{{city_name}}</text>
        <image class="hd_arrow" src="/yjh-config/images/choose_arrow.png"></image>
    </view>
    <view class="search">
        <input bindinput="searchMall" class="head_seach" placeholder="搜索门店"></input>
        <image class="head_seach_icon" mode="widthFix" src="/yjh-config/images/search.png"></image>
    </view>
</view>
<view class="con" hidden="{{showMall}}">
    <view bindtap="setMdid" class="list" data-index="{{item.store_id}}" wx:for="{{show_mall_list}}" wx:for-index="idx" wx:key="key">
        <view class="list_name">{{item.name}} <text wx:if="{{idx==0}}">离我最近</text>
        </view>
        <view class="list_add">{{item.distance}}km l {{item.district_name}}-{{item.address}}</view>
    </view>
</view>
<view class="content" hidden="{{showCity}}">
    <view class="city" wx:for="{{city_list}}" wx:key="key">
        <view class="city_name">{{item.initial}}</view>
        <view bindtap="setCity" class="city_list" data-index="{{value}}" wx:for="{{item.city}}" wx:for-item="value" wx:key="ke">
            <view class="city_add">{{value}}</view>
        </view>
    </view>
</view>
