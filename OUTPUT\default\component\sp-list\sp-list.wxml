<view class="waterfall-con">
    <view bindtap="tapSeeMore" class="waterfall-item ad-item" wx:if="{{groupImgurl}}">
        <image class="sp-group-url ad-img" mode="widthFix" src="{{filter.addImgPath(groupImgurl)}}"></image>
    </view>
    <view bindtap="goodsDetail" class="waterfall-item sp-item shadow" data-goods="{{item}}" wx:for="{{list}}" wx:key="id">
        <view class="sp-img-wra">
            <image lazyLoad class="sp-img" src="{{filter.addImgPath350x350(item.url)}}"></image>
            <image class="sp-freeze" mode="heightFix" src="{{filter.addImgPath(item.freezeUrl)}}" wx:if="{{item.freezeUrl}}"></image>
            <image class="sp-sale" mode="heightFix" src="{{filter.addImgPath(item.saleUrl)}}" wx:if="{{item.saleUrl}}"></image>
        </view>
        <view class="sp-name">
            <view class="tags">
                <text class="tag" style="background: {{tag.colour}}" wx:for="{{item.smGoodsSellLabelVoList}}" wx:for-item="tag" wx:key="index">{{tag.name}}</text>
            </view> {{item.goodsName}} <view class="jinyouji" wx:if="{{item.mailOnly==1}}">仅邮寄</view>
            <view class="jinyouji" wx:elif="{{item.mailOnly==2}}">仅自提</view>
            <view class="jinyouji" wx:elif="{{item.mailOnly==3}}">仅配送</view>
        </view>
        <view class="sp-desption" wx:if="{{item.sellPoint}}">{{item.sellPoint}}</view>
        <view class="manjian-wrapper">
            <view class="sp-manjian" wx:if="{{item.preSaleId}}">预售</view>
            <view class="sp-manjian" wx:if="{{item.markingPrice>item.sellPrice||item.activityPrice&&item.markingPrice>item.activityPrice}}">省{{filter.priceToNoFixed2(item.activityPrice?item.markingPrice-item.activityPrice:item.markingPrice-item.sellPrice)}}元</view>
            <view class="sp-manjian" wx:if="{{item.couponFlag>0}}">券</view>
            <view class="sp-manjian" wx:if="{{item.activityTag}}">{{item.activityTag}}</view>
            <view class="sp-manjian" wx:if="{{item.setMealTag&&!item.preSaleId}}">{{item.setMealTag}}</view>
            <view class="sp-manjian" wx:if="{{item.suitTag&&!item.preSaleId}}">{{item.suitTag}}</view>
        </view>
        <view class="sp-team">
            <view>
                <view class="sp-xj">￥{{filter.priceToFixed2(item.activityPrice?item.activityPrice:item.sellPrice)}} <text class="sp-yj" wx:if="{{item.markingPrice>item.sellPrice||item.activityPrice&&item.markingPrice>item.activityPrice}}">￥{{filter.priceToFixed2(item.markingPrice)}}</text>
                </view>
            </view>
            <view catchtap="addCartFire" class="sp-add" data-item="{{item}}">
                <image src="/img/smicon/tianjia.png" wx:if="{{(item.stock-item.confirmStock-item.freezeStock>0||item.mailOnly==1&&item.stock==-1)&&item.status!=3}}"></image>
                <image src="/img/smicon/tianjia-grey.png" wx:else></image>
            </view>
        </view>
        <view catchtap="goToRankList" class="rank-view" data-category="{{item.categoryName||'本类'}}" data-category-id="{{item.rankId}}" data-rank-type="{{item.rankType}}" wx:if="{{item.categoryName}}">
            <view class="rank-left">
                <image class="rank-icon" mode="heightFix" src="/supermarket/img/smicon/class-top.png"></image>
            </view>
            <view class="rank-middle">
                <text class="rank-text">{{item.categoryName||'本类'}}<text wx:if="{{item.rank}}">第</text>
                </text>
                <text class="rank-number" wx:if="{{item.rank}}">{{item.rank}}</text>
                <text class="rank-text" wx:if="{{item.rank}}">名</text>
            </view>
            <view class="rank-right">
                <text class="rank-arrow">></text>
            </view>
        </view>
    </view>
</view>
<goods-spec bind:changeCartNum="changeCartNum" bind:chooseGuige="chooseGuige" bind:hideSpec="hideSpec" goodsInfo="{{goodsInfo}}" paramData="{{paramData}}" specShow="{{specShow}}"></goods-spec>

<wxs module="filter" src="..\..\supermarket-utils\filter.wxs"/>