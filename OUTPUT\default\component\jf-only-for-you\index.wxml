<view class="other_content" style="margin-bottom:{{margin_bottom}};">
    <view class="only_title">
        <image class="only_bg" mode="widthFix" src="{{banner_pic}}"></image>
    </view>
    <scroll-view scrollX class="tab">
        <view bindtap="tabFun" class="{{tabArr.curHdIndex=='0'?'active':''}}" data-id="0" data-nid="0" data-type="1" id="tab-hd01">全部</view>
        <view bindtap="tabFun" class="nav-item {{tabArr.curHdIndex==idx?'active':''}}" data-id="{{idx}}" data-nid="{{navItem.type_one_id}}" data-type="1" id="tab-hd{{idx}}" wx:for="{{navData}}" wx:for-index="idx" wx:for-item="navItem" wx:key="idx">{{navItem.name}}</view>
    </scroll-view>
    <view class="tab2">
        <view catchtap="change_tab" class="tab_flex {{select_tab_id==2?'bg_red':''}}" data-id="2" data-search_field="sales_num_down">
            <text>兑换排行</text>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='4'?'bg_red':''}}" data-id="4" data-search_field="price_down" hidden="{{select_tab_id>=4&&select_tab_id<7}}">
            <view> 积分值 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon01.png"></image>
            </view>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='5'?'bg_red':''}}" data-id="5" data-search_field="price_down" hidden="{{!(select_tab_id==5)}}">
            <view> 积分值 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon02.png"></image>
            </view>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='6'?'bg_red':''}}" data-id="6" data-search_field="price_up" hidden="{{!(select_tab_id==6)}}">
            <view> 积分值 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon03.png"></image>
            </view>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='7'?'bg_red':''}}" data-id="7" data-search_field="create_time_desc" hidden="{{select_tab_id>=7&&select_tab_id<10}}">
            <view> 最新 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon01.png"></image>
            </view>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='8'?'bg_red':''}}" data-id="8" data-search_field="create_time_desc" hidden="{{!(select_tab_id==8)}}">
            <view> 最新 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon02.png"></image>
            </view>
        </view>
        <view catchtap="change_tab" class="tab_flex  {{select_tab_id=='9'?'bg_red':''}}" data-id="9" data-search_field="create_time_asc" hidden="{{!(select_tab_id==9)}}">
            <view> 最新 <image class="price_cheap" mode="widthFix" src="/yjh-config/images/home_icon03.png"></image>
            </view>
        </view>
    </view>
    <view class="mall_content" wx:if="{{goods_list.length!=0}}">
        <view class="mall_con clearfix">
            <view class="mall_list" wx:for="{{goods_list}}" wx:key="key">
                <navigator bindtap="we_click" class="we-prod-card" data-id="{{item.id}}" data-item="{{item}}" data-scene="jf_recommend_api_item" url="/jf/pages/mallGoodsDetails/index?goods_detail_id={{item.id}}&base_id={{item.base_id}}">
                    <view class="goods_img">
                        <image class="mall_img" src="{{item.cover_img}}"></image>
                        <image class="mall_img back_img" src="{{item.back_img}}" wx:if="{{item.back_img!=''}}"></image>
                        <block wx:if="{{item.is_over==1}}">
                            <image class="mall_img back_img" src="https://obs.springland.com.cn/mg-mp/image/20221121174119.png"></image>
                            <text class="over_price">￥{{item.price}}</text>
                            <text class="over_del">￥{{item.del_price}}</text>
                            <text class="over_jf">{{item.rule}}</text>
                        </block>
                    </view>
                    <view class="mall_title">{{item.name}}</view>
                    <view class="mall_point">{{item.rule}}积分<text>{{item.market_price}}</text>
                    </view>
                    <view class="mall_num">已兑{{item.jf_sales}}件</view>
                </navigator>
            </view>
        </view>
    </view>
    <view class="bottom" wx:else>- 暂无商品 -</view>
</view>
