<view class="top-wrapper">
    <view class="ewm-wrapper">
        <image class="canvas" src="{{gift.goodsUrl}}"></image>
    </view>
</view>
<view class="margin-top"></view>
<view class="des-wrapper">
    <view class="content">
        <view class="title">{{gift.benefitName}}</view>
        <view class="descr">取货地址：</view>
        <view class="descr">{{gift.position}}</view>
        <view class="descr">取货方式：派样机扫码取货</view>
    </view>
</view>
<view class="des-wrapper">
    <view class="content">
        <view class="title">使用说明</view>
        <wxparser richText="{{gift.useInfo}}"></wxparser>
    </view>
</view>
<view class="bottom_fixed">
    <view catchtap="pickup" class="bottom_btn {{btnUse?'':'unuse'}}">{{btnStr}}</view>
    <view catchtap="refund" class="bottom_btn " data-id="{{id}}" wx:if="{{(btnStr=='已过期'||btnStr=='扫码取货')&&gift.refundStatus==null}}">申请退款</view>
</view>
