<view class="select-tag">
    <block wx:for="{{tags}}" wx:key="index">
        <view bindtap="onClickSelectType" class="my-tag  {{item.checked?'selected':''}}" data-checked="{{item.checked}}" data-index="{{index}}" data-listindex="{{listindex}}" data-optionid="{{item.optionId}}" wx:if="{{!compare&&item.isBad!=1}}"> {{item.caption}} </view>
        <view bindtap="onClickSelectType" class="my-tag  {{item.checked?'selected':''}}" data-index="{{index}}" data-listindex="{{listindex}}" data-optionid="{{item.optionId}}" wx:if="{{compare&&item.isBad==1}}"> {{item.caption}} </view>
    </block>
</view>
