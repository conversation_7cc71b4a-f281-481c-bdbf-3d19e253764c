<view class="plugin-mall-score exposure-report-dom" data-exposure-data="{{({event_id:100000,trade_state:reportTradeState,parking_state:reportParkingState})}}" id="plugin-mall-score" wx:if="{{showPage}}">
    <image class="plugin-mall-score-bg" mode="aspectFill" src="https://gtimg.wechatpay.cn/resource/xres/wechat_pay_system/business_operation/mch_circle_plugin/image/card_header_bg_v3.png"></image>
    <navigate-bar title="{{brandName}}"></navigate-bar>
    <view class="mall-score">
        <image class="mall-score-mask" mode="aspectFill" src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images/card_mask.png"></image>
        <image class="mall-card-bg" mode="aspectFill" src="{{memberInfo.cardBackground}}"></image>
        <view class="mall-score-box">
            <view class="mall-store-top">
                <image class="mall-logo" mode="aspectFill" src="{{memberInfo.cardLogo}}"></image>
                <view class="mall-card mall-card-name">{{memberInfo.cardName}}</view>
                <image catchtap="showServeManage" class="mall-card-menu" mode="aspectFill" src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images/icon-more.png"></image>
            </view>
            <view class="points">我的积分</view>
            <view class="mall-store-bottom">
                <view catchtap="toMiniProgram" class="mall-points exposure-report-dom" data-exposure-data="{{({event_id:100001})}}" data-miniprogram="{{pointsList}}" data-report-data="{{({event_id:2002})}}" id="mall-points">
                    <score-scroll score="{{memberInfo.points}}"></score-scroll>
                </view>
                <view catchtap="toMiniProgram" class="points-shop exposure-report-dom" data-exposure-data="{{({event_id:100002})}}" data-miniprogram="{{pointsMall}}" data-report-data="{{({event_id:2001})}}" id="points-shop" wx:if="{{pointsMall.appid&&pointsMall.path}}">积分商城</view>
            </view>
        </view>
    </view>
    <view class="box-card">
        <view class="content-box-container m32 score-container">
            <view class="content-box-header">
                <view class="content-box-title">今日快速积分</view>
                <button class="points-btn" size="mini" type="primary" wx:if="{{showOneKeyPointsBtn}}">一键积分</button>
            </view>
            <view class="content-box-body">
                <view class="points-list" wx:if="{{pointsBillList.length}}">
                    <view class="point-item" wx:if="{{index<3&&!isExpandAllBill||index<100&&isExpandAllBill}}" wx:for="{{pointsBillList}}" wx:key="index">
                        <image class="point-item-logo" mode="aspectFill" src="{{item.logo}}" wx:if="{{item.logo}}"></image>
                        <view class="point-item-logo logo-bg" wx:else>{{item.store_name&&item.store_name[0]}}</view>
                        <view class="point-item-info">
                            <view class="point-item-name">{{item.store_name}}</view>
                            <view class="point-item-price">
                                <view class="point-item-price-type">￥</view>
                                <view class="point-item-price-num">{{item.amount||0}}</view>
                            </view>
                        </view>
                        <view class="point-item-num" wx:if="{{item.status===1}}">+{{item.reply_points||0}}</view>
                        <button catch:tap="applyPoints" class="points-btn" data-point-item="{{item}}" size="mini" type="primary" wx:elif="{{item.status===2}}">积分</button>
                        <view class="point-item-state" wx:else>{{pointStatusText[item.status]||''}}</view>
                    </view>
                    <view class="expand-all" wx:if="{{pointsBillList.length>3}}">
                        <view catch:tap="expandAllPointBill" class="expand-all-box">
                            <view class="expand-all-text">{{isExpandAllBill?'收起':'展开全部'}}</view>
                            <view class="{{isExpandAllBill?'arrow-up':'arrow-down'}}"></view>
                        </view>
                    </view>
                </view>
                <view class="points-empty" wx:else>
                    <image class="search-icon" mode="aspectFill" src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images/icon-empty.png"></image>
                    <view class="empty-text">今日暂无微信支付消费积分</view>
                </view>
            </view>
        </view>
        <view class="content-box-container m32 parking-service-container">
            <view class="content-box-header">
                <view class="content-box-title">会员出行服务</view>
            </view>
            <view class="content-box-body">
                <view class="parking-title">停车权益</view>
                <view class="parking-serve" wx:if="{{carList.length}}">
                    <view class="parking-item" wx:for="{{carList}}" wx:key="index">
                        <block wx:if="{{item.plateNoHide}}">
                            <view class="parking-name parking-no-replace">{{item.plateNoReplace}}</view>
                            <image catchtap="showParkingHiddenTips" class="parking-name-hidden-tips" mode="aspectFill" src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images/icon-tips.png"></image>
                        </block>
                        <block wx:else>
                            <view class="parking-name">{{item.parkingNo}}</view>
                            <view class="parking-state">{{parkingStatusText[item.parkingState]}}</view>
                        </block>
                        <view catch:tap="handleShowParkingDestDialog" class="parking-des" data-parking-car="{{item}}" data-report-data="{{({event_id:3001,parking_state:reportParkingState})}}" wx:if="{{parkingBenefitsList.length}}">可享{{parkingBenefitsList.length}}项停车权益</view>
                    </view>
                </view>
                <view class="parking-empty" wx:else>
                    <view catch:tap="handleShowParkingDestDialog" class="parking-box" data-report-data="{{({event_id:3001,parking_state:reportParkingState})}}" wx:if="{{parkingBenefitsList.length}}">
                        <text class="parking-tips">{{memberInfo.level}}可享{{parkingBenefitsList.length}}项会员停车权益</text>
                        <view class="arrow-left"></view>
                    </view>
                    <text class="parking-empty-car" wx:if="{{parkingBenefitsList.length===0}}">暂无停车记录</text>
                </view>
                <travel-coupon bind:openCouponInfoDialog="openCouponInfoDialog" id="travelCoupon" mchCode="{{mchCode}}" openId="{{openid}}" tradeStatus="{{tradeStatus}}"></travel-coupon>
            </view>
        </view>
        <store-contact bind:invokeGuideList="invokeGuideList" bind:invokeGuideWechat="invokeGuideWechat" mchCode="{{mchCode}}" openId="{{openid}}"></store-contact>
    </view>
    <half-dialog bind:dialogClose="closeHalfDialog" data-dialog="showParkingDestDialog" show="{{showParkingDestDialog}}" title="停车权益">
        <view class="plugin-parking-dialog-box">
            <view class="plugin-parking-dialog-box-top" hoverClass="none" hoverStopPropagation="false">
                <view class="plugin-parking-dialog-item" wx:for="{{parkingBenefitsList}}" wx:key="index">
                    <image class="plugin-parking-dialog-img" mode="aspectFill" src="https://gtimg.wechatpay.cn/pay_h5/payment_points/images{{item.type===1?'/member_card_auth.png':'/member_basic_auth.png'}}"></image>
                    <view class="plugin-parking-dialog-des">{{item.desc}}</view>
                    <view catch:tap="toMiniProgram" class="plugin-parking-exchange {{index===0?'exposure-report-dom':''}}" data-exposure-data="{{({event_id:100005,parking_state:reportParkingState})}}" data-miniprogram="{{item}}" data-report-data="{{({event_id:3004,parking_state:reportParkingState})}}" id="plugin-parking-exchange-{{index}}" wx:if="{{item.appid&&item.path}}">去兑换</view>
                </view>
            </view>
            <view class="plugin-parking-pay-wraper">
                <button catch:tap="toMiniProgram" class="plugin-parking-pay-btn exposure-report-dom" data-exposure-data="{{({event_id:100003,parking_state:reportParkingState})}}" data-miniprogram="{{parkingPay}}" data-report-data="{{({event_id:3002,parking_state:reportParkingState})}}" id="plugin-parking-pay-btn" type="primary" wx:if="{{parkingPay.appid&&parkingPay.path}}">去缴费</button>
                <view catch:tap="toMiniProgram" class="bind-car exposure-report-dom" data-exposure-data="{{({event_id:100004,parking_state:reportParkingState})}}" data-miniprogram="{{carManage}}" data-report-data="{{({event_id:3003,parking_state:reportParkingState})}}" id="plugin-bind-car" wx:if="{{carManage.appid&&carManage.path}}">绑定车辆</view>
            </view>
        </view>
    </half-dialog>
    <half-dialog show="{{showExitPluginDialog}}" showClose="{{false}}">
        <view class="plugin-exit-box">
            <icon class="warn-icon" size="102rpx" type="warn"></icon>
            <view class="warn-msg">{{errorMsg}}</view>
            <navigator class="warn-btn" hoverClass="navigator-hover" openType="exit" target="miniProgram">知道了</navigator>
        </view>
    </half-dialog>
    <half-dialog bind:dialogClose="closeHalfDialog" data-dialog="showAuthSystemLocationDialog" show="{{showAuthSystemLocationDialog}}" title="获取位置信息失败">
        <view class="dialog-bd">
            <view class="dialog-bd__desc">解决方法：</view>
            <view class="dialog-bd__desc">1、检查手机定位功能是否开启</view>
            <view class="dialog-bd__desc">2、检查是否允许微信使用定位功能</view>
        </view>
        <view class="dialog-ft">
            <button bindtap="closeSystemLocationDialog" class="close-btn" size="mini" type="primary">关闭</button>
        </view>
    </half-dialog>
    <half-dialog bind:dialogClose="closeHalfDialog" data-dialog="showAuthAppLocationDialog" show="{{showAuthAppLocationDialog}}" title="获取位置信息失败">
        <view class="dialog-bd">
            <view class="dialog-bd__desc text-center">请前往设置打开位置信息授权后重试</view>
        </view>
        <view class="dialog-ft">
            <button bindtap="closeAppLocationDialog" class="close-btn" openType="openSetting" size="mini" type="primary"> 前往授权 </button>
        </view>
    </half-dialog>
    <travel-coupon-detail bind:noticeUpdateCouponList="updateCouponList" id="travelCouponDetail" mchCode="{{mchCode}}" openId="{{openid}}" tradeStatus="{{tradeStatus}}"></travel-coupon-detail>
    <store-guide-list bind:invokeGuideWechat="invokeGuideWechat" id="storeGuideList" mchCode="{{mchCode}}" openId="{{openid}}"></store-guide-list>
    <store-guide-detail id="storeGuideDetail" mchCode="{{mchCode}}" openId="{{openid}}"></store-guide-detail>
</view>
