.fadeIn {
    -webkit-animation: fadeInCommon .2s 1 forwards;
    animation: fadeInCommon .2s 1 forwards
}

.fadeOut {
    -webkit-animation: fadeOutCommon .2s 1 forwards;
    animation: fadeOutCommon .2s 1 forwards
}

@-webkit-keyframes fadeIn<PERSON>ommon {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInCommon {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes fadeOutCommon {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0);
        opacity: 0
    }
}

@keyframes fadeOut<PERSON>ommon {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0);
        opacity: 0
    }
}