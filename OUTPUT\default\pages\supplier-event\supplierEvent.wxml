<bar class="search">
    <view class="search-form round">
        <text class="icon-search"></text>
        <input bindinput="searchIcon" confirmType="search" placeholder="搜索" type="text"></input>
    </view>
</bar>
<view class="solids-bottom padding-xs flex align-center" wx:if="{{gysEventList.length==0}}">
    <view class="flex-sub text-center">
        <view class="solid-bottom text-xl padding">
            <text class="text-black text-bold">暂无数据</text>
        </view>
    </view>
</view>
<card class="dynamic {{isCard?'no-card':''}}">
    <item class="shadow" wx:if="{{it.isShow}}" wx:for="{{gysEventList}}" wx:for-item="it">
        <list class="menu">
            <item>
                <view class="content">
                    <text>{{it.deptName}}</text>
                    <text bindtap="phoneCall" class="icon-phone margin-left" data-phone="{{it.phoneNumber}}" wx:if="{{it.phoneNumber}}"></text>
                </view>
                <view class="action">
                    <text class="text-grey">{{it.timeFromNow}}</text>
                </view>
            </item>
        </list>
        <view class="text-content text-grey"> {{it.detail}} </view>
        <view class="grid col-1 flex-sub padding-lr" wx:if="{{it.fileList.length==1}}">
            <view class="bg-img {{isCard?'':'only-img'}}" style="background-image:url({{file}});" wx:for="{{it.fileList}}" wx:for-item="file"></view>
        </view>
        <view class="grid col-2 grid-square flex-sub padding-lr" wx:if="{{it.fileList.length==4||it.fileList.length==2}}">
            <view class="bg-img {{isCard?'':'only-img'}}" style="background-image:url({{file}});" wx:for="{{it.fileList}}" wx:for-item="file"></view>
        </view>
        <view class="grid col-3 grid-square flex-sub padding-lr" wx:else>
            <view class="bg-img {{isCard?'':'only-img'}}" style="background-image:url({{file}});" wx:for="{{it.fileList}}" wx:for-item="file"></view>
        </view>
        <view class="text-link text-left padding">
            <text bindtap="toArticle" class="icon-link" data-url="{{it.articleLink}}" wx:if="{{it.articleLink}}">查看详情</text>
        </view>
    </item>
</card>
