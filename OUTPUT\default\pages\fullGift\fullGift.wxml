<view class="container">
    <view class="null" wx:if="{{list.length==0}}">现在暂无活动</view>
    <view bindtap="goDetail" class="item" data-id="{{it.id}}" wx:for="{{list}}" wx:for-index="idx" wx:for-item="it">
        <image src="{{it.background}}"></image>
        <view class="text">
            <view class="name">{{it.name}}</view>
            <view class="time">活动时间：{{it.startTime}} - {{it.endTime}}</view>
        </view>
    </view>
</view>
<back-home homeShow="{{homeShow}}"></back-home>
