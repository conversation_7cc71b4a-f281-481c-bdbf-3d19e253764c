/**
 * 八佰伴智慧购签到脚本
 * 环境变量：BBBZHG (格式：hyid值，多个账号用&分隔)
 * 例如：export BBBZHG="820536796"
 * 多账号：export BBBZHG="820536796&123456789"
 */

const axios = require('axios');
const crypto = require('crypto');

// 配置信息
const CONFIG = {
    // 签到接口地址
    SIGN_URL: 'https://mp-gp.springland.com.cn/sign/sign',
    // 登录接口地址  
    LOGIN_URL: 'https://mp-gp.springland.com.cn/login/getOpenidByCode',
    // 固定值
    MDID: '6021',
    // 签名相关常量（从小程序源码中提取）
    REQUEST_KEY: 'SPRINGLAND',
    REQUEST_SECRET: 'springland*&^0627@',
    // 请求头
    HEADERS: {
        'Host': 'mp-gp.springland.com.cn',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept-Encoding': 'gzip,compress,br,deflate',
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN',
        'Referer': 'https://servicewechat.com/wxa4781f15f0a821a9/642/page-frame.html'
    }
};

/**
 * SHA1加密函数（从小程序源码中提取的签名算法）
 */
function sha1(str) {
    const crypto = require('crypto');
    return crypto.createHash('sha1').update(str, 'utf8').digest('hex').toUpperCase();
}

/**
 * 生成签名（基于小程序源码中的签名算法）
 */
function generateSign(url, params) {
    const sortedKeys = Object.keys(params).sort();
    const signArray = [CONFIG.REQUEST_KEY];
    signArray.push(url);
    
    sortedKeys.forEach(key => {
        signArray.push(key + params[key]);
    });
    
    signArray.push(CONFIG.REQUEST_SECRET);
    const signString = signArray.join('');
    
    return sha1(signString);
}

/**
 * 生成Authorization token
 * 分析发现：
 * 1. 登录接口返回的token: 1fb508f5c099c83322274fe5a8ad1f46c7f5882a
 * 2. 签到请求的Authorization: 1fb508f5a4e6557df6cb447aa314544f308a7a5d
 * Authorization是对原始token进行某种加密/签名处理的结果
 */
function generateAuthorization(originalToken, hyid, requestData) {
    // 方法1: 如果有原始token，尝试基于token生成签名
    if (originalToken) {
        // 可能的签名算法：token + hyid + mdid + 其他参数
        const signData = `${originalToken}${hyid}${CONFIG.MDID}`;
        return crypto.createHash('sha1').update(signData).digest('hex');
    }

    // 方法2: 基于小程序源码中的SHA1签名算法
    // 构建签名字符串：REQUEST_KEY + URL + 排序参数 + REQUEST_SECRET
    const url = '/sign/sign';
    const sortedKeys = Object.keys(requestData).sort();
    const signArray = [CONFIG.REQUEST_KEY, `https://mp-gp.springland.com.cn${url}`];

    sortedKeys.forEach(key => {
        signArray.push(key + requestData[key]);
    });

    signArray.push(CONFIG.REQUEST_SECRET);
    const signString = signArray.join('');

    return sha1(signString);
}

/**
 * 模拟登录获取token（可选功能）
 */
async function getTokenByLogin(code, appid) {
    try {
        const loginUrl = `${CONFIG.LOGIN_URL}/${code}/${appid}`;
        const response = await axios.post(loginUrl, {}, {
            headers: {
                ...CONFIG.HEADERS,
                'Content-Type': 'application/json',
                'Content-Length': '0'
            },
            timeout: 10000
        });

        if (response.data && response.data.errCode === 0) {
            return response.data.result.token;
        }
        return null;
    } catch (error) {
        console.error('获取token失败:', error.message);
        return null;
    }
}

/**
 * 执行签到
 */
async function doSign(hyid, originalToken = null) {
    try {
        console.log(`开始为账号 ${hyid} 执行签到...`);

        // 准备签到数据
        const signData = {
            hyid: hyid,
            mdid: CONFIG.MDID
        };

        // 生成Authorization（尝试多种方法）
        let authorization;

        // 方法1: 使用原始token生成
        if (originalToken) {
            authorization = generateAuthorization(originalToken, hyid, signData);
            console.log(`使用token生成Authorization: ${authorization}`);
        } else {
            // 方法2: 使用签名算法生成
            authorization = generateAuthorization(null, hyid, signData);
            console.log(`使用签名算法生成Authorization: ${authorization}`);
        }

        // 构建请求头
        const headers = {
            ...CONFIG.HEADERS,
            'Authorization': authorization,
            'Content-Length': new URLSearchParams(signData).toString().length
        };

        console.log(`请求数据: ${JSON.stringify(signData)}`);

        // 发送签到请求
        const response = await axios.post(CONFIG.SIGN_URL, signData, {
            headers: headers,
            timeout: 10000
        });

        console.log(`账号 ${hyid} 签到响应:`, response.data);

        // 解析响应
        if (response.data && response.data.errCode === 0) {
            console.log(`✅ 账号 ${hyid} 签到成功！获得积分: ${response.data.result}`);
            return {
                success: true,
                hyid: hyid,
                points: response.data.result,
                message: '签到成功'
            };
        } else {
            console.log(`❌ 账号 ${hyid} 签到失败:`, response.data.errMsg || '未知错误');
            return {
                success: false,
                hyid: hyid,
                message: response.data.errMsg || '签到失败'
            };
        }

    } catch (error) {
        console.error(`❌ 账号 ${hyid} 签到出错:`, error.message);
        return {
            success: false,
            hyid: hyid,
            message: error.message
        };
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🚀 八佰伴智慧购签到脚本启动...');
    
    // 从环境变量获取账号信息
    const bbbzhgEnv = process.env.BBBZHG;
    
    if (!bbbzhgEnv) {
        console.error('❌ 请设置环境变量 BBBZHG');
        console.log('设置方法：export BBBZHG="你的hyid"');
        console.log('多账号：export BBBZHG="hyid1&hyid2&hyid3"');
        process.exit(1);
    }
    
    // 解析账号列表
    const hyidList = bbbzhgEnv.split('&').filter(id => id.trim());
    
    if (hyidList.length === 0) {
        console.error('❌ 未找到有效的hyid');
        process.exit(1);
    }
    
    console.log(`📝 共找到 ${hyidList.length} 个账号`);
    
    // 执行签到
    const results = [];
    for (let i = 0; i < hyidList.length; i++) {
        const hyid = hyidList[i].trim();
        console.log(`\n--- 处理第 ${i + 1} 个账号 ---`);
        
        const result = await doSign(hyid);
        results.push(result);
        
        // 延迟1秒，避免请求过快
        if (i < hyidList.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    
    // 输出总结
    console.log('\n📊 签到结果汇总:');
    console.log('='.repeat(50));
    
    let successCount = 0;
    let totalPoints = 0;
    
    results.forEach((result, index) => {
        const status = result.success ? '✅ 成功' : '❌ 失败';
        const points = result.points ? ` (+${result.points}分)` : '';
        console.log(`账号${index + 1} (${result.hyid}): ${status} ${result.message}${points}`);
        
        if (result.success) {
            successCount++;
            totalPoints += parseInt(result.points || 0);
        }
    });
    
    console.log('='.repeat(50));
    console.log(`✅ 成功: ${successCount}/${results.length}`);
    console.log(`🎯 总积分: ${totalPoints}`);
    console.log('🎉 签到完成！');
}

// 运行脚本
if (require.main === module) {
    main().catch(error => {
        console.error('脚本执行出错:', error);
        process.exit(1);
    });
}

module.exports = {
    main,
    doSign,
    generateSign,
    generateAuthorization
};
